import {
  require_baseDifference,
  require_baseIteratee
} from "./chunk-IBERZTNF.js";
import {
  require_arrayMap,
  require_baseClone,
  require_baseFlatten,
  require_baseFor,
  require_baseGet,
  require_baseRest,
  require_baseUniq,
  require_baseUnset,
  require_castPath,
  require_copyObject,
  require_flatRest,
  require_get,
  require_getAllKeysIn,
  require_identity,
  require_isArrayLikeObject,
  require_isIterateeCall,
  require_isPlainObject,
  require_isSymbol,
  require_last,
  require_merge,
  require_pick,
  require_set
} from "./chunk-H4XJCW4X.js";
import {
  require_arrayFilter,
  require_baseGetTag,
  require_baseUnary,
  require_isArray,
  require_isArrayLike,
  require_isEqual,
  require_isFunction,
  require_isObjectLike,
  require_keys,
  require_nodeUtil
} from "./chunk-5NM3XKGP.js";
import {
  require_react_dom
} from "./chunk-F34GCA6J.js";
import {
  require_prop_types
} from "./chunk-NHY3NUFE.js";
import {
  require_jsx_runtime
} from "./chunk-CRNJR6QK.js";
import {
  require_react
} from "./chunk-ZMLY2J2T.js";
import {
  __commonJS,
  __toESM
} from "./chunk-4B2QHNJT.js";

// node_modules/lodash/isString.js
var require_isString = __commonJS({
  "node_modules/lodash/isString.js"(exports, module) {
    var baseGetTag = require_baseGetTag();
    var isArray = require_isArray();
    var isObjectLike = require_isObjectLike();
    var stringTag = "[object String]";
    function isString(value) {
      return typeof value == "string" || !isArray(value) && isObjectLike(value) && baseGetTag(value) == stringTag;
    }
    module.exports = isString;
  }
});

// node_modules/lodash/without.js
var require_without = __commonJS({
  "node_modules/lodash/without.js"(exports, module) {
    var baseDifference = require_baseDifference();
    var baseRest = require_baseRest();
    var isArrayLikeObject = require_isArrayLikeObject();
    var without = baseRest(function(array2, values) {
      return isArrayLikeObject(array2) ? baseDifference(array2, values) : [];
    });
    module.exports = without;
  }
});

// node_modules/lodash/uniq.js
var require_uniq = __commonJS({
  "node_modules/lodash/uniq.js"(exports, module) {
    var baseUniq = require_baseUniq();
    function uniq(array2) {
      return array2 && array2.length ? baseUniq(array2) : [];
    }
    module.exports = uniq;
  }
});

// node_modules/lodash/uniqBy.js
var require_uniqBy = __commonJS({
  "node_modules/lodash/uniqBy.js"(exports, module) {
    var baseIteratee = require_baseIteratee();
    var baseUniq = require_baseUniq();
    function uniqBy(array2, iteratee) {
      return array2 && array2.length ? baseUniq(array2, baseIteratee(iteratee, 2)) : [];
    }
    module.exports = uniqBy;
  }
});

// node_modules/lodash/_baseForOwn.js
var require_baseForOwn = __commonJS({
  "node_modules/lodash/_baseForOwn.js"(exports, module) {
    var baseFor = require_baseFor();
    var keys = require_keys();
    function baseForOwn(object, iteratee) {
      return object && baseFor(object, iteratee, keys);
    }
    module.exports = baseForOwn;
  }
});

// node_modules/lodash/_createBaseEach.js
var require_createBaseEach = __commonJS({
  "node_modules/lodash/_createBaseEach.js"(exports, module) {
    var isArrayLike = require_isArrayLike();
    function createBaseEach(eachFunc, fromRight) {
      return function(collection, iteratee) {
        if (collection == null) {
          return collection;
        }
        if (!isArrayLike(collection)) {
          return eachFunc(collection, iteratee);
        }
        var length = collection.length, index2 = fromRight ? length : -1, iterable = Object(collection);
        while (fromRight ? index2-- : ++index2 < length) {
          if (iteratee(iterable[index2], index2, iterable) === false) {
            break;
          }
        }
        return collection;
      };
    }
    module.exports = createBaseEach;
  }
});

// node_modules/lodash/_baseEach.js
var require_baseEach = __commonJS({
  "node_modules/lodash/_baseEach.js"(exports, module) {
    var baseForOwn = require_baseForOwn();
    var createBaseEach = require_createBaseEach();
    var baseEach = createBaseEach(baseForOwn);
    module.exports = baseEach;
  }
});

// node_modules/lodash/_baseMap.js
var require_baseMap = __commonJS({
  "node_modules/lodash/_baseMap.js"(exports, module) {
    var baseEach = require_baseEach();
    var isArrayLike = require_isArrayLike();
    function baseMap(collection, iteratee) {
      var index2 = -1, result = isArrayLike(collection) ? Array(collection.length) : [];
      baseEach(collection, function(value, key, collection2) {
        result[++index2] = iteratee(value, key, collection2);
      });
      return result;
    }
    module.exports = baseMap;
  }
});

// node_modules/lodash/_baseSortBy.js
var require_baseSortBy = __commonJS({
  "node_modules/lodash/_baseSortBy.js"(exports, module) {
    function baseSortBy(array2, comparer) {
      var length = array2.length;
      array2.sort(comparer);
      while (length--) {
        array2[length] = array2[length].value;
      }
      return array2;
    }
    module.exports = baseSortBy;
  }
});

// node_modules/lodash/_compareAscending.js
var require_compareAscending = __commonJS({
  "node_modules/lodash/_compareAscending.js"(exports, module) {
    var isSymbol = require_isSymbol();
    function compareAscending(value, other) {
      if (value !== other) {
        var valIsDefined = value !== void 0, valIsNull = value === null, valIsReflexive = value === value, valIsSymbol = isSymbol(value);
        var othIsDefined = other !== void 0, othIsNull = other === null, othIsReflexive = other === other, othIsSymbol = isSymbol(other);
        if (!othIsNull && !othIsSymbol && !valIsSymbol && value > other || valIsSymbol && othIsDefined && othIsReflexive && !othIsNull && !othIsSymbol || valIsNull && othIsDefined && othIsReflexive || !valIsDefined && othIsReflexive || !valIsReflexive) {
          return 1;
        }
        if (!valIsNull && !valIsSymbol && !othIsSymbol && value < other || othIsSymbol && valIsDefined && valIsReflexive && !valIsNull && !valIsSymbol || othIsNull && valIsDefined && valIsReflexive || !othIsDefined && valIsReflexive || !othIsReflexive) {
          return -1;
        }
      }
      return 0;
    }
    module.exports = compareAscending;
  }
});

// node_modules/lodash/_compareMultiple.js
var require_compareMultiple = __commonJS({
  "node_modules/lodash/_compareMultiple.js"(exports, module) {
    var compareAscending = require_compareAscending();
    function compareMultiple(object, other, orders) {
      var index2 = -1, objCriteria = object.criteria, othCriteria = other.criteria, length = objCriteria.length, ordersLength = orders.length;
      while (++index2 < length) {
        var result = compareAscending(objCriteria[index2], othCriteria[index2]);
        if (result) {
          if (index2 >= ordersLength) {
            return result;
          }
          var order = orders[index2];
          return result * (order == "desc" ? -1 : 1);
        }
      }
      return object.index - other.index;
    }
    module.exports = compareMultiple;
  }
});

// node_modules/lodash/_baseOrderBy.js
var require_baseOrderBy = __commonJS({
  "node_modules/lodash/_baseOrderBy.js"(exports, module) {
    var arrayMap = require_arrayMap();
    var baseGet = require_baseGet();
    var baseIteratee = require_baseIteratee();
    var baseMap = require_baseMap();
    var baseSortBy = require_baseSortBy();
    var baseUnary = require_baseUnary();
    var compareMultiple = require_compareMultiple();
    var identity5 = require_identity();
    var isArray = require_isArray();
    function baseOrderBy(collection, iteratees, orders) {
      if (iteratees.length) {
        iteratees = arrayMap(iteratees, function(iteratee) {
          if (isArray(iteratee)) {
            return function(value) {
              return baseGet(value, iteratee.length === 1 ? iteratee[0] : iteratee);
            };
          }
          return iteratee;
        });
      } else {
        iteratees = [identity5];
      }
      var index2 = -1;
      iteratees = arrayMap(iteratees, baseUnary(baseIteratee));
      var result = baseMap(collection, function(value, key, collection2) {
        var criteria = arrayMap(iteratees, function(iteratee) {
          return iteratee(value);
        });
        return { "criteria": criteria, "index": ++index2, "value": value };
      });
      return baseSortBy(result, function(object, other) {
        return compareMultiple(object, other, orders);
      });
    }
    module.exports = baseOrderBy;
  }
});

// node_modules/lodash/sortBy.js
var require_sortBy = __commonJS({
  "node_modules/lodash/sortBy.js"(exports, module) {
    var baseFlatten = require_baseFlatten();
    var baseOrderBy = require_baseOrderBy();
    var baseRest = require_baseRest();
    var isIterateeCall = require_isIterateeCall();
    var sortBy = baseRest(function(collection, iteratees) {
      if (collection == null) {
        return [];
      }
      var length = iteratees.length;
      if (length > 1 && isIterateeCall(collection, iteratees[0], iteratees[1])) {
        iteratees = [];
      } else if (length > 2 && isIterateeCall(iteratees[0], iteratees[1], iteratees[2])) {
        iteratees = [iteratees[0]];
      }
      return baseOrderBy(collection, baseFlatten(iteratees, 1), []);
    });
    module.exports = sortBy;
  }
});

// node_modules/lodash/_baseIsDate.js
var require_baseIsDate = __commonJS({
  "node_modules/lodash/_baseIsDate.js"(exports, module) {
    var baseGetTag = require_baseGetTag();
    var isObjectLike = require_isObjectLike();
    var dateTag = "[object Date]";
    function baseIsDate(value) {
      return isObjectLike(value) && baseGetTag(value) == dateTag;
    }
    module.exports = baseIsDate;
  }
});

// node_modules/lodash/isDate.js
var require_isDate = __commonJS({
  "node_modules/lodash/isDate.js"(exports, module) {
    var baseIsDate = require_baseIsDate();
    var baseUnary = require_baseUnary();
    var nodeUtil = require_nodeUtil();
    var nodeIsDate = nodeUtil && nodeUtil.isDate;
    var isDate = nodeIsDate ? baseUnary(nodeIsDate) : baseIsDate;
    module.exports = isDate;
  }
});

// node_modules/lodash/_baseFilter.js
var require_baseFilter = __commonJS({
  "node_modules/lodash/_baseFilter.js"(exports, module) {
    var baseEach = require_baseEach();
    function baseFilter(collection, predicate) {
      var result = [];
      baseEach(collection, function(value, index2, collection2) {
        if (predicate(value, index2, collection2)) {
          result.push(value);
        }
      });
      return result;
    }
    module.exports = baseFilter;
  }
});

// node_modules/lodash/filter.js
var require_filter = __commonJS({
  "node_modules/lodash/filter.js"(exports, module) {
    var arrayFilter = require_arrayFilter();
    var baseFilter = require_baseFilter();
    var baseIteratee = require_baseIteratee();
    var isArray = require_isArray();
    function filter2(collection, predicate) {
      var func = isArray(collection) ? arrayFilter : baseFilter;
      return func(collection, baseIteratee(predicate, 3));
    }
    module.exports = filter2;
  }
});

// node_modules/lodash/isNumber.js
var require_isNumber = __commonJS({
  "node_modules/lodash/isNumber.js"(exports, module) {
    var baseGetTag = require_baseGetTag();
    var isObjectLike = require_isObjectLike();
    var numberTag = "[object Number]";
    function isNumber(value) {
      return typeof value == "number" || isObjectLike(value) && baseGetTag(value) == numberTag;
    }
    module.exports = isNumber;
  }
});

// node_modules/lodash/_customOmitClone.js
var require_customOmitClone = __commonJS({
  "node_modules/lodash/_customOmitClone.js"(exports, module) {
    var isPlainObject = require_isPlainObject();
    function customOmitClone(value) {
      return isPlainObject(value) ? void 0 : value;
    }
    module.exports = customOmitClone;
  }
});

// node_modules/lodash/omit.js
var require_omit = __commonJS({
  "node_modules/lodash/omit.js"(exports, module) {
    var arrayMap = require_arrayMap();
    var baseClone = require_baseClone();
    var baseUnset = require_baseUnset();
    var castPath = require_castPath();
    var copyObject = require_copyObject();
    var customOmitClone = require_customOmitClone();
    var flatRest = require_flatRest();
    var getAllKeysIn = require_getAllKeysIn();
    var CLONE_DEEP_FLAG = 1;
    var CLONE_FLAT_FLAG = 2;
    var CLONE_SYMBOLS_FLAG = 4;
    var omit = flatRest(function(object, paths) {
      var result = {};
      if (object == null) {
        return result;
      }
      var isDeep = false;
      paths = arrayMap(paths, function(path2) {
        path2 = castPath(path2, object);
        isDeep || (isDeep = path2.length > 1);
        return path2;
      });
      copyObject(object, getAllKeysIn(object), result);
      if (isDeep) {
        result = baseClone(result, CLONE_DEEP_FLAG | CLONE_FLAT_FLAG | CLONE_SYMBOLS_FLAG, customOmitClone);
      }
      var length = paths.length;
      while (length--) {
        baseUnset(result, paths[length]);
      }
      return result;
    });
    module.exports = omit;
  }
});

// node_modules/@nivo/axes/dist/nivo-axes.es.js
var t6 = __toESM(require_react());
var import_react17 = __toESM(require_react());

// node_modules/@nivo/core/dist/nivo-core.es.js
var import_react16 = __toESM(require_react());
var import_prop_types = __toESM(require_prop_types());

// node_modules/@nivo/tooltip/dist/nivo-tooltip.es.js
var import_react15 = __toESM(require_react());

// node_modules/@react-spring/rafz/dist/react-spring_rafz.modern.mjs
var updateQueue = makeQueue();
var raf = (fn2) => schedule(fn2, updateQueue);
var writeQueue = makeQueue();
raf.write = (fn2) => schedule(fn2, writeQueue);
var onStartQueue = makeQueue();
raf.onStart = (fn2) => schedule(fn2, onStartQueue);
var onFrameQueue = makeQueue();
raf.onFrame = (fn2) => schedule(fn2, onFrameQueue);
var onFinishQueue = makeQueue();
raf.onFinish = (fn2) => schedule(fn2, onFinishQueue);
var timeouts = [];
raf.setTimeout = (handler, ms) => {
  const time2 = raf.now() + ms;
  const cancel = () => {
    const i5 = timeouts.findIndex((t9) => t9.cancel == cancel);
    if (~i5)
      timeouts.splice(i5, 1);
    pendingCount -= ~i5 ? 1 : 0;
  };
  const timeout = { time: time2, handler, cancel };
  timeouts.splice(findTimeout(time2), 0, timeout);
  pendingCount += 1;
  start();
  return timeout;
};
var findTimeout = (time2) => ~(~timeouts.findIndex((t9) => t9.time > time2) || ~timeouts.length);
raf.cancel = (fn2) => {
  onStartQueue.delete(fn2);
  onFrameQueue.delete(fn2);
  onFinishQueue.delete(fn2);
  updateQueue.delete(fn2);
  writeQueue.delete(fn2);
};
raf.sync = (fn2) => {
  sync = true;
  raf.batchedUpdates(fn2);
  sync = false;
};
raf.throttle = (fn2) => {
  let lastArgs;
  function queuedFn() {
    try {
      fn2(...lastArgs);
    } finally {
      lastArgs = null;
    }
  }
  function throttled(...args) {
    lastArgs = args;
    raf.onStart(queuedFn);
  }
  throttled.handler = fn2;
  throttled.cancel = () => {
    onStartQueue.delete(queuedFn);
    lastArgs = null;
  };
  return throttled;
};
var nativeRaf = typeof window != "undefined" ? window.requestAnimationFrame : (
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  () => {
  }
);
raf.use = (impl) => nativeRaf = impl;
raf.now = typeof performance != "undefined" ? () => performance.now() : Date.now;
raf.batchedUpdates = (fn2) => fn2();
raf.catch = console.error;
raf.frameLoop = "always";
raf.advance = () => {
  if (raf.frameLoop !== "demand") {
    console.warn(
      "Cannot call the manual advancement of rafz whilst frameLoop is not set as demand"
    );
  } else {
    update();
  }
};
var ts = -1;
var pendingCount = 0;
var sync = false;
function schedule(fn2, queue) {
  if (sync) {
    queue.delete(fn2);
    fn2(0);
  } else {
    queue.add(fn2);
    start();
  }
}
function start() {
  if (ts < 0) {
    ts = 0;
    if (raf.frameLoop !== "demand") {
      nativeRaf(loop);
    }
  }
}
function stop() {
  ts = -1;
}
function loop() {
  if (~ts) {
    nativeRaf(loop);
    raf.batchedUpdates(update);
  }
}
function update() {
  const prevTs = ts;
  ts = raf.now();
  const count2 = findTimeout(ts);
  if (count2) {
    eachSafely(timeouts.splice(0, count2), (t9) => t9.handler());
    pendingCount -= count2;
  }
  if (!pendingCount) {
    stop();
    return;
  }
  onStartQueue.flush();
  updateQueue.flush(prevTs ? Math.min(64, ts - prevTs) : 16.667);
  onFrameQueue.flush();
  writeQueue.flush();
  onFinishQueue.flush();
}
function makeQueue() {
  let next = /* @__PURE__ */ new Set();
  let current = next;
  return {
    add(fn2) {
      pendingCount += current == next && !next.has(fn2) ? 1 : 0;
      next.add(fn2);
    },
    delete(fn2) {
      pendingCount -= current == next && next.has(fn2) ? 1 : 0;
      return next.delete(fn2);
    },
    flush(arg) {
      if (current.size) {
        next = /* @__PURE__ */ new Set();
        pendingCount -= current.size;
        eachSafely(current, (fn2) => fn2(arg) && next.add(fn2));
        pendingCount += next.size;
        current = next;
      }
    }
  };
}
function eachSafely(values, each2) {
  values.forEach((value) => {
    try {
      each2(value);
    } catch (e11) {
      raf.catch(e11);
    }
  });
}

// node_modules/@react-spring/shared/dist/react-spring_shared.modern.mjs
var import_react = __toESM(require_react(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_react3 = __toESM(require_react(), 1);
var import_react4 = __toESM(require_react(), 1);
var import_react5 = __toESM(require_react(), 1);
var import_react6 = __toESM(require_react(), 1);
var import_react7 = __toESM(require_react(), 1);
var import_react8 = __toESM(require_react(), 1);
var __defProp = Object.defineProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var globals_exports = {};
__export(globals_exports, {
  assign: () => assign,
  colors: () => colors,
  createStringInterpolator: () => createStringInterpolator,
  skipAnimation: () => skipAnimation,
  to: () => to,
  willAdvance: () => willAdvance
});
function noop() {
}
var defineHidden = (obj, key, value) => Object.defineProperty(obj, key, { value, writable: true, configurable: true });
var is = {
  arr: Array.isArray,
  obj: (a5) => !!a5 && a5.constructor.name === "Object",
  fun: (a5) => typeof a5 === "function",
  str: (a5) => typeof a5 === "string",
  num: (a5) => typeof a5 === "number",
  und: (a5) => a5 === void 0
};
function isEqual(a5, b5) {
  if (is.arr(a5)) {
    if (!is.arr(b5) || a5.length !== b5.length)
      return false;
    for (let i5 = 0; i5 < a5.length; i5++) {
      if (a5[i5] !== b5[i5])
        return false;
    }
    return true;
  }
  return a5 === b5;
}
var each = (obj, fn2) => obj.forEach(fn2);
function eachProp(obj, fn2, ctx2) {
  if (is.arr(obj)) {
    for (let i5 = 0; i5 < obj.length; i5++) {
      fn2.call(ctx2, obj[i5], `${i5}`);
    }
    return;
  }
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      fn2.call(ctx2, obj[key], key);
    }
  }
}
var toArray = (a5) => is.und(a5) ? [] : is.arr(a5) ? a5 : [a5];
function flush(queue, iterator) {
  if (queue.size) {
    const items = Array.from(queue);
    queue.clear();
    each(items, iterator);
  }
}
var flushCalls = (queue, ...args) => flush(queue, (fn2) => fn2(...args));
var isSSR = () => typeof window === "undefined" || !window.navigator || /ServerSideRendering|^Deno\//.test(window.navigator.userAgent);
var createStringInterpolator;
var to;
var colors = null;
var skipAnimation = false;
var willAdvance = noop;
var assign = (globals) => {
  if (globals.to)
    to = globals.to;
  if (globals.now)
    raf.now = globals.now;
  if (globals.colors !== void 0)
    colors = globals.colors;
  if (globals.skipAnimation != null)
    skipAnimation = globals.skipAnimation;
  if (globals.createStringInterpolator)
    createStringInterpolator = globals.createStringInterpolator;
  if (globals.requestAnimationFrame)
    raf.use(globals.requestAnimationFrame);
  if (globals.batchedUpdates)
    raf.batchedUpdates = globals.batchedUpdates;
  if (globals.willAdvance)
    willAdvance = globals.willAdvance;
  if (globals.frameLoop)
    raf.frameLoop = globals.frameLoop;
};
var startQueue = /* @__PURE__ */ new Set();
var currentFrame = [];
var prevFrame = [];
var priority = 0;
var frameLoop = {
  get idle() {
    return !startQueue.size && !currentFrame.length;
  },
  /** Advance the given animation on every frame until idle. */
  start(animation) {
    if (priority > animation.priority) {
      startQueue.add(animation);
      raf.onStart(flushStartQueue);
    } else {
      startSafely(animation);
      raf(advance);
    }
  },
  /** Advance all animations by the given time. */
  advance,
  /** Call this when an animation's priority changes. */
  sort(animation) {
    if (priority) {
      raf.onFrame(() => frameLoop.sort(animation));
    } else {
      const prevIndex = currentFrame.indexOf(animation);
      if (~prevIndex) {
        currentFrame.splice(prevIndex, 1);
        startUnsafely(animation);
      }
    }
  },
  /**
   * Clear all animations. For testing purposes.
   *
   * ☠️ Never call this from within the frameloop.
   */
  clear() {
    currentFrame = [];
    startQueue.clear();
  }
};
function flushStartQueue() {
  startQueue.forEach(startSafely);
  startQueue.clear();
  raf(advance);
}
function startSafely(animation) {
  if (!currentFrame.includes(animation))
    startUnsafely(animation);
}
function startUnsafely(animation) {
  currentFrame.splice(
    findIndex(currentFrame, (other) => other.priority > animation.priority),
    0,
    animation
  );
}
function advance(dt2) {
  const nextFrame = prevFrame;
  for (let i5 = 0; i5 < currentFrame.length; i5++) {
    const animation = currentFrame[i5];
    priority = animation.priority;
    if (!animation.idle) {
      willAdvance(animation);
      animation.advance(dt2);
      if (!animation.idle) {
        nextFrame.push(animation);
      }
    }
  }
  priority = 0;
  prevFrame = currentFrame;
  prevFrame.length = 0;
  currentFrame = nextFrame;
  return currentFrame.length > 0;
}
function findIndex(arr, test) {
  const index2 = arr.findIndex(test);
  return index2 < 0 ? arr.length : index2;
}
var clamp = (min3, max3, v6) => Math.min(Math.max(v6, min3), max3);
var colors2 = {
  transparent: 0,
  aliceblue: 4042850303,
  antiquewhite: 4209760255,
  aqua: 16777215,
  aquamarine: 2147472639,
  azure: 4043309055,
  beige: 4126530815,
  bisque: 4293182719,
  black: 255,
  blanchedalmond: 4293643775,
  blue: 65535,
  blueviolet: 2318131967,
  brown: 2771004159,
  burlywood: 3736635391,
  burntsienna: 3934150143,
  cadetblue: 1604231423,
  chartreuse: 2147418367,
  chocolate: 3530104575,
  coral: 4286533887,
  cornflowerblue: 1687547391,
  cornsilk: 4294499583,
  crimson: 3692313855,
  cyan: 16777215,
  darkblue: 35839,
  darkcyan: 9145343,
  darkgoldenrod: 3095792639,
  darkgray: 2846468607,
  darkgreen: 6553855,
  darkgrey: 2846468607,
  darkkhaki: 3182914559,
  darkmagenta: 2332068863,
  darkolivegreen: 1433087999,
  darkorange: 4287365375,
  darkorchid: 2570243327,
  darkred: 2332033279,
  darksalmon: 3918953215,
  darkseagreen: 2411499519,
  darkslateblue: 1211993087,
  darkslategray: 793726975,
  darkslategrey: 793726975,
  darkturquoise: 13554175,
  darkviolet: 2483082239,
  deeppink: 4279538687,
  deepskyblue: 12582911,
  dimgray: 1768516095,
  dimgrey: 1768516095,
  dodgerblue: 512819199,
  firebrick: 2988581631,
  floralwhite: 4294635775,
  forestgreen: 579543807,
  fuchsia: 4278255615,
  gainsboro: 3705462015,
  ghostwhite: 4177068031,
  gold: 4292280575,
  goldenrod: 3668254975,
  gray: 2155905279,
  green: 8388863,
  greenyellow: 2919182335,
  grey: 2155905279,
  honeydew: 4043305215,
  hotpink: 4285117695,
  indianred: 3445382399,
  indigo: 1258324735,
  ivory: 4294963455,
  khaki: 4041641215,
  lavender: 3873897215,
  lavenderblush: 4293981695,
  lawngreen: 2096890111,
  lemonchiffon: 4294626815,
  lightblue: 2916673279,
  lightcoral: 4034953471,
  lightcyan: 3774873599,
  lightgoldenrodyellow: 4210742015,
  lightgray: 3553874943,
  lightgreen: 2431553791,
  lightgrey: 3553874943,
  lightpink: 4290167295,
  lightsalmon: 4288707327,
  lightseagreen: 548580095,
  lightskyblue: 2278488831,
  lightslategray: 2005441023,
  lightslategrey: 2005441023,
  lightsteelblue: 2965692159,
  lightyellow: 4294959359,
  lime: 16711935,
  limegreen: 852308735,
  linen: 4210091775,
  magenta: 4278255615,
  maroon: 2147483903,
  mediumaquamarine: 1724754687,
  mediumblue: 52735,
  mediumorchid: 3126187007,
  mediumpurple: 2473647103,
  mediumseagreen: 1018393087,
  mediumslateblue: 2070474495,
  mediumspringgreen: 16423679,
  mediumturquoise: 1221709055,
  mediumvioletred: 3340076543,
  midnightblue: 421097727,
  mintcream: 4127193855,
  mistyrose: 4293190143,
  moccasin: 4293178879,
  navajowhite: 4292783615,
  navy: 33023,
  oldlace: 4260751103,
  olive: 2155872511,
  olivedrab: 1804477439,
  orange: 4289003775,
  orangered: 4282712319,
  orchid: 3664828159,
  palegoldenrod: 4008225535,
  palegreen: 2566625535,
  paleturquoise: 2951671551,
  palevioletred: 3681588223,
  papayawhip: 4293907967,
  peachpuff: 4292524543,
  peru: 3448061951,
  pink: 4290825215,
  plum: 3718307327,
  powderblue: 2967529215,
  purple: 2147516671,
  rebeccapurple: 1714657791,
  red: 4278190335,
  rosybrown: 3163525119,
  royalblue: 1097458175,
  saddlebrown: 2336560127,
  salmon: 4202722047,
  sandybrown: 4104413439,
  seagreen: 780883967,
  seashell: 4294307583,
  sienna: 2689740287,
  silver: 3233857791,
  skyblue: 2278484991,
  slateblue: 1784335871,
  slategray: 1887473919,
  slategrey: 1887473919,
  snow: 4294638335,
  springgreen: 16744447,
  steelblue: 1182971135,
  tan: 3535047935,
  teal: 8421631,
  thistle: 3636451583,
  tomato: 4284696575,
  turquoise: 1088475391,
  violet: 4001558271,
  wheat: 4125012991,
  white: 4294967295,
  whitesmoke: 4126537215,
  yellow: 4294902015,
  yellowgreen: 2597139199
};
var NUMBER = "[-+]?\\d*\\.?\\d+";
var PERCENTAGE = NUMBER + "%";
function call(...parts) {
  return "\\(\\s*(" + parts.join(")\\s*,\\s*(") + ")\\s*\\)";
}
var rgb = new RegExp("rgb" + call(NUMBER, NUMBER, NUMBER));
var rgba = new RegExp("rgba" + call(NUMBER, NUMBER, NUMBER, NUMBER));
var hsl = new RegExp("hsl" + call(NUMBER, PERCENTAGE, PERCENTAGE));
var hsla = new RegExp(
  "hsla" + call(NUMBER, PERCENTAGE, PERCENTAGE, NUMBER)
);
var hex3 = /^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/;
var hex4 = /^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/;
var hex6 = /^#([0-9a-fA-F]{6})$/;
var hex8 = /^#([0-9a-fA-F]{8})$/;
function normalizeColor(color2) {
  let match;
  if (typeof color2 === "number") {
    return color2 >>> 0 === color2 && color2 >= 0 && color2 <= 4294967295 ? color2 : null;
  }
  if (match = hex6.exec(color2))
    return parseInt(match[1] + "ff", 16) >>> 0;
  if (colors && colors[color2] !== void 0) {
    return colors[color2];
  }
  if (match = rgb.exec(color2)) {
    return (parse255(match[1]) << 24 | // r
    parse255(match[2]) << 16 | // g
    parse255(match[3]) << 8 | // b
    255) >>> // a
    0;
  }
  if (match = rgba.exec(color2)) {
    return (parse255(match[1]) << 24 | // r
    parse255(match[2]) << 16 | // g
    parse255(match[3]) << 8 | // b
    parse1(match[4])) >>> // a
    0;
  }
  if (match = hex3.exec(color2)) {
    return parseInt(
      match[1] + match[1] + // r
      match[2] + match[2] + // g
      match[3] + match[3] + // b
      "ff",
      // a
      16
    ) >>> 0;
  }
  if (match = hex8.exec(color2))
    return parseInt(match[1], 16) >>> 0;
  if (match = hex4.exec(color2)) {
    return parseInt(
      match[1] + match[1] + // r
      match[2] + match[2] + // g
      match[3] + match[3] + // b
      match[4] + match[4],
      // a
      16
    ) >>> 0;
  }
  if (match = hsl.exec(color2)) {
    return (hslToRgb(
      parse360(match[1]),
      // h
      parsePercentage(match[2]),
      // s
      parsePercentage(match[3])
      // l
    ) | 255) >>> // a
    0;
  }
  if (match = hsla.exec(color2)) {
    return (hslToRgb(
      parse360(match[1]),
      // h
      parsePercentage(match[2]),
      // s
      parsePercentage(match[3])
      // l
    ) | parse1(match[4])) >>> // a
    0;
  }
  return null;
}
function hue2rgb(p5, q4, t9) {
  if (t9 < 0)
    t9 += 1;
  if (t9 > 1)
    t9 -= 1;
  if (t9 < 1 / 6)
    return p5 + (q4 - p5) * 6 * t9;
  if (t9 < 1 / 2)
    return q4;
  if (t9 < 2 / 3)
    return p5 + (q4 - p5) * (2 / 3 - t9) * 6;
  return p5;
}
function hslToRgb(h2, s4, l3) {
  const q4 = l3 < 0.5 ? l3 * (1 + s4) : l3 + s4 - l3 * s4;
  const p5 = 2 * l3 - q4;
  const r5 = hue2rgb(p5, q4, h2 + 1 / 3);
  const g5 = hue2rgb(p5, q4, h2);
  const b5 = hue2rgb(p5, q4, h2 - 1 / 3);
  return Math.round(r5 * 255) << 24 | Math.round(g5 * 255) << 16 | Math.round(b5 * 255) << 8;
}
function parse255(str) {
  const int = parseInt(str, 10);
  if (int < 0)
    return 0;
  if (int > 255)
    return 255;
  return int;
}
function parse360(str) {
  const int = parseFloat(str);
  return (int % 360 + 360) % 360 / 360;
}
function parse1(str) {
  const num = parseFloat(str);
  if (num < 0)
    return 0;
  if (num > 1)
    return 255;
  return Math.round(num * 255);
}
function parsePercentage(str) {
  const int = parseFloat(str);
  if (int < 0)
    return 0;
  if (int > 100)
    return 1;
  return int / 100;
}
function colorToRgba(input) {
  let int32Color = normalizeColor(input);
  if (int32Color === null)
    return input;
  int32Color = int32Color || 0;
  const r5 = (int32Color & 4278190080) >>> 24;
  const g5 = (int32Color & 16711680) >>> 16;
  const b5 = (int32Color & 65280) >>> 8;
  const a5 = (int32Color & 255) / 255;
  return `rgba(${r5}, ${g5}, ${b5}, ${a5})`;
}
var createInterpolator = (range2, output, extrapolate) => {
  if (is.fun(range2)) {
    return range2;
  }
  if (is.arr(range2)) {
    return createInterpolator({
      range: range2,
      output,
      extrapolate
    });
  }
  if (is.str(range2.output[0])) {
    return createStringInterpolator(range2);
  }
  const config2 = range2;
  const outputRange = config2.output;
  const inputRange = config2.range || [0, 1];
  const extrapolateLeft = config2.extrapolateLeft || config2.extrapolate || "extend";
  const extrapolateRight = config2.extrapolateRight || config2.extrapolate || "extend";
  const easing = config2.easing || ((t9) => t9);
  return (input) => {
    const range22 = findRange(input, inputRange);
    return interpolate(
      input,
      inputRange[range22],
      inputRange[range22 + 1],
      outputRange[range22],
      outputRange[range22 + 1],
      easing,
      extrapolateLeft,
      extrapolateRight,
      config2.map
    );
  };
};
function interpolate(input, inputMin, inputMax, outputMin, outputMax, easing, extrapolateLeft, extrapolateRight, map4) {
  let result = map4 ? map4(input) : input;
  if (result < inputMin) {
    if (extrapolateLeft === "identity")
      return result;
    else if (extrapolateLeft === "clamp")
      result = inputMin;
  }
  if (result > inputMax) {
    if (extrapolateRight === "identity")
      return result;
    else if (extrapolateRight === "clamp")
      result = inputMax;
  }
  if (outputMin === outputMax)
    return outputMin;
  if (inputMin === inputMax)
    return input <= inputMin ? outputMin : outputMax;
  if (inputMin === -Infinity)
    result = -result;
  else if (inputMax === Infinity)
    result = result - inputMin;
  else
    result = (result - inputMin) / (inputMax - inputMin);
  result = easing(result);
  if (outputMin === -Infinity)
    result = -result;
  else if (outputMax === Infinity)
    result = result + outputMin;
  else
    result = result * (outputMax - outputMin) + outputMin;
  return result;
}
function findRange(input, inputRange) {
  for (var i5 = 1; i5 < inputRange.length - 1; ++i5)
    if (inputRange[i5] >= input)
      break;
  return i5 - 1;
}
var steps = (steps2, direction = "end") => (progress2) => {
  progress2 = direction === "end" ? Math.min(progress2, 0.999) : Math.max(progress2, 1e-3);
  const expanded = progress2 * steps2;
  const rounded = direction === "end" ? Math.floor(expanded) : Math.ceil(expanded);
  return clamp(0, 1, rounded / steps2);
};
var c1 = 1.70158;
var c2 = c1 * 1.525;
var c3 = c1 + 1;
var c4 = 2 * Math.PI / 3;
var c5 = 2 * Math.PI / 4.5;
var bounceOut = (x5) => {
  const n1 = 7.5625;
  const d1 = 2.75;
  if (x5 < 1 / d1) {
    return n1 * x5 * x5;
  } else if (x5 < 2 / d1) {
    return n1 * (x5 -= 1.5 / d1) * x5 + 0.75;
  } else if (x5 < 2.5 / d1) {
    return n1 * (x5 -= 2.25 / d1) * x5 + 0.9375;
  } else {
    return n1 * (x5 -= 2.625 / d1) * x5 + 0.984375;
  }
};
var easings = {
  linear: (x5) => x5,
  easeInQuad: (x5) => x5 * x5,
  easeOutQuad: (x5) => 1 - (1 - x5) * (1 - x5),
  easeInOutQuad: (x5) => x5 < 0.5 ? 2 * x5 * x5 : 1 - Math.pow(-2 * x5 + 2, 2) / 2,
  easeInCubic: (x5) => x5 * x5 * x5,
  easeOutCubic: (x5) => 1 - Math.pow(1 - x5, 3),
  easeInOutCubic: (x5) => x5 < 0.5 ? 4 * x5 * x5 * x5 : 1 - Math.pow(-2 * x5 + 2, 3) / 2,
  easeInQuart: (x5) => x5 * x5 * x5 * x5,
  easeOutQuart: (x5) => 1 - Math.pow(1 - x5, 4),
  easeInOutQuart: (x5) => x5 < 0.5 ? 8 * x5 * x5 * x5 * x5 : 1 - Math.pow(-2 * x5 + 2, 4) / 2,
  easeInQuint: (x5) => x5 * x5 * x5 * x5 * x5,
  easeOutQuint: (x5) => 1 - Math.pow(1 - x5, 5),
  easeInOutQuint: (x5) => x5 < 0.5 ? 16 * x5 * x5 * x5 * x5 * x5 : 1 - Math.pow(-2 * x5 + 2, 5) / 2,
  easeInSine: (x5) => 1 - Math.cos(x5 * Math.PI / 2),
  easeOutSine: (x5) => Math.sin(x5 * Math.PI / 2),
  easeInOutSine: (x5) => -(Math.cos(Math.PI * x5) - 1) / 2,
  easeInExpo: (x5) => x5 === 0 ? 0 : Math.pow(2, 10 * x5 - 10),
  easeOutExpo: (x5) => x5 === 1 ? 1 : 1 - Math.pow(2, -10 * x5),
  easeInOutExpo: (x5) => x5 === 0 ? 0 : x5 === 1 ? 1 : x5 < 0.5 ? Math.pow(2, 20 * x5 - 10) / 2 : (2 - Math.pow(2, -20 * x5 + 10)) / 2,
  easeInCirc: (x5) => 1 - Math.sqrt(1 - Math.pow(x5, 2)),
  easeOutCirc: (x5) => Math.sqrt(1 - Math.pow(x5 - 1, 2)),
  easeInOutCirc: (x5) => x5 < 0.5 ? (1 - Math.sqrt(1 - Math.pow(2 * x5, 2))) / 2 : (Math.sqrt(1 - Math.pow(-2 * x5 + 2, 2)) + 1) / 2,
  easeInBack: (x5) => c3 * x5 * x5 * x5 - c1 * x5 * x5,
  easeOutBack: (x5) => 1 + c3 * Math.pow(x5 - 1, 3) + c1 * Math.pow(x5 - 1, 2),
  easeInOutBack: (x5) => x5 < 0.5 ? Math.pow(2 * x5, 2) * ((c2 + 1) * 2 * x5 - c2) / 2 : (Math.pow(2 * x5 - 2, 2) * ((c2 + 1) * (x5 * 2 - 2) + c2) + 2) / 2,
  easeInElastic: (x5) => x5 === 0 ? 0 : x5 === 1 ? 1 : -Math.pow(2, 10 * x5 - 10) * Math.sin((x5 * 10 - 10.75) * c4),
  easeOutElastic: (x5) => x5 === 0 ? 0 : x5 === 1 ? 1 : Math.pow(2, -10 * x5) * Math.sin((x5 * 10 - 0.75) * c4) + 1,
  easeInOutElastic: (x5) => x5 === 0 ? 0 : x5 === 1 ? 1 : x5 < 0.5 ? -(Math.pow(2, 20 * x5 - 10) * Math.sin((20 * x5 - 11.125) * c5)) / 2 : Math.pow(2, -20 * x5 + 10) * Math.sin((20 * x5 - 11.125) * c5) / 2 + 1,
  easeInBounce: (x5) => 1 - bounceOut(1 - x5),
  easeOutBounce: bounceOut,
  easeInOutBounce: (x5) => x5 < 0.5 ? (1 - bounceOut(1 - 2 * x5)) / 2 : (1 + bounceOut(2 * x5 - 1)) / 2,
  steps
};
var $get = Symbol.for("FluidValue.get");
var $observers = Symbol.for("FluidValue.observers");
var hasFluidValue = (arg) => Boolean(arg && arg[$get]);
var getFluidValue = (arg) => arg && arg[$get] ? arg[$get]() : arg;
var getFluidObservers = (target) => target[$observers] || null;
function callFluidObserver(observer2, event) {
  if (observer2.eventObserved) {
    observer2.eventObserved(event);
  } else {
    observer2(event);
  }
}
function callFluidObservers(target, event) {
  const observers = target[$observers];
  if (observers) {
    observers.forEach((observer2) => {
      callFluidObserver(observer2, event);
    });
  }
}
var FluidValue = class {
  constructor(get) {
    if (!get && !(get = this.get)) {
      throw Error("Unknown getter");
    }
    setFluidGetter(this, get);
  }
};
var setFluidGetter = (target, get) => setHidden(target, $get, get);
function addFluidObserver(target, observer2) {
  if (target[$get]) {
    let observers = target[$observers];
    if (!observers) {
      setHidden(target, $observers, observers = /* @__PURE__ */ new Set());
    }
    if (!observers.has(observer2)) {
      observers.add(observer2);
      if (target.observerAdded) {
        target.observerAdded(observers.size, observer2);
      }
    }
  }
  return observer2;
}
function removeFluidObserver(target, observer2) {
  const observers = target[$observers];
  if (observers && observers.has(observer2)) {
    const count2 = observers.size - 1;
    if (count2) {
      observers.delete(observer2);
    } else {
      target[$observers] = null;
    }
    if (target.observerRemoved) {
      target.observerRemoved(count2, observer2);
    }
  }
}
var setHidden = (target, key, value) => Object.defineProperty(target, key, {
  value,
  writable: true,
  configurable: true
});
var numberRegex = /[+\-]?(?:0|[1-9]\d*)(?:\.\d*)?(?:[eE][+\-]?\d+)?/g;
var colorRegex = /(#(?:[0-9a-f]{2}){2,4}|(#[0-9a-f]{3})|(rgb|hsl)a?\((-?\d+%?[,\s]+){2,3}\s*[\d\.]+%?\))/gi;
var unitRegex = new RegExp(`(${numberRegex.source})(%|[a-z]+)`, "i");
var rgbaRegex = /rgba\(([0-9\.-]+), ([0-9\.-]+), ([0-9\.-]+), ([0-9\.-]+)\)/gi;
var cssVariableRegex = /var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;
var variableToRgba = (input) => {
  const [token, fallback] = parseCSSVariable(input);
  if (!token || isSSR()) {
    return input;
  }
  const value = window.getComputedStyle(document.documentElement).getPropertyValue(token);
  if (value) {
    return value.trim();
  } else if (fallback && fallback.startsWith("--")) {
    const value2 = window.getComputedStyle(document.documentElement).getPropertyValue(fallback);
    if (value2) {
      return value2;
    } else {
      return input;
    }
  } else if (fallback && cssVariableRegex.test(fallback)) {
    return variableToRgba(fallback);
  } else if (fallback) {
    return fallback;
  }
  return input;
};
var parseCSSVariable = (current) => {
  const match = cssVariableRegex.exec(current);
  if (!match)
    return [,];
  const [, token, fallback] = match;
  return [token, fallback];
};
var namedColorRegex;
var rgbaRound = (_3, p1, p22, p32, p42) => `rgba(${Math.round(p1)}, ${Math.round(p22)}, ${Math.round(p32)}, ${p42})`;
var createStringInterpolator2 = (config2) => {
  if (!namedColorRegex)
    namedColorRegex = colors ? (
      // match color names, ignore partial matches
      new RegExp(`(${Object.keys(colors).join("|")})(?!\\w)`, "g")
    ) : (
      // never match
      /^\b$/
    );
  const output = config2.output.map((value) => {
    return getFluidValue(value).replace(cssVariableRegex, variableToRgba).replace(colorRegex, colorToRgba).replace(namedColorRegex, colorToRgba);
  });
  const keyframes = output.map((value) => value.match(numberRegex).map(Number));
  const outputRanges = keyframes[0].map(
    (_3, i5) => keyframes.map((values) => {
      if (!(i5 in values)) {
        throw Error('The arity of each "output" value must be equal');
      }
      return values[i5];
    })
  );
  const interpolators = outputRanges.map(
    (output2) => createInterpolator({ ...config2, output: output2 })
  );
  return (input) => {
    var _a;
    const missingUnit = !unitRegex.test(output[0]) && ((_a = output.find((value) => unitRegex.test(value))) == null ? void 0 : _a.replace(numberRegex, ""));
    let i5 = 0;
    return output[0].replace(
      numberRegex,
      () => `${interpolators[i5++](input)}${missingUnit || ""}`
    ).replace(rgbaRegex, rgbaRound);
  };
};
var prefix = "react-spring: ";
var once = (fn2) => {
  const func = fn2;
  let called = false;
  if (typeof func != "function") {
    throw new TypeError(`${prefix}once requires a function parameter`);
  }
  return (...args) => {
    if (!called) {
      func(...args);
      called = true;
    }
  };
};
var warnInterpolate = once(console.warn);
function deprecateInterpolate() {
  warnInterpolate(
    `${prefix}The "interpolate" function is deprecated in v9 (use "to" instead)`
  );
}
var warnDirectCall = once(console.warn);
function deprecateDirectCall() {
  warnDirectCall(
    `${prefix}Directly calling start instead of using the api object is deprecated in v9 (use ".start" instead), this will be removed in later 0.X.0 versions`
  );
}
function isAnimatedString(value) {
  return is.str(value) && (value[0] == "#" || /\d/.test(value) || // Do not identify a CSS variable as an AnimatedString if its SSR
  !isSSR() && cssVariableRegex.test(value) || value in (colors || {}));
}
var useIsomorphicLayoutEffect = isSSR() ? import_react4.useEffect : import_react4.useLayoutEffect;
var useIsMounted = () => {
  const isMounted = (0, import_react3.useRef)(false);
  useIsomorphicLayoutEffect(() => {
    isMounted.current = true;
    return () => {
      isMounted.current = false;
    };
  }, []);
  return isMounted;
};
function useForceUpdate() {
  const update3 = (0, import_react2.useState)()[1];
  const isMounted = useIsMounted();
  return () => {
    if (isMounted.current) {
      update3(Math.random());
    }
  };
}
function useMemoOne(getResult, inputs) {
  const [initial] = (0, import_react5.useState)(
    () => ({
      inputs,
      result: getResult()
    })
  );
  const committed = (0, import_react5.useRef)();
  const prevCache = committed.current;
  let cache = prevCache;
  if (cache) {
    const useCache = Boolean(
      inputs && cache.inputs && areInputsEqual(inputs, cache.inputs)
    );
    if (!useCache) {
      cache = {
        inputs,
        result: getResult()
      };
    }
  } else {
    cache = initial;
  }
  (0, import_react5.useEffect)(() => {
    committed.current = cache;
    if (prevCache == initial) {
      initial.inputs = initial.result = void 0;
    }
  }, [cache]);
  return cache.result;
}
function areInputsEqual(next, prev) {
  if (next.length !== prev.length) {
    return false;
  }
  for (let i5 = 0; i5 < next.length; i5++) {
    if (next[i5] !== prev[i5]) {
      return false;
    }
  }
  return true;
}
var useOnce = (effect) => (0, import_react6.useEffect)(effect, emptyDeps);
var emptyDeps = [];
function usePrev(value) {
  const prevRef = (0, import_react7.useRef)();
  (0, import_react7.useEffect)(() => {
    prevRef.current = value;
  });
  return prevRef.current;
}

// node_modules/@react-spring/core/dist/react-spring_core.modern.mjs
var import_react10 = __toESM(require_react(), 1);

// node_modules/@react-spring/animated/dist/react-spring_animated.modern.mjs
var React = __toESM(require_react(), 1);
var import_react9 = __toESM(require_react(), 1);
var $node = Symbol.for("Animated:node");
var isAnimated = (value) => !!value && value[$node] === value;
var getAnimated = (owner) => owner && owner[$node];
var setAnimated = (owner, node) => defineHidden(owner, $node, node);
var getPayload = (owner) => owner && owner[$node] && owner[$node].getPayload();
var Animated = class {
  constructor() {
    setAnimated(this, this);
  }
  /** Get every `AnimatedValue` used by this node. */
  getPayload() {
    return this.payload || [];
  }
};
var AnimatedValue = class extends Animated {
  constructor(_value) {
    super();
    this._value = _value;
    this.done = true;
    this.durationProgress = 0;
    if (is.num(this._value)) {
      this.lastPosition = this._value;
    }
  }
  /** @internal */
  static create(value) {
    return new AnimatedValue(value);
  }
  getPayload() {
    return [this];
  }
  getValue() {
    return this._value;
  }
  setValue(value, step) {
    if (is.num(value)) {
      this.lastPosition = value;
      if (step) {
        value = Math.round(value / step) * step;
        if (this.done) {
          this.lastPosition = value;
        }
      }
    }
    if (this._value === value) {
      return false;
    }
    this._value = value;
    return true;
  }
  reset() {
    const { done } = this;
    this.done = false;
    if (is.num(this._value)) {
      this.elapsedTime = 0;
      this.durationProgress = 0;
      this.lastPosition = this._value;
      if (done)
        this.lastVelocity = null;
      this.v0 = null;
    }
  }
};
var AnimatedString = class extends AnimatedValue {
  constructor(value) {
    super(0);
    this._string = null;
    this._toString = createInterpolator({
      output: [value, value]
    });
  }
  /** @internal */
  static create(value) {
    return new AnimatedString(value);
  }
  getValue() {
    const value = this._string;
    return value == null ? this._string = this._toString(this._value) : value;
  }
  setValue(value) {
    if (is.str(value)) {
      if (value == this._string) {
        return false;
      }
      this._string = value;
      this._value = 1;
    } else if (super.setValue(value)) {
      this._string = null;
    } else {
      return false;
    }
    return true;
  }
  reset(goal) {
    if (goal) {
      this._toString = createInterpolator({
        output: [this.getValue(), goal]
      });
    }
    this._value = 0;
    super.reset();
  }
};
var TreeContext = { dependencies: null };
var AnimatedObject = class extends Animated {
  constructor(source) {
    super();
    this.source = source;
    this.setValue(source);
  }
  getValue(animated2) {
    const values = {};
    eachProp(this.source, (source, key) => {
      if (isAnimated(source)) {
        values[key] = source.getValue(animated2);
      } else if (hasFluidValue(source)) {
        values[key] = getFluidValue(source);
      } else if (!animated2) {
        values[key] = source;
      }
    });
    return values;
  }
  /** Replace the raw object data */
  setValue(source) {
    this.source = source;
    this.payload = this._makePayload(source);
  }
  reset() {
    if (this.payload) {
      each(this.payload, (node) => node.reset());
    }
  }
  /** Create a payload set. */
  _makePayload(source) {
    if (source) {
      const payload = /* @__PURE__ */ new Set();
      eachProp(source, this._addToPayload, payload);
      return Array.from(payload);
    }
  }
  /** Add to a payload set. */
  _addToPayload(source) {
    if (TreeContext.dependencies && hasFluidValue(source)) {
      TreeContext.dependencies.add(source);
    }
    const payload = getPayload(source);
    if (payload) {
      each(payload, (node) => this.add(node));
    }
  }
};
var AnimatedArray = class extends AnimatedObject {
  constructor(source) {
    super(source);
  }
  /** @internal */
  static create(source) {
    return new AnimatedArray(source);
  }
  getValue() {
    return this.source.map((node) => node.getValue());
  }
  setValue(source) {
    const payload = this.getPayload();
    if (source.length == payload.length) {
      return payload.map((node, i5) => node.setValue(source[i5])).some(Boolean);
    }
    super.setValue(source.map(makeAnimated));
    return true;
  }
};
function makeAnimated(value) {
  const nodeType = isAnimatedString(value) ? AnimatedString : AnimatedValue;
  return nodeType.create(value);
}
function getAnimatedType(value) {
  const parentNode = getAnimated(value);
  return parentNode ? parentNode.constructor : is.arr(value) ? AnimatedArray : isAnimatedString(value) ? AnimatedString : AnimatedValue;
}
var withAnimated = (Component, host2) => {
  const hasInstance = (
    // Function components must use "forwardRef" to avoid being
    // re-rendered on every animation frame.
    !is.fun(Component) || Component.prototype && Component.prototype.isReactComponent
  );
  return (0, import_react9.forwardRef)((givenProps, givenRef) => {
    const instanceRef = (0, import_react9.useRef)(null);
    const ref = hasInstance && // eslint-disable-next-line react-hooks/rules-of-hooks
    (0, import_react9.useCallback)(
      (value) => {
        instanceRef.current = updateRef(givenRef, value);
      },
      [givenRef]
    );
    const [props, deps] = getAnimatedState(givenProps, host2);
    const forceUpdate = useForceUpdate();
    const callback = () => {
      const instance = instanceRef.current;
      if (hasInstance && !instance) {
        return;
      }
      const didUpdate = instance ? host2.applyAnimatedValues(instance, props.getValue(true)) : false;
      if (didUpdate === false) {
        forceUpdate();
      }
    };
    const observer = new PropsObserver(callback, deps);
    const observerRef = (0, import_react9.useRef)();
    useIsomorphicLayoutEffect(() => {
      observerRef.current = observer;
      each(deps, (dep) => addFluidObserver(dep, observer));
      return () => {
        if (observerRef.current) {
          each(
            observerRef.current.deps,
            (dep) => removeFluidObserver(dep, observerRef.current)
          );
          raf.cancel(observerRef.current.update);
        }
      };
    });
    (0, import_react9.useEffect)(callback, []);
    useOnce(() => () => {
      const observer2 = observerRef.current;
      each(observer2.deps, (dep) => removeFluidObserver(dep, observer2));
    });
    const usedProps = host2.getComponentProps(props.getValue());
    return React.createElement(Component, { ...usedProps, ref });
  });
};
var PropsObserver = class {
  constructor(update3, deps) {
    this.update = update3;
    this.deps = deps;
  }
  eventObserved(event) {
    if (event.type == "change") {
      raf.write(this.update);
    }
  }
};
function getAnimatedState(props, host2) {
  const dependencies = /* @__PURE__ */ new Set();
  TreeContext.dependencies = dependencies;
  if (props.style)
    props = {
      ...props,
      style: host2.createAnimatedStyle(props.style)
    };
  props = new AnimatedObject(props);
  TreeContext.dependencies = null;
  return [props, dependencies];
}
function updateRef(ref, value) {
  if (ref) {
    if (is.fun(ref))
      ref(value);
    else
      ref.current = value;
  }
  return value;
}
var cacheKey = Symbol.for("AnimatedComponent");
var createHost = (components, {
  applyAnimatedValues: applyAnimatedValues2 = () => false,
  createAnimatedStyle = (style) => new AnimatedObject(style),
  getComponentProps = (props) => props
} = {}) => {
  const hostConfig = {
    applyAnimatedValues: applyAnimatedValues2,
    createAnimatedStyle,
    getComponentProps
  };
  const animated2 = (Component) => {
    const displayName = getDisplayName(Component) || "Anonymous";
    if (is.str(Component)) {
      Component = animated2[Component] || (animated2[Component] = withAnimated(Component, hostConfig));
    } else {
      Component = Component[cacheKey] || (Component[cacheKey] = withAnimated(Component, hostConfig));
    }
    Component.displayName = `Animated(${displayName})`;
    return Component;
  };
  eachProp(components, (Component, key) => {
    if (is.arr(components)) {
      key = getDisplayName(Component);
    }
    animated2[key] = animated2(Component);
  });
  return {
    animated: animated2
  };
};
var getDisplayName = (arg) => is.str(arg) ? arg : arg && is.str(arg.displayName) ? arg.displayName : is.fun(arg) && arg.name || null;

// node_modules/@react-spring/core/dist/react-spring_core.modern.mjs
var React2 = __toESM(require_react(), 1);
var import_react11 = __toESM(require_react(), 1);
var import_react12 = __toESM(require_react(), 1);
var React22 = __toESM(require_react(), 1);
var import_react13 = __toESM(require_react(), 1);
var import_react14 = __toESM(require_react(), 1);
function callProp(value, ...args) {
  return is.fun(value) ? value(...args) : value;
}
var matchProp = (value, key) => value === true || !!(key && value && (is.fun(value) ? value(key) : toArray(value).includes(key)));
var resolveProp = (prop, key) => is.obj(prop) ? key && prop[key] : prop;
var getDefaultProp = (props, key) => props.default === true ? props[key] : props.default ? props.default[key] : void 0;
var noopTransform = (value) => value;
var getDefaultProps = (props, transform = noopTransform) => {
  let keys = DEFAULT_PROPS;
  if (props.default && props.default !== true) {
    props = props.default;
    keys = Object.keys(props);
  }
  const defaults2 = {};
  for (const key of keys) {
    const value = transform(props[key], key);
    if (!is.und(value)) {
      defaults2[key] = value;
    }
  }
  return defaults2;
};
var DEFAULT_PROPS = [
  "config",
  "onProps",
  "onStart",
  "onChange",
  "onPause",
  "onResume",
  "onRest"
];
var RESERVED_PROPS = {
  config: 1,
  from: 1,
  to: 1,
  ref: 1,
  loop: 1,
  reset: 1,
  pause: 1,
  cancel: 1,
  reverse: 1,
  immediate: 1,
  default: 1,
  delay: 1,
  onProps: 1,
  onStart: 1,
  onChange: 1,
  onPause: 1,
  onResume: 1,
  onRest: 1,
  onResolve: 1,
  // Transition props
  items: 1,
  trail: 1,
  sort: 1,
  expires: 1,
  initial: 1,
  enter: 1,
  update: 1,
  leave: 1,
  children: 1,
  onDestroyed: 1,
  // Internal props
  keys: 1,
  callId: 1,
  parentId: 1
};
function getForwardProps(props) {
  const forward = {};
  let count2 = 0;
  eachProp(props, (value, prop) => {
    if (!RESERVED_PROPS[prop]) {
      forward[prop] = value;
      count2++;
    }
  });
  if (count2) {
    return forward;
  }
}
function inferTo(props) {
  const to22 = getForwardProps(props);
  if (to22) {
    const out = { to: to22 };
    eachProp(props, (val, key) => key in to22 || (out[key] = val));
    return out;
  }
  return { ...props };
}
function computeGoal(value) {
  value = getFluidValue(value);
  return is.arr(value) ? value.map(computeGoal) : isAnimatedString(value) ? globals_exports.createStringInterpolator({
    range: [0, 1],
    output: [value, value]
  })(1) : value;
}
function hasProps(props) {
  for (const _3 in props)
    return true;
  return false;
}
function isAsyncTo(to22) {
  return is.fun(to22) || is.arr(to22) && is.obj(to22[0]);
}
function detachRefs(ctrl, ref) {
  var _a;
  (_a = ctrl.ref) == null ? void 0 : _a.delete(ctrl);
  ref == null ? void 0 : ref.delete(ctrl);
}
function replaceRef(ctrl, ref) {
  var _a;
  if (ref && ctrl.ref !== ref) {
    (_a = ctrl.ref) == null ? void 0 : _a.delete(ctrl);
    ref.add(ctrl);
    ctrl.ref = ref;
  }
}
var config = {
  default: { tension: 170, friction: 26 },
  gentle: { tension: 120, friction: 14 },
  wobbly: { tension: 180, friction: 12 },
  stiff: { tension: 210, friction: 20 },
  slow: { tension: 280, friction: 60 },
  molasses: { tension: 280, friction: 120 }
};
var defaults = {
  ...config.default,
  mass: 1,
  damping: 1,
  easing: easings.linear,
  clamp: false
};
var AnimationConfig = class {
  constructor() {
    this.velocity = 0;
    Object.assign(this, defaults);
  }
};
function mergeConfig(config2, newConfig, defaultConfig) {
  if (defaultConfig) {
    defaultConfig = { ...defaultConfig };
    sanitizeConfig(defaultConfig, newConfig);
    newConfig = { ...defaultConfig, ...newConfig };
  }
  sanitizeConfig(config2, newConfig);
  Object.assign(config2, newConfig);
  for (const key in defaults) {
    if (config2[key] == null) {
      config2[key] = defaults[key];
    }
  }
  let { frequency, damping } = config2;
  const { mass } = config2;
  if (!is.und(frequency)) {
    if (frequency < 0.01)
      frequency = 0.01;
    if (damping < 0)
      damping = 0;
    config2.tension = Math.pow(2 * Math.PI / frequency, 2) * mass;
    config2.friction = 4 * Math.PI * damping * mass / frequency;
  }
  return config2;
}
function sanitizeConfig(config2, props) {
  if (!is.und(props.decay)) {
    config2.duration = void 0;
  } else {
    const isTensionConfig = !is.und(props.tension) || !is.und(props.friction);
    if (isTensionConfig || !is.und(props.frequency) || !is.und(props.damping) || !is.und(props.mass)) {
      config2.duration = void 0;
      config2.decay = void 0;
    }
    if (isTensionConfig) {
      config2.frequency = void 0;
    }
  }
}
var emptyArray = [];
var Animation = class {
  constructor() {
    this.changed = false;
    this.values = emptyArray;
    this.toValues = null;
    this.fromValues = emptyArray;
    this.config = new AnimationConfig();
    this.immediate = false;
  }
};
function scheduleProps(callId, { key, props, defaultProps, state, actions }) {
  return new Promise((resolve, reject) => {
    let delay;
    let timeout;
    let cancel = matchProp(props.cancel ?? (defaultProps == null ? void 0 : defaultProps.cancel), key);
    if (cancel) {
      onStart();
    } else {
      if (!is.und(props.pause)) {
        state.paused = matchProp(props.pause, key);
      }
      let pause = defaultProps == null ? void 0 : defaultProps.pause;
      if (pause !== true) {
        pause = state.paused || matchProp(pause, key);
      }
      delay = callProp(props.delay || 0, key);
      if (pause) {
        state.resumeQueue.add(onResume);
        actions.pause();
      } else {
        actions.resume();
        onResume();
      }
    }
    function onPause() {
      state.resumeQueue.add(onResume);
      state.timeouts.delete(timeout);
      timeout.cancel();
      delay = timeout.time - raf.now();
    }
    function onResume() {
      if (delay > 0 && !globals_exports.skipAnimation) {
        state.delayed = true;
        timeout = raf.setTimeout(onStart, delay);
        state.pauseQueue.add(onPause);
        state.timeouts.add(timeout);
      } else {
        onStart();
      }
    }
    function onStart() {
      if (state.delayed) {
        state.delayed = false;
      }
      state.pauseQueue.delete(onPause);
      state.timeouts.delete(timeout);
      if (callId <= (state.cancelId || 0)) {
        cancel = true;
      }
      try {
        actions.start({ ...props, callId, cancel }, resolve);
      } catch (err) {
        reject(err);
      }
    }
  });
}
var getCombinedResult = (target, results) => results.length == 1 ? results[0] : results.some((result) => result.cancelled) ? getCancelledResult(target.get()) : results.every((result) => result.noop) ? getNoopResult(target.get()) : getFinishedResult(
  target.get(),
  results.every((result) => result.finished)
);
var getNoopResult = (value) => ({
  value,
  noop: true,
  finished: true,
  cancelled: false
});
var getFinishedResult = (value, finished, cancelled = false) => ({
  value,
  finished,
  cancelled
});
var getCancelledResult = (value) => ({
  value,
  cancelled: true,
  finished: false
});
function runAsync(to22, props, state, target) {
  const { callId, parentId, onRest } = props;
  const { asyncTo: prevTo, promise: prevPromise } = state;
  if (!parentId && to22 === prevTo && !props.reset) {
    return prevPromise;
  }
  return state.promise = (async () => {
    state.asyncId = callId;
    state.asyncTo = to22;
    const defaultProps = getDefaultProps(
      props,
      (value, key) => (
        // The `onRest` prop is only called when the `runAsync` promise is resolved.
        key === "onRest" ? void 0 : value
      )
    );
    let preventBail;
    let bail;
    const bailPromise = new Promise(
      (resolve, reject) => (preventBail = resolve, bail = reject)
    );
    const bailIfEnded = (bailSignal) => {
      const bailResult = (
        // The `cancel` prop or `stop` method was used.
        callId <= (state.cancelId || 0) && getCancelledResult(target) || // The async `to` prop was replaced.
        callId !== state.asyncId && getFinishedResult(target, false)
      );
      if (bailResult) {
        bailSignal.result = bailResult;
        bail(bailSignal);
        throw bailSignal;
      }
    };
    const animate = (arg1, arg2) => {
      const bailSignal = new BailSignal();
      const skipAnimationSignal = new SkipAnimationSignal();
      return (async () => {
        if (globals_exports.skipAnimation) {
          stopAsync(state);
          skipAnimationSignal.result = getFinishedResult(target, false);
          bail(skipAnimationSignal);
          throw skipAnimationSignal;
        }
        bailIfEnded(bailSignal);
        const props2 = is.obj(arg1) ? { ...arg1 } : { ...arg2, to: arg1 };
        props2.parentId = callId;
        eachProp(defaultProps, (value, key) => {
          if (is.und(props2[key])) {
            props2[key] = value;
          }
        });
        const result2 = await target.start(props2);
        bailIfEnded(bailSignal);
        if (state.paused) {
          await new Promise((resume) => {
            state.resumeQueue.add(resume);
          });
        }
        return result2;
      })();
    };
    let result;
    if (globals_exports.skipAnimation) {
      stopAsync(state);
      return getFinishedResult(target, false);
    }
    try {
      let animating;
      if (is.arr(to22)) {
        animating = (async (queue) => {
          for (const props2 of queue) {
            await animate(props2);
          }
        })(to22);
      } else {
        animating = Promise.resolve(to22(animate, target.stop.bind(target)));
      }
      await Promise.all([animating.then(preventBail), bailPromise]);
      result = getFinishedResult(target.get(), true, false);
    } catch (err) {
      if (err instanceof BailSignal) {
        result = err.result;
      } else if (err instanceof SkipAnimationSignal) {
        result = err.result;
      } else {
        throw err;
      }
    } finally {
      if (callId == state.asyncId) {
        state.asyncId = parentId;
        state.asyncTo = parentId ? prevTo : void 0;
        state.promise = parentId ? prevPromise : void 0;
      }
    }
    if (is.fun(onRest)) {
      raf.batchedUpdates(() => {
        onRest(result, target, target.item);
      });
    }
    return result;
  })();
}
function stopAsync(state, cancelId) {
  flush(state.timeouts, (t9) => t9.cancel());
  state.pauseQueue.clear();
  state.resumeQueue.clear();
  state.asyncId = state.asyncTo = state.promise = void 0;
  if (cancelId)
    state.cancelId = cancelId;
}
var BailSignal = class extends Error {
  constructor() {
    super(
      "An async animation has been interrupted. You see this error because you forgot to use `await` or `.catch(...)` on its returned promise."
    );
  }
};
var SkipAnimationSignal = class extends Error {
  constructor() {
    super("SkipAnimationSignal");
  }
};
var isFrameValue = (value) => value instanceof FrameValue;
var nextId = 1;
var FrameValue = class extends FluidValue {
  constructor() {
    super(...arguments);
    this.id = nextId++;
    this._priority = 0;
  }
  get priority() {
    return this._priority;
  }
  set priority(priority2) {
    if (this._priority != priority2) {
      this._priority = priority2;
      this._onPriorityChange(priority2);
    }
  }
  /** Get the current value */
  get() {
    const node = getAnimated(this);
    return node && node.getValue();
  }
  /** Create a spring that maps our value to another value */
  to(...args) {
    return globals_exports.to(this, args);
  }
  /** @deprecated Use the `to` method instead. */
  interpolate(...args) {
    deprecateInterpolate();
    return globals_exports.to(this, args);
  }
  toJSON() {
    return this.get();
  }
  observerAdded(count2) {
    if (count2 == 1)
      this._attach();
  }
  observerRemoved(count2) {
    if (count2 == 0)
      this._detach();
  }
  /** Called when the first child is added. */
  _attach() {
  }
  /** Called when the last child is removed. */
  _detach() {
  }
  /** Tell our children about our new value */
  _onChange(value, idle = false) {
    callFluidObservers(this, {
      type: "change",
      parent: this,
      value,
      idle
    });
  }
  /** Tell our children about our new priority */
  _onPriorityChange(priority2) {
    if (!this.idle) {
      frameLoop.sort(this);
    }
    callFluidObservers(this, {
      type: "priority",
      parent: this,
      priority: priority2
    });
  }
};
var $P = Symbol.for("SpringPhase");
var HAS_ANIMATED = 1;
var IS_ANIMATING = 2;
var IS_PAUSED = 4;
var hasAnimated = (target) => (target[$P] & HAS_ANIMATED) > 0;
var isAnimating = (target) => (target[$P] & IS_ANIMATING) > 0;
var isPaused = (target) => (target[$P] & IS_PAUSED) > 0;
var setActiveBit = (target, active) => active ? target[$P] |= IS_ANIMATING | HAS_ANIMATED : target[$P] &= ~IS_ANIMATING;
var setPausedBit = (target, paused) => paused ? target[$P] |= IS_PAUSED : target[$P] &= ~IS_PAUSED;
var SpringValue = class extends FrameValue {
  constructor(arg1, arg2) {
    super();
    this.animation = new Animation();
    this.defaultProps = {};
    this._state = {
      paused: false,
      delayed: false,
      pauseQueue: /* @__PURE__ */ new Set(),
      resumeQueue: /* @__PURE__ */ new Set(),
      timeouts: /* @__PURE__ */ new Set()
    };
    this._pendingCalls = /* @__PURE__ */ new Set();
    this._lastCallId = 0;
    this._lastToId = 0;
    this._memoizedDuration = 0;
    if (!is.und(arg1) || !is.und(arg2)) {
      const props = is.obj(arg1) ? { ...arg1 } : { ...arg2, from: arg1 };
      if (is.und(props.default)) {
        props.default = true;
      }
      this.start(props);
    }
  }
  /** Equals true when not advancing on each frame. */
  get idle() {
    return !(isAnimating(this) || this._state.asyncTo) || isPaused(this);
  }
  get goal() {
    return getFluidValue(this.animation.to);
  }
  get velocity() {
    const node = getAnimated(this);
    return node instanceof AnimatedValue ? node.lastVelocity || 0 : node.getPayload().map((node2) => node2.lastVelocity || 0);
  }
  /**
   * When true, this value has been animated at least once.
   */
  get hasAnimated() {
    return hasAnimated(this);
  }
  /**
   * When true, this value has an unfinished animation,
   * which is either active or paused.
   */
  get isAnimating() {
    return isAnimating(this);
  }
  /**
   * When true, all current and future animations are paused.
   */
  get isPaused() {
    return isPaused(this);
  }
  /**
   *
   *
   */
  get isDelayed() {
    return this._state.delayed;
  }
  /** Advance the current animation by a number of milliseconds */
  advance(dt2) {
    let idle = true;
    let changed = false;
    const anim = this.animation;
    let { toValues } = anim;
    const { config: config2 } = anim;
    const payload = getPayload(anim.to);
    if (!payload && hasFluidValue(anim.to)) {
      toValues = toArray(getFluidValue(anim.to));
    }
    anim.values.forEach((node2, i5) => {
      if (node2.done)
        return;
      const to22 = (
        // Animated strings always go from 0 to 1.
        node2.constructor == AnimatedString ? 1 : payload ? payload[i5].lastPosition : toValues[i5]
      );
      let finished = anim.immediate;
      let position = to22;
      if (!finished) {
        position = node2.lastPosition;
        if (config2.tension <= 0) {
          node2.done = true;
          return;
        }
        let elapsed = node2.elapsedTime += dt2;
        const from = anim.fromValues[i5];
        const v0 = node2.v0 != null ? node2.v0 : node2.v0 = is.arr(config2.velocity) ? config2.velocity[i5] : config2.velocity;
        let velocity;
        const precision = config2.precision || (from == to22 ? 5e-3 : Math.min(1, Math.abs(to22 - from) * 1e-3));
        if (!is.und(config2.duration)) {
          let p5 = 1;
          if (config2.duration > 0) {
            if (this._memoizedDuration !== config2.duration) {
              this._memoizedDuration = config2.duration;
              if (node2.durationProgress > 0) {
                node2.elapsedTime = config2.duration * node2.durationProgress;
                elapsed = node2.elapsedTime += dt2;
              }
            }
            p5 = (config2.progress || 0) + elapsed / this._memoizedDuration;
            p5 = p5 > 1 ? 1 : p5 < 0 ? 0 : p5;
            node2.durationProgress = p5;
          }
          position = from + config2.easing(p5) * (to22 - from);
          velocity = (position - node2.lastPosition) / dt2;
          finished = p5 == 1;
        } else if (config2.decay) {
          const decay = config2.decay === true ? 0.998 : config2.decay;
          const e11 = Math.exp(-(1 - decay) * elapsed);
          position = from + v0 / (1 - decay) * (1 - e11);
          finished = Math.abs(node2.lastPosition - position) <= precision;
          velocity = v0 * e11;
        } else {
          velocity = node2.lastVelocity == null ? v0 : node2.lastVelocity;
          const restVelocity = config2.restVelocity || precision / 10;
          const bounceFactor = config2.clamp ? 0 : config2.bounce;
          const canBounce = !is.und(bounceFactor);
          const isGrowing = from == to22 ? node2.v0 > 0 : from < to22;
          let isMoving;
          let isBouncing = false;
          const step = 1;
          const numSteps = Math.ceil(dt2 / step);
          for (let n6 = 0; n6 < numSteps; ++n6) {
            isMoving = Math.abs(velocity) > restVelocity;
            if (!isMoving) {
              finished = Math.abs(to22 - position) <= precision;
              if (finished) {
                break;
              }
            }
            if (canBounce) {
              isBouncing = position == to22 || position > to22 == isGrowing;
              if (isBouncing) {
                velocity = -velocity * bounceFactor;
                position = to22;
              }
            }
            const springForce = -config2.tension * 1e-6 * (position - to22);
            const dampingForce = -config2.friction * 1e-3 * velocity;
            const acceleration = (springForce + dampingForce) / config2.mass;
            velocity = velocity + acceleration * step;
            position = position + velocity * step;
          }
        }
        node2.lastVelocity = velocity;
        if (Number.isNaN(position)) {
          console.warn(`Got NaN while animating:`, this);
          finished = true;
        }
      }
      if (payload && !payload[i5].done) {
        finished = false;
      }
      if (finished) {
        node2.done = true;
      } else {
        idle = false;
      }
      if (node2.setValue(position, config2.round)) {
        changed = true;
      }
    });
    const node = getAnimated(this);
    const currVal = node.getValue();
    if (idle) {
      const finalVal = getFluidValue(anim.to);
      if ((currVal !== finalVal || changed) && !config2.decay) {
        node.setValue(finalVal);
        this._onChange(finalVal);
      } else if (changed && config2.decay) {
        this._onChange(currVal);
      }
      this._stop();
    } else if (changed) {
      this._onChange(currVal);
    }
  }
  /** Set the current value, while stopping the current animation */
  set(value) {
    raf.batchedUpdates(() => {
      this._stop();
      this._focus(value);
      this._set(value);
    });
    return this;
  }
  /**
   * Freeze the active animation in time, as well as any updates merged
   * before `resume` is called.
   */
  pause() {
    this._update({ pause: true });
  }
  /** Resume the animation if paused. */
  resume() {
    this._update({ pause: false });
  }
  /** Skip to the end of the current animation. */
  finish() {
    if (isAnimating(this)) {
      const { to: to22, config: config2 } = this.animation;
      raf.batchedUpdates(() => {
        this._onStart();
        if (!config2.decay) {
          this._set(to22, false);
        }
        this._stop();
      });
    }
    return this;
  }
  /** Push props into the pending queue. */
  update(props) {
    const queue = this.queue || (this.queue = []);
    queue.push(props);
    return this;
  }
  start(to22, arg2) {
    let queue;
    if (!is.und(to22)) {
      queue = [is.obj(to22) ? to22 : { ...arg2, to: to22 }];
    } else {
      queue = this.queue || [];
      this.queue = [];
    }
    return Promise.all(
      queue.map((props) => {
        const up = this._update(props);
        return up;
      })
    ).then((results) => getCombinedResult(this, results));
  }
  /**
   * Stop the current animation, and cancel any delayed updates.
   *
   * Pass `true` to call `onRest` with `cancelled: true`.
   */
  stop(cancel) {
    const { to: to22 } = this.animation;
    this._focus(this.get());
    stopAsync(this._state, cancel && this._lastCallId);
    raf.batchedUpdates(() => this._stop(to22, cancel));
    return this;
  }
  /** Restart the animation. */
  reset() {
    this._update({ reset: true });
  }
  /** @internal */
  eventObserved(event) {
    if (event.type == "change") {
      this._start();
    } else if (event.type == "priority") {
      this.priority = event.priority + 1;
    }
  }
  /**
   * Parse the `to` and `from` range from the given `props` object.
   *
   * This also ensures the initial value is available to animated components
   * during the render phase.
   */
  _prepareNode(props) {
    const key = this.key || "";
    let { to: to22, from } = props;
    to22 = is.obj(to22) ? to22[key] : to22;
    if (to22 == null || isAsyncTo(to22)) {
      to22 = void 0;
    }
    from = is.obj(from) ? from[key] : from;
    if (from == null) {
      from = void 0;
    }
    const range2 = { to: to22, from };
    if (!hasAnimated(this)) {
      if (props.reverse)
        [to22, from] = [from, to22];
      from = getFluidValue(from);
      if (!is.und(from)) {
        this._set(from);
      } else if (!getAnimated(this)) {
        this._set(to22);
      }
    }
    return range2;
  }
  /** Every update is processed by this method before merging. */
  _update({ ...props }, isLoop) {
    const { key, defaultProps } = this;
    if (props.default)
      Object.assign(
        defaultProps,
        getDefaultProps(
          props,
          (value, prop) => /^on/.test(prop) ? resolveProp(value, key) : value
        )
      );
    mergeActiveFn(this, props, "onProps");
    sendEvent(this, "onProps", props, this);
    const range2 = this._prepareNode(props);
    if (Object.isFrozen(this)) {
      throw Error(
        "Cannot animate a `SpringValue` object that is frozen. Did you forget to pass your component to `animated(...)` before animating its props?"
      );
    }
    const state = this._state;
    return scheduleProps(++this._lastCallId, {
      key,
      props,
      defaultProps,
      state,
      actions: {
        pause: () => {
          if (!isPaused(this)) {
            setPausedBit(this, true);
            flushCalls(state.pauseQueue);
            sendEvent(
              this,
              "onPause",
              getFinishedResult(this, checkFinished(this, this.animation.to)),
              this
            );
          }
        },
        resume: () => {
          if (isPaused(this)) {
            setPausedBit(this, false);
            if (isAnimating(this)) {
              this._resume();
            }
            flushCalls(state.resumeQueue);
            sendEvent(
              this,
              "onResume",
              getFinishedResult(this, checkFinished(this, this.animation.to)),
              this
            );
          }
        },
        start: this._merge.bind(this, range2)
      }
    }).then((result) => {
      if (props.loop && result.finished && !(isLoop && result.noop)) {
        const nextProps = createLoopUpdate(props);
        if (nextProps) {
          return this._update(nextProps, true);
        }
      }
      return result;
    });
  }
  /** Merge props into the current animation */
  _merge(range2, props, resolve) {
    if (props.cancel) {
      this.stop(true);
      return resolve(getCancelledResult(this));
    }
    const hasToProp = !is.und(range2.to);
    const hasFromProp = !is.und(range2.from);
    if (hasToProp || hasFromProp) {
      if (props.callId > this._lastToId) {
        this._lastToId = props.callId;
      } else {
        return resolve(getCancelledResult(this));
      }
    }
    const { key, defaultProps, animation: anim } = this;
    const { to: prevTo, from: prevFrom } = anim;
    let { to: to22 = prevTo, from = prevFrom } = range2;
    if (hasFromProp && !hasToProp && (!props.default || is.und(to22))) {
      to22 = from;
    }
    if (props.reverse)
      [to22, from] = [from, to22];
    const hasFromChanged = !isEqual(from, prevFrom);
    if (hasFromChanged) {
      anim.from = from;
    }
    from = getFluidValue(from);
    const hasToChanged = !isEqual(to22, prevTo);
    if (hasToChanged) {
      this._focus(to22);
    }
    const hasAsyncTo = isAsyncTo(props.to);
    const { config: config2 } = anim;
    const { decay, velocity } = config2;
    if (hasToProp || hasFromProp) {
      config2.velocity = 0;
    }
    if (props.config && !hasAsyncTo) {
      mergeConfig(
        config2,
        callProp(props.config, key),
        // Avoid calling the same "config" prop twice.
        props.config !== defaultProps.config ? callProp(defaultProps.config, key) : void 0
      );
    }
    let node = getAnimated(this);
    if (!node || is.und(to22)) {
      return resolve(getFinishedResult(this, true));
    }
    const reset = (
      // When `reset` is undefined, the `from` prop implies `reset: true`,
      // except for declarative updates. When `reset` is defined, there
      // must exist a value to animate from.
      is.und(props.reset) ? hasFromProp && !props.default : !is.und(from) && matchProp(props.reset, key)
    );
    const value = reset ? from : this.get();
    const goal = computeGoal(to22);
    const isAnimatable = is.num(goal) || is.arr(goal) || isAnimatedString(goal);
    const immediate = !hasAsyncTo && (!isAnimatable || matchProp(defaultProps.immediate || props.immediate, key));
    if (hasToChanged) {
      const nodeType = getAnimatedType(to22);
      if (nodeType !== node.constructor) {
        if (immediate) {
          node = this._set(goal);
        } else
          throw Error(
            `Cannot animate between ${node.constructor.name} and ${nodeType.name}, as the "to" prop suggests`
          );
      }
    }
    const goalType = node.constructor;
    let started = hasFluidValue(to22);
    let finished = false;
    if (!started) {
      const hasValueChanged = reset || !hasAnimated(this) && hasFromChanged;
      if (hasToChanged || hasValueChanged) {
        finished = isEqual(computeGoal(value), goal);
        started = !finished;
      }
      if (!isEqual(anim.immediate, immediate) && !immediate || !isEqual(config2.decay, decay) || !isEqual(config2.velocity, velocity)) {
        started = true;
      }
    }
    if (finished && isAnimating(this)) {
      if (anim.changed && !reset) {
        started = true;
      } else if (!started) {
        this._stop(prevTo);
      }
    }
    if (!hasAsyncTo) {
      if (started || hasFluidValue(prevTo)) {
        anim.values = node.getPayload();
        anim.toValues = hasFluidValue(to22) ? null : goalType == AnimatedString ? [1] : toArray(goal);
      }
      if (anim.immediate != immediate) {
        anim.immediate = immediate;
        if (!immediate && !reset) {
          this._set(prevTo);
        }
      }
      if (started) {
        const { onRest } = anim;
        each(ACTIVE_EVENTS, (type) => mergeActiveFn(this, props, type));
        const result = getFinishedResult(this, checkFinished(this, prevTo));
        flushCalls(this._pendingCalls, result);
        this._pendingCalls.add(resolve);
        if (anim.changed)
          raf.batchedUpdates(() => {
            var _a;
            anim.changed = !reset;
            onRest == null ? void 0 : onRest(result, this);
            if (reset) {
              callProp(defaultProps.onRest, result);
            } else {
              (_a = anim.onStart) == null ? void 0 : _a.call(anim, result, this);
            }
          });
      }
    }
    if (reset) {
      this._set(value);
    }
    if (hasAsyncTo) {
      resolve(runAsync(props.to, props, this._state, this));
    } else if (started) {
      this._start();
    } else if (isAnimating(this) && !hasToChanged) {
      this._pendingCalls.add(resolve);
    } else {
      resolve(getNoopResult(value));
    }
  }
  /** Update the `animation.to` value, which might be a `FluidValue` */
  _focus(value) {
    const anim = this.animation;
    if (value !== anim.to) {
      if (getFluidObservers(this)) {
        this._detach();
      }
      anim.to = value;
      if (getFluidObservers(this)) {
        this._attach();
      }
    }
  }
  _attach() {
    let priority2 = 0;
    const { to: to22 } = this.animation;
    if (hasFluidValue(to22)) {
      addFluidObserver(to22, this);
      if (isFrameValue(to22)) {
        priority2 = to22.priority + 1;
      }
    }
    this.priority = priority2;
  }
  _detach() {
    const { to: to22 } = this.animation;
    if (hasFluidValue(to22)) {
      removeFluidObserver(to22, this);
    }
  }
  /**
   * Update the current value from outside the frameloop,
   * and return the `Animated` node.
   */
  _set(arg, idle = true) {
    const value = getFluidValue(arg);
    if (!is.und(value)) {
      const oldNode = getAnimated(this);
      if (!oldNode || !isEqual(value, oldNode.getValue())) {
        const nodeType = getAnimatedType(value);
        if (!oldNode || oldNode.constructor != nodeType) {
          setAnimated(this, nodeType.create(value));
        } else {
          oldNode.setValue(value);
        }
        if (oldNode) {
          raf.batchedUpdates(() => {
            this._onChange(value, idle);
          });
        }
      }
    }
    return getAnimated(this);
  }
  _onStart() {
    const anim = this.animation;
    if (!anim.changed) {
      anim.changed = true;
      sendEvent(
        this,
        "onStart",
        getFinishedResult(this, checkFinished(this, anim.to)),
        this
      );
    }
  }
  _onChange(value, idle) {
    if (!idle) {
      this._onStart();
      callProp(this.animation.onChange, value, this);
    }
    callProp(this.defaultProps.onChange, value, this);
    super._onChange(value, idle);
  }
  // This method resets the animation state (even if already animating) to
  // ensure the latest from/to range is used, and it also ensures this spring
  // is added to the frameloop.
  _start() {
    const anim = this.animation;
    getAnimated(this).reset(getFluidValue(anim.to));
    if (!anim.immediate) {
      anim.fromValues = anim.values.map((node) => node.lastPosition);
    }
    if (!isAnimating(this)) {
      setActiveBit(this, true);
      if (!isPaused(this)) {
        this._resume();
      }
    }
  }
  _resume() {
    if (globals_exports.skipAnimation) {
      this.finish();
    } else {
      frameLoop.start(this);
    }
  }
  /**
   * Exit the frameloop and notify `onRest` listeners.
   *
   * Always wrap `_stop` calls with `batchedUpdates`.
   */
  _stop(goal, cancel) {
    if (isAnimating(this)) {
      setActiveBit(this, false);
      const anim = this.animation;
      each(anim.values, (node) => {
        node.done = true;
      });
      if (anim.toValues) {
        anim.onChange = anim.onPause = anim.onResume = void 0;
      }
      callFluidObservers(this, {
        type: "idle",
        parent: this
      });
      const result = cancel ? getCancelledResult(this.get()) : getFinishedResult(this.get(), checkFinished(this, goal ?? anim.to));
      flushCalls(this._pendingCalls, result);
      if (anim.changed) {
        anim.changed = false;
        sendEvent(this, "onRest", result, this);
      }
    }
  }
};
function checkFinished(target, to22) {
  const goal = computeGoal(to22);
  const value = computeGoal(target.get());
  return isEqual(value, goal);
}
function createLoopUpdate(props, loop2 = props.loop, to22 = props.to) {
  const loopRet = callProp(loop2);
  if (loopRet) {
    const overrides = loopRet !== true && inferTo(loopRet);
    const reverse2 = (overrides || props).reverse;
    const reset = !overrides || overrides.reset;
    return createUpdate({
      ...props,
      loop: loop2,
      // Avoid updating default props when looping.
      default: false,
      // Never loop the `pause` prop.
      pause: void 0,
      // For the "reverse" prop to loop as expected, the "to" prop
      // must be undefined. The "reverse" prop is ignored when the
      // "to" prop is an array or function.
      to: !reverse2 || isAsyncTo(to22) ? to22 : void 0,
      // Ignore the "from" prop except on reset.
      from: reset ? props.from : void 0,
      reset,
      // The "loop" prop can return a "useSpring" props object to
      // override any of the original props.
      ...overrides
    });
  }
}
function createUpdate(props) {
  const { to: to22, from } = props = inferTo(props);
  const keys = /* @__PURE__ */ new Set();
  if (is.obj(to22))
    findDefined(to22, keys);
  if (is.obj(from))
    findDefined(from, keys);
  props.keys = keys.size ? Array.from(keys) : null;
  return props;
}
function declareUpdate(props) {
  const update22 = createUpdate(props);
  if (is.und(update22.default)) {
    update22.default = getDefaultProps(update22);
  }
  return update22;
}
function findDefined(values, keys) {
  eachProp(values, (value, key) => value != null && keys.add(key));
}
var ACTIVE_EVENTS = [
  "onStart",
  "onRest",
  "onChange",
  "onPause",
  "onResume"
];
function mergeActiveFn(target, props, type) {
  target.animation[type] = props[type] !== getDefaultProp(props, type) ? resolveProp(props[type], target.key) : void 0;
}
function sendEvent(target, type, ...args) {
  var _a, _b, _c, _d;
  (_b = (_a = target.animation)[type]) == null ? void 0 : _b.call(_a, ...args);
  (_d = (_c = target.defaultProps)[type]) == null ? void 0 : _d.call(_c, ...args);
}
var BATCHED_EVENTS = ["onStart", "onChange", "onRest"];
var nextId2 = 1;
var Controller = class {
  constructor(props, flush3) {
    this.id = nextId2++;
    this.springs = {};
    this.queue = [];
    this._lastAsyncId = 0;
    this._active = /* @__PURE__ */ new Set();
    this._changed = /* @__PURE__ */ new Set();
    this._started = false;
    this._state = {
      paused: false,
      pauseQueue: /* @__PURE__ */ new Set(),
      resumeQueue: /* @__PURE__ */ new Set(),
      timeouts: /* @__PURE__ */ new Set()
    };
    this._events = {
      onStart: /* @__PURE__ */ new Map(),
      onChange: /* @__PURE__ */ new Map(),
      onRest: /* @__PURE__ */ new Map()
    };
    this._onFrame = this._onFrame.bind(this);
    if (flush3) {
      this._flush = flush3;
    }
    if (props) {
      this.start({ default: true, ...props });
    }
  }
  /**
   * Equals `true` when no spring values are in the frameloop, and
   * no async animation is currently active.
   */
  get idle() {
    return !this._state.asyncTo && Object.values(this.springs).every((spring) => {
      return spring.idle && !spring.isDelayed && !spring.isPaused;
    });
  }
  get item() {
    return this._item;
  }
  set item(item) {
    this._item = item;
  }
  /** Get the current values of our springs */
  get() {
    const values = {};
    this.each((spring, key) => values[key] = spring.get());
    return values;
  }
  /** Set the current values without animating. */
  set(values) {
    for (const key in values) {
      const value = values[key];
      if (!is.und(value)) {
        this.springs[key].set(value);
      }
    }
  }
  /** Push an update onto the queue of each value. */
  update(props) {
    if (props) {
      this.queue.push(createUpdate(props));
    }
    return this;
  }
  /**
   * Start the queued animations for every spring, and resolve the returned
   * promise once all queued animations have finished or been cancelled.
   *
   * When you pass a queue (instead of nothing), that queue is used instead of
   * the queued animations added with the `update` method, which are left alone.
   */
  start(props) {
    let { queue } = this;
    if (props) {
      queue = toArray(props).map(createUpdate);
    } else {
      this.queue = [];
    }
    if (this._flush) {
      return this._flush(this, queue);
    }
    prepareKeys(this, queue);
    return flushUpdateQueue(this, queue);
  }
  /** @internal */
  stop(arg, keys) {
    if (arg !== !!arg) {
      keys = arg;
    }
    if (keys) {
      const springs = this.springs;
      each(toArray(keys), (key) => springs[key].stop(!!arg));
    } else {
      stopAsync(this._state, this._lastAsyncId);
      this.each((spring) => spring.stop(!!arg));
    }
    return this;
  }
  /** Freeze the active animation in time */
  pause(keys) {
    if (is.und(keys)) {
      this.start({ pause: true });
    } else {
      const springs = this.springs;
      each(toArray(keys), (key) => springs[key].pause());
    }
    return this;
  }
  /** Resume the animation if paused. */
  resume(keys) {
    if (is.und(keys)) {
      this.start({ pause: false });
    } else {
      const springs = this.springs;
      each(toArray(keys), (key) => springs[key].resume());
    }
    return this;
  }
  /** Call a function once per spring value */
  each(iterator) {
    eachProp(this.springs, iterator);
  }
  /** @internal Called at the end of every animation frame */
  _onFrame() {
    const { onStart, onChange, onRest } = this._events;
    const active = this._active.size > 0;
    const changed = this._changed.size > 0;
    if (active && !this._started || changed && !this._started) {
      this._started = true;
      flush(onStart, ([onStart2, result]) => {
        result.value = this.get();
        onStart2(result, this, this._item);
      });
    }
    const idle = !active && this._started;
    const values = changed || idle && onRest.size ? this.get() : null;
    if (changed && onChange.size) {
      flush(onChange, ([onChange2, result]) => {
        result.value = values;
        onChange2(result, this, this._item);
      });
    }
    if (idle) {
      this._started = false;
      flush(onRest, ([onRest2, result]) => {
        result.value = values;
        onRest2(result, this, this._item);
      });
    }
  }
  /** @internal */
  eventObserved(event) {
    if (event.type == "change") {
      this._changed.add(event.parent);
      if (!event.idle) {
        this._active.add(event.parent);
      }
    } else if (event.type == "idle") {
      this._active.delete(event.parent);
    } else
      return;
    raf.onFrame(this._onFrame);
  }
};
function flushUpdateQueue(ctrl, queue) {
  return Promise.all(queue.map((props) => flushUpdate(ctrl, props))).then(
    (results) => getCombinedResult(ctrl, results)
  );
}
async function flushUpdate(ctrl, props, isLoop) {
  const { keys, to: to22, from, loop: loop2, onRest, onResolve } = props;
  const defaults2 = is.obj(props.default) && props.default;
  if (loop2) {
    props.loop = false;
  }
  if (to22 === false)
    props.to = null;
  if (from === false)
    props.from = null;
  const asyncTo = is.arr(to22) || is.fun(to22) ? to22 : void 0;
  if (asyncTo) {
    props.to = void 0;
    props.onRest = void 0;
    if (defaults2) {
      defaults2.onRest = void 0;
    }
  } else {
    each(BATCHED_EVENTS, (key) => {
      const handler = props[key];
      if (is.fun(handler)) {
        const queue = ctrl["_events"][key];
        props[key] = ({ finished, cancelled }) => {
          const result2 = queue.get(handler);
          if (result2) {
            if (!finished)
              result2.finished = false;
            if (cancelled)
              result2.cancelled = true;
          } else {
            queue.set(handler, {
              value: null,
              finished: finished || false,
              cancelled: cancelled || false
            });
          }
        };
        if (defaults2) {
          defaults2[key] = props[key];
        }
      }
    });
  }
  const state = ctrl["_state"];
  if (props.pause === !state.paused) {
    state.paused = props.pause;
    flushCalls(props.pause ? state.pauseQueue : state.resumeQueue);
  } else if (state.paused) {
    props.pause = true;
  }
  const promises = (keys || Object.keys(ctrl.springs)).map(
    (key) => ctrl.springs[key].start(props)
  );
  const cancel = props.cancel === true || getDefaultProp(props, "cancel") === true;
  if (asyncTo || cancel && state.asyncId) {
    promises.push(
      scheduleProps(++ctrl["_lastAsyncId"], {
        props,
        state,
        actions: {
          pause: noop,
          resume: noop,
          start(props2, resolve) {
            if (cancel) {
              stopAsync(state, ctrl["_lastAsyncId"]);
              resolve(getCancelledResult(ctrl));
            } else {
              props2.onRest = onRest;
              resolve(
                runAsync(
                  asyncTo,
                  props2,
                  state,
                  ctrl
                )
              );
            }
          }
        }
      })
    );
  }
  if (state.paused) {
    await new Promise((resume) => {
      state.resumeQueue.add(resume);
    });
  }
  const result = getCombinedResult(ctrl, await Promise.all(promises));
  if (loop2 && result.finished && !(isLoop && result.noop)) {
    const nextProps = createLoopUpdate(props, loop2, to22);
    if (nextProps) {
      prepareKeys(ctrl, [nextProps]);
      return flushUpdate(ctrl, nextProps, true);
    }
  }
  if (onResolve) {
    raf.batchedUpdates(() => onResolve(result, ctrl, ctrl.item));
  }
  return result;
}
function getSprings(ctrl, props) {
  const springs = { ...ctrl.springs };
  if (props) {
    each(toArray(props), (props2) => {
      if (is.und(props2.keys)) {
        props2 = createUpdate(props2);
      }
      if (!is.obj(props2.to)) {
        props2 = { ...props2, to: void 0 };
      }
      prepareSprings(springs, props2, (key) => {
        return createSpring(key);
      });
    });
  }
  setSprings(ctrl, springs);
  return springs;
}
function setSprings(ctrl, springs) {
  eachProp(springs, (spring, key) => {
    if (!ctrl.springs[key]) {
      ctrl.springs[key] = spring;
      addFluidObserver(spring, ctrl);
    }
  });
}
function createSpring(key, observer) {
  const spring = new SpringValue();
  spring.key = key;
  if (observer) {
    addFluidObserver(spring, observer);
  }
  return spring;
}
function prepareSprings(springs, props, create) {
  if (props.keys) {
    each(props.keys, (key) => {
      const spring = springs[key] || (springs[key] = create(key));
      spring["_prepareNode"](props);
    });
  }
}
function prepareKeys(ctrl, queue) {
  each(queue, (props) => {
    prepareSprings(ctrl.springs, props, (key) => {
      return createSpring(key, ctrl);
    });
  });
}
var SpringContext = ({
  children,
  ...props
}) => {
  const inherited = (0, import_react11.useContext)(ctx);
  const pause = props.pause || !!inherited.pause, immediate = props.immediate || !!inherited.immediate;
  props = useMemoOne(() => ({ pause, immediate }), [pause, immediate]);
  const { Provider } = ctx;
  return React2.createElement(Provider, { value: props }, children);
};
var ctx = makeContext(SpringContext, {});
SpringContext.Provider = ctx.Provider;
SpringContext.Consumer = ctx.Consumer;
function makeContext(target, init) {
  Object.assign(target, React2.createContext(init));
  target.Provider._context = target;
  target.Consumer._context = target;
  return target;
}
var SpringRef = () => {
  const current = [];
  const SpringRef2 = function(props) {
    deprecateDirectCall();
    const results = [];
    each(current, (ctrl, i5) => {
      if (is.und(props)) {
        results.push(ctrl.start());
      } else {
        const update22 = _getProps(props, ctrl, i5);
        if (update22) {
          results.push(ctrl.start(update22));
        }
      }
    });
    return results;
  };
  SpringRef2.current = current;
  SpringRef2.add = function(ctrl) {
    if (!current.includes(ctrl)) {
      current.push(ctrl);
    }
  };
  SpringRef2.delete = function(ctrl) {
    const i5 = current.indexOf(ctrl);
    if (~i5)
      current.splice(i5, 1);
  };
  SpringRef2.pause = function() {
    each(current, (ctrl) => ctrl.pause(...arguments));
    return this;
  };
  SpringRef2.resume = function() {
    each(current, (ctrl) => ctrl.resume(...arguments));
    return this;
  };
  SpringRef2.set = function(values) {
    each(current, (ctrl, i5) => {
      const update22 = is.fun(values) ? values(i5, ctrl) : values;
      if (update22) {
        ctrl.set(update22);
      }
    });
  };
  SpringRef2.start = function(props) {
    const results = [];
    each(current, (ctrl, i5) => {
      if (is.und(props)) {
        results.push(ctrl.start());
      } else {
        const update22 = this._getProps(props, ctrl, i5);
        if (update22) {
          results.push(ctrl.start(update22));
        }
      }
    });
    return results;
  };
  SpringRef2.stop = function() {
    each(current, (ctrl) => ctrl.stop(...arguments));
    return this;
  };
  SpringRef2.update = function(props) {
    each(current, (ctrl, i5) => ctrl.update(this._getProps(props, ctrl, i5)));
    return this;
  };
  const _getProps = function(arg, ctrl, index2) {
    return is.fun(arg) ? arg(index2, ctrl) : arg;
  };
  SpringRef2._getProps = _getProps;
  return SpringRef2;
};
function useSprings(length, props, deps) {
  const propsFn = is.fun(props) && props;
  if (propsFn && !deps)
    deps = [];
  const ref = (0, import_react10.useMemo)(
    () => propsFn || arguments.length == 3 ? SpringRef() : void 0,
    []
  );
  const layoutId = (0, import_react10.useRef)(0);
  const forceUpdate = useForceUpdate();
  const state = (0, import_react10.useMemo)(
    () => ({
      ctrls: [],
      queue: [],
      flush(ctrl, updates2) {
        const springs2 = getSprings(ctrl, updates2);
        const canFlushSync = layoutId.current > 0 && !state.queue.length && !Object.keys(springs2).some((key) => !ctrl.springs[key]);
        return canFlushSync ? flushUpdateQueue(ctrl, updates2) : new Promise((resolve) => {
          setSprings(ctrl, springs2);
          state.queue.push(() => {
            resolve(flushUpdateQueue(ctrl, updates2));
          });
          forceUpdate();
        });
      }
    }),
    []
  );
  const ctrls = (0, import_react10.useRef)([...state.ctrls]);
  const updates = [];
  const prevLength = usePrev(length) || 0;
  (0, import_react10.useMemo)(() => {
    each(ctrls.current.slice(length, prevLength), (ctrl) => {
      detachRefs(ctrl, ref);
      ctrl.stop(true);
    });
    ctrls.current.length = length;
    declareUpdates(prevLength, length);
  }, [length]);
  (0, import_react10.useMemo)(() => {
    declareUpdates(0, Math.min(prevLength, length));
  }, deps);
  function declareUpdates(startIndex, endIndex) {
    for (let i5 = startIndex; i5 < endIndex; i5++) {
      const ctrl = ctrls.current[i5] || (ctrls.current[i5] = new Controller(null, state.flush));
      const update22 = propsFn ? propsFn(i5, ctrl) : props[i5];
      if (update22) {
        updates[i5] = declareUpdate(update22);
      }
    }
  }
  const springs = ctrls.current.map((ctrl, i5) => getSprings(ctrl, updates[i5]));
  const context = (0, import_react10.useContext)(SpringContext);
  const prevContext = usePrev(context);
  const hasContext = context !== prevContext && hasProps(context);
  useIsomorphicLayoutEffect(() => {
    layoutId.current++;
    state.ctrls = ctrls.current;
    const { queue } = state;
    if (queue.length) {
      state.queue = [];
      each(queue, (cb) => cb());
    }
    each(ctrls.current, (ctrl, i5) => {
      ref == null ? void 0 : ref.add(ctrl);
      if (hasContext) {
        ctrl.start({ default: context });
      }
      const update22 = updates[i5];
      if (update22) {
        replaceRef(ctrl, update22.ref);
        if (ctrl.ref) {
          ctrl.queue.push(update22);
        } else {
          ctrl.start(update22);
        }
      }
    });
  });
  useOnce(() => () => {
    each(state.ctrls, (ctrl) => ctrl.stop(true));
  });
  const values = springs.map((x5) => ({ ...x5 }));
  return ref ? [values, ref] : values;
}
function useSpring(props, deps) {
  const isFn = is.fun(props);
  const [[values], ref] = useSprings(
    1,
    isFn ? props : [props],
    isFn ? deps || [] : deps
  );
  return isFn || arguments.length == 2 ? [values, ref] : values;
}
function useTransition(data, props, deps) {
  const propsFn = is.fun(props) && props;
  const {
    reset,
    sort: sort2,
    trail = 0,
    expires = true,
    exitBeforeEnter = false,
    onDestroyed,
    ref: propsRef,
    config: propsConfig
  } = propsFn ? propsFn() : props;
  const ref = (0, import_react13.useMemo)(
    () => propsFn || arguments.length == 3 ? SpringRef() : void 0,
    []
  );
  const items = toArray(data);
  const transitions = [];
  const usedTransitions = (0, import_react13.useRef)(null);
  const prevTransitions = reset ? null : usedTransitions.current;
  useIsomorphicLayoutEffect(() => {
    usedTransitions.current = transitions;
  });
  useOnce(() => {
    each(transitions, (t9) => {
      ref == null ? void 0 : ref.add(t9.ctrl);
      t9.ctrl.ref = ref;
    });
    return () => {
      each(usedTransitions.current, (t9) => {
        if (t9.expired) {
          clearTimeout(t9.expirationId);
        }
        detachRefs(t9.ctrl, ref);
        t9.ctrl.stop(true);
      });
    };
  });
  const keys = getKeys(items, propsFn ? propsFn() : props, prevTransitions);
  const expired = reset && usedTransitions.current || [];
  useIsomorphicLayoutEffect(
    () => each(expired, ({ ctrl, item, key }) => {
      detachRefs(ctrl, ref);
      callProp(onDestroyed, item, key);
    })
  );
  const reused = [];
  if (prevTransitions)
    each(prevTransitions, (t9, i5) => {
      if (t9.expired) {
        clearTimeout(t9.expirationId);
        expired.push(t9);
      } else {
        i5 = reused[i5] = keys.indexOf(t9.key);
        if (~i5)
          transitions[i5] = t9;
      }
    });
  each(items, (item, i5) => {
    if (!transitions[i5]) {
      transitions[i5] = {
        key: keys[i5],
        item,
        phase: "mount",
        ctrl: new Controller()
      };
      transitions[i5].ctrl.item = item;
    }
  });
  if (reused.length) {
    let i5 = -1;
    const { leave } = propsFn ? propsFn() : props;
    each(reused, (keyIndex, prevIndex) => {
      const t9 = prevTransitions[prevIndex];
      if (~keyIndex) {
        i5 = transitions.indexOf(t9);
        transitions[i5] = { ...t9, item: items[keyIndex] };
      } else if (leave) {
        transitions.splice(++i5, 0, t9);
      }
    });
  }
  if (is.fun(sort2)) {
    transitions.sort((a5, b5) => sort2(a5.item, b5.item));
  }
  let delay = -trail;
  const forceUpdate = useForceUpdate();
  const defaultProps = getDefaultProps(props);
  const changes = /* @__PURE__ */ new Map();
  const exitingTransitions = (0, import_react13.useRef)(/* @__PURE__ */ new Map());
  const forceChange = (0, import_react13.useRef)(false);
  each(transitions, (t9, i5) => {
    const key = t9.key;
    const prevPhase = t9.phase;
    const p5 = propsFn ? propsFn() : props;
    let to22;
    let phase;
    const propsDelay = callProp(p5.delay || 0, key);
    if (prevPhase == "mount") {
      to22 = p5.enter;
      phase = "enter";
    } else {
      const isLeave = keys.indexOf(key) < 0;
      if (prevPhase != "leave") {
        if (isLeave) {
          to22 = p5.leave;
          phase = "leave";
        } else if (to22 = p5.update) {
          phase = "update";
        } else
          return;
      } else if (!isLeave) {
        to22 = p5.enter;
        phase = "enter";
      } else
        return;
    }
    to22 = callProp(to22, t9.item, i5);
    to22 = is.obj(to22) ? inferTo(to22) : { to: to22 };
    if (!to22.config) {
      const config2 = propsConfig || defaultProps.config;
      to22.config = callProp(config2, t9.item, i5, phase);
    }
    delay += trail;
    const payload = {
      ...defaultProps,
      // we need to add our props.delay value you here.
      delay: propsDelay + delay,
      ref: propsRef,
      immediate: p5.immediate,
      // This prevents implied resets.
      reset: false,
      // Merge any phase-specific props.
      ...to22
    };
    if (phase == "enter" && is.und(payload.from)) {
      const p22 = propsFn ? propsFn() : props;
      const from = is.und(p22.initial) || prevTransitions ? p22.from : p22.initial;
      payload.from = callProp(from, t9.item, i5);
    }
    const { onResolve } = payload;
    payload.onResolve = (result) => {
      callProp(onResolve, result);
      const transitions2 = usedTransitions.current;
      const t22 = transitions2.find((t32) => t32.key === key);
      if (!t22)
        return;
      if (result.cancelled && t22.phase != "update") {
        return;
      }
      if (t22.ctrl.idle) {
        const idle = transitions2.every((t32) => t32.ctrl.idle);
        if (t22.phase == "leave") {
          const expiry = callProp(expires, t22.item);
          if (expiry !== false) {
            const expiryMs = expiry === true ? 0 : expiry;
            t22.expired = true;
            if (!idle && expiryMs > 0) {
              if (expiryMs <= 2147483647)
                t22.expirationId = setTimeout(forceUpdate, expiryMs);
              return;
            }
          }
        }
        if (idle && transitions2.some((t32) => t32.expired)) {
          exitingTransitions.current.delete(t22);
          if (exitBeforeEnter) {
            forceChange.current = true;
          }
          forceUpdate();
        }
      }
    };
    const springs = getSprings(t9.ctrl, payload);
    if (phase === "leave" && exitBeforeEnter) {
      exitingTransitions.current.set(t9, { phase, springs, payload });
    } else {
      changes.set(t9, { phase, springs, payload });
    }
  });
  const context = (0, import_react13.useContext)(SpringContext);
  const prevContext = usePrev(context);
  const hasContext = context !== prevContext && hasProps(context);
  useIsomorphicLayoutEffect(() => {
    if (hasContext) {
      each(transitions, (t9) => {
        t9.ctrl.start({ default: context });
      });
    }
  }, [context]);
  each(changes, (_3, t9) => {
    if (exitingTransitions.current.size) {
      const ind = transitions.findIndex((state) => state.key === t9.key);
      transitions.splice(ind, 1);
    }
  });
  useIsomorphicLayoutEffect(
    () => {
      each(
        exitingTransitions.current.size ? exitingTransitions.current : changes,
        ({ phase, payload }, t9) => {
          const { ctrl } = t9;
          t9.phase = phase;
          ref == null ? void 0 : ref.add(ctrl);
          if (hasContext && phase == "enter") {
            ctrl.start({ default: context });
          }
          if (payload) {
            replaceRef(ctrl, payload.ref);
            if ((ctrl.ref || ref) && !forceChange.current) {
              ctrl.update(payload);
            } else {
              ctrl.start(payload);
              if (forceChange.current) {
                forceChange.current = false;
              }
            }
          }
        }
      );
    },
    reset ? void 0 : deps
  );
  const renderTransitions = (render) => React22.createElement(React22.Fragment, null, transitions.map((t9, i5) => {
    const { springs } = changes.get(t9) || t9.ctrl;
    const elem = render({ ...springs }, t9.item, t9, i5);
    return elem && elem.type ? React22.createElement(
      elem.type,
      {
        ...elem.props,
        key: is.str(t9.key) || is.num(t9.key) ? t9.key : t9.ctrl.id,
        ref: elem.ref
      }
    ) : elem;
  }));
  return ref ? [renderTransitions, ref] : renderTransitions;
}
var nextKey = 1;
function getKeys(items, { key, keys = key }, prevTransitions) {
  if (keys === null) {
    const reused = /* @__PURE__ */ new Set();
    return items.map((item) => {
      const t9 = prevTransitions && prevTransitions.find(
        (t22) => t22.item === item && t22.phase !== "leave" && !reused.has(t22)
      );
      if (t9) {
        reused.add(t9);
        return t9.key;
      }
      return nextKey++;
    });
  }
  return is.und(keys) ? items : is.fun(keys) ? items.map(keys) : toArray(keys);
}
var Interpolation = class extends FrameValue {
  constructor(source, args) {
    super();
    this.source = source;
    this.idle = true;
    this._active = /* @__PURE__ */ new Set();
    this.calc = createInterpolator(...args);
    const value = this._get();
    const nodeType = getAnimatedType(value);
    setAnimated(this, nodeType.create(value));
  }
  advance(_dt) {
    const value = this._get();
    const oldValue = this.get();
    if (!isEqual(value, oldValue)) {
      getAnimated(this).setValue(value);
      this._onChange(value, this.idle);
    }
    if (!this.idle && checkIdle(this._active)) {
      becomeIdle(this);
    }
  }
  _get() {
    const inputs = is.arr(this.source) ? this.source.map(getFluidValue) : toArray(getFluidValue(this.source));
    return this.calc(...inputs);
  }
  _start() {
    if (this.idle && !checkIdle(this._active)) {
      this.idle = false;
      each(getPayload(this), (node) => {
        node.done = false;
      });
      if (globals_exports.skipAnimation) {
        raf.batchedUpdates(() => this.advance());
        becomeIdle(this);
      } else {
        frameLoop.start(this);
      }
    }
  }
  // Observe our sources only when we're observed.
  _attach() {
    let priority2 = 1;
    each(toArray(this.source), (source) => {
      if (hasFluidValue(source)) {
        addFluidObserver(source, this);
      }
      if (isFrameValue(source)) {
        if (!source.idle) {
          this._active.add(source);
        }
        priority2 = Math.max(priority2, source.priority + 1);
      }
    });
    this.priority = priority2;
    this._start();
  }
  // Stop observing our sources once we have no observers.
  _detach() {
    each(toArray(this.source), (source) => {
      if (hasFluidValue(source)) {
        removeFluidObserver(source, this);
      }
    });
    this._active.clear();
    becomeIdle(this);
  }
  /** @internal */
  eventObserved(event) {
    if (event.type == "change") {
      if (event.idle) {
        this.advance();
      } else {
        this._active.add(event.parent);
        this._start();
      }
    } else if (event.type == "idle") {
      this._active.delete(event.parent);
    } else if (event.type == "priority") {
      this.priority = toArray(this.source).reduce(
        (highest, parent) => Math.max(highest, (isFrameValue(parent) ? parent.priority : 0) + 1),
        0
      );
    }
  }
};
function isIdle(source) {
  return source.idle !== false;
}
function checkIdle(active) {
  return !active.size || Array.from(active).every(isIdle);
}
function becomeIdle(self) {
  if (!self.idle) {
    self.idle = true;
    each(getPayload(self), (node) => {
      node.done = true;
    });
    callFluidObservers(self, {
      type: "idle",
      parent: self
    });
  }
}
var to2 = (source, ...args) => new Interpolation(source, args);
globals_exports.assign({
  createStringInterpolator: createStringInterpolator2,
  to: (source, args) => new Interpolation(source, args)
});
var update2 = frameLoop.advance;

// node_modules/@react-spring/web/dist/react-spring_web.modern.mjs
var import_react_dom = __toESM(require_react_dom(), 1);
var isCustomPropRE = /^--/;
function dangerousStyleValue(name, value) {
  if (value == null || typeof value === "boolean" || value === "")
    return "";
  if (typeof value === "number" && value !== 0 && !isCustomPropRE.test(name) && !(isUnitlessNumber.hasOwnProperty(name) && isUnitlessNumber[name]))
    return value + "px";
  return ("" + value).trim();
}
var attributeCache = {};
function applyAnimatedValues(instance, props) {
  if (!instance.nodeType || !instance.setAttribute) {
    return false;
  }
  const isFilterElement = instance.nodeName === "filter" || instance.parentNode && instance.parentNode.nodeName === "filter";
  const {
    className,
    style,
    children,
    scrollTop,
    scrollLeft,
    viewBox,
    ...attributes
  } = props;
  const values = Object.values(attributes);
  const names = Object.keys(attributes).map(
    (name) => isFilterElement || instance.hasAttribute(name) ? name : attributeCache[name] || (attributeCache[name] = name.replace(
      /([A-Z])/g,
      // Attributes are written in dash case
      (n6) => "-" + n6.toLowerCase()
    ))
  );
  if (children !== void 0) {
    instance.textContent = children;
  }
  for (const name in style) {
    if (style.hasOwnProperty(name)) {
      const value = dangerousStyleValue(name, style[name]);
      if (isCustomPropRE.test(name)) {
        instance.style.setProperty(name, value);
      } else {
        instance.style[name] = value;
      }
    }
  }
  names.forEach((name, i5) => {
    instance.setAttribute(name, values[i5]);
  });
  if (className !== void 0) {
    instance.className = className;
  }
  if (scrollTop !== void 0) {
    instance.scrollTop = scrollTop;
  }
  if (scrollLeft !== void 0) {
    instance.scrollLeft = scrollLeft;
  }
  if (viewBox !== void 0) {
    instance.setAttribute("viewBox", viewBox);
  }
}
var isUnitlessNumber = {
  animationIterationCount: true,
  borderImageOutset: true,
  borderImageSlice: true,
  borderImageWidth: true,
  boxFlex: true,
  boxFlexGroup: true,
  boxOrdinalGroup: true,
  columnCount: true,
  columns: true,
  flex: true,
  flexGrow: true,
  flexPositive: true,
  flexShrink: true,
  flexNegative: true,
  flexOrder: true,
  gridRow: true,
  gridRowEnd: true,
  gridRowSpan: true,
  gridRowStart: true,
  gridColumn: true,
  gridColumnEnd: true,
  gridColumnSpan: true,
  gridColumnStart: true,
  fontWeight: true,
  lineClamp: true,
  lineHeight: true,
  opacity: true,
  order: true,
  orphans: true,
  tabSize: true,
  widows: true,
  zIndex: true,
  zoom: true,
  // SVG-related properties
  fillOpacity: true,
  floodOpacity: true,
  stopOpacity: true,
  strokeDasharray: true,
  strokeDashoffset: true,
  strokeMiterlimit: true,
  strokeOpacity: true,
  strokeWidth: true
};
var prefixKey = (prefix2, key) => prefix2 + key.charAt(0).toUpperCase() + key.substring(1);
var prefixes = ["Webkit", "Ms", "Moz", "O"];
isUnitlessNumber = Object.keys(isUnitlessNumber).reduce((acc, prop) => {
  prefixes.forEach((prefix2) => acc[prefixKey(prefix2, prop)] = acc[prop]);
  return acc;
}, isUnitlessNumber);
var domTransforms = /^(matrix|translate|scale|rotate|skew)/;
var pxTransforms = /^(translate)/;
var degTransforms = /^(rotate|skew)/;
var addUnit = (value, unit2) => is.num(value) && value !== 0 ? value + unit2 : value;
var isValueIdentity = (value, id) => is.arr(value) ? value.every((v6) => isValueIdentity(v6, id)) : is.num(value) ? value === id : parseFloat(value) === id;
var AnimatedStyle = class extends AnimatedObject {
  constructor({ x: x5, y: y5, z: z5, ...style }) {
    const inputs = [];
    const transforms = [];
    if (x5 || y5 || z5) {
      inputs.push([x5 || 0, y5 || 0, z5 || 0]);
      transforms.push((xyz) => [
        `translate3d(${xyz.map((v6) => addUnit(v6, "px")).join(",")})`,
        // prettier-ignore
        isValueIdentity(xyz, 0)
      ]);
    }
    eachProp(style, (value, key) => {
      if (key === "transform") {
        inputs.push([value || ""]);
        transforms.push((transform) => [transform, transform === ""]);
      } else if (domTransforms.test(key)) {
        delete style[key];
        if (is.und(value))
          return;
        const unit2 = pxTransforms.test(key) ? "px" : degTransforms.test(key) ? "deg" : "";
        inputs.push(toArray(value));
        transforms.push(
          key === "rotate3d" ? ([x22, y22, z22, deg]) => [
            `rotate3d(${x22},${y22},${z22},${addUnit(deg, unit2)})`,
            isValueIdentity(deg, 0)
          ] : (input) => [
            `${key}(${input.map((v6) => addUnit(v6, unit2)).join(",")})`,
            isValueIdentity(input, key.startsWith("scale") ? 1 : 0)
          ]
        );
      }
    });
    if (inputs.length) {
      style.transform = new FluidTransform(inputs, transforms);
    }
    super(style);
  }
};
var FluidTransform = class extends FluidValue {
  constructor(inputs, transforms) {
    super();
    this.inputs = inputs;
    this.transforms = transforms;
    this._value = null;
  }
  get() {
    return this._value || (this._value = this._get());
  }
  _get() {
    let transform = "";
    let identity5 = true;
    each(this.inputs, (input, i5) => {
      const arg1 = getFluidValue(input[0]);
      const [t9, id] = this.transforms[i5](
        is.arr(arg1) ? arg1 : input.map(getFluidValue)
      );
      transform += " " + t9;
      identity5 = identity5 && id;
    });
    return identity5 ? "none" : transform;
  }
  // Start observing our inputs once we have an observer.
  observerAdded(count2) {
    if (count2 == 1)
      each(
        this.inputs,
        (input) => each(
          input,
          (value) => hasFluidValue(value) && addFluidObserver(value, this)
        )
      );
  }
  // Stop observing our inputs once we have no observers.
  observerRemoved(count2) {
    if (count2 == 0)
      each(
        this.inputs,
        (input) => each(
          input,
          (value) => hasFluidValue(value) && removeFluidObserver(value, this)
        )
      );
  }
  eventObserved(event) {
    if (event.type == "change") {
      this._value = null;
    }
    callFluidObservers(this, event);
  }
};
var primitives = [
  "a",
  "abbr",
  "address",
  "area",
  "article",
  "aside",
  "audio",
  "b",
  "base",
  "bdi",
  "bdo",
  "big",
  "blockquote",
  "body",
  "br",
  "button",
  "canvas",
  "caption",
  "cite",
  "code",
  "col",
  "colgroup",
  "data",
  "datalist",
  "dd",
  "del",
  "details",
  "dfn",
  "dialog",
  "div",
  "dl",
  "dt",
  "em",
  "embed",
  "fieldset",
  "figcaption",
  "figure",
  "footer",
  "form",
  "h1",
  "h2",
  "h3",
  "h4",
  "h5",
  "h6",
  "head",
  "header",
  "hgroup",
  "hr",
  "html",
  "i",
  "iframe",
  "img",
  "input",
  "ins",
  "kbd",
  "keygen",
  "label",
  "legend",
  "li",
  "link",
  "main",
  "map",
  "mark",
  "menu",
  "menuitem",
  "meta",
  "meter",
  "nav",
  "noscript",
  "object",
  "ol",
  "optgroup",
  "option",
  "output",
  "p",
  "param",
  "picture",
  "pre",
  "progress",
  "q",
  "rp",
  "rt",
  "ruby",
  "s",
  "samp",
  "script",
  "section",
  "select",
  "small",
  "source",
  "span",
  "strong",
  "style",
  "sub",
  "summary",
  "sup",
  "table",
  "tbody",
  "td",
  "textarea",
  "tfoot",
  "th",
  "thead",
  "time",
  "title",
  "tr",
  "track",
  "u",
  "ul",
  "var",
  "video",
  "wbr",
  // SVG
  "circle",
  "clipPath",
  "defs",
  "ellipse",
  "foreignObject",
  "g",
  "image",
  "line",
  "linearGradient",
  "mask",
  "path",
  "pattern",
  "polygon",
  "polyline",
  "radialGradient",
  "rect",
  "stop",
  "svg",
  "text",
  "tspan"
];
globals_exports.assign({
  batchedUpdates: import_react_dom.unstable_batchedUpdates,
  createStringInterpolator: createStringInterpolator2,
  colors: colors2
});
var host = createHost(primitives, {
  applyAnimatedValues,
  createAnimatedStyle: (style) => new AnimatedStyle(style),
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  getComponentProps: ({ scrollTop, scrollLeft, ...props }) => props
});
var animated = host.animated;

// node_modules/@nivo/tooltip/dist/nivo-tooltip.es.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
function v() {
  return v = Object.assign ? Object.assign.bind() : function(t9) {
    for (var i5 = 1; i5 < arguments.length; i5++) {
      var o4 = arguments[i5];
      for (var n6 in o4) Object.prototype.hasOwnProperty.call(o4, n6) && (t9[n6] = o4[n6]);
    }
    return t9;
  }, v.apply(this, arguments);
}
var x = { pointerEvents: "none", position: "absolute", zIndex: 10, top: 0, left: 0 };
var m = function(t9, i5) {
  return "translate(" + t9 + "px, " + i5 + "px)";
};
var b = (0, import_react15.memo)(function(t9) {
  var o4, n6 = t9.position, r5 = t9.anchor, e11 = t9.children, l3 = zt(), d3 = Ur(), y5 = d3.animate, f3 = d3.config, b5 = kt(), g5 = b5[0], w6 = b5[1], T4 = (0, import_react15.useRef)(false), C7 = void 0, E5 = false, P5 = w6.width > 0 && w6.height > 0, j5 = Math.round(n6[0]), N3 = Math.round(n6[1]);
  P5 && ("top" === r5 ? (j5 -= w6.width / 2, N3 -= w6.height + 14) : "right" === r5 ? (j5 += 14, N3 -= w6.height / 2) : "bottom" === r5 ? (j5 -= w6.width / 2, N3 += 14) : "left" === r5 ? (j5 -= w6.width + 14, N3 -= w6.height / 2) : "center" === r5 && (j5 -= w6.width / 2, N3 -= w6.height / 2), C7 = { transform: m(j5, N3) }, T4.current || (E5 = true), T4.current = [j5, N3]);
  var O6 = useSpring({ to: C7, config: f3, immediate: !y5 || E5 }), V3 = v({}, x, l3.tooltip.wrapper, { transform: null != (o4 = O6.transform) ? o4 : m(j5, N3), opacity: O6.transform ? 1 : 0 });
  return (0, import_jsx_runtime.jsx)(animated.div, { ref: g5, style: V3, children: e11 });
});
b.displayName = "TooltipWrapper";
var g = (0, import_react15.memo)(function(t9) {
  var i5 = t9.size, o4 = void 0 === i5 ? 12 : i5, n6 = t9.color, r5 = t9.style;
  return (0, import_jsx_runtime.jsx)("span", { style: v({ display: "block", width: o4, height: o4, background: n6 }, void 0 === r5 ? {} : r5) });
});
var w = (0, import_react15.memo)(function(t9) {
  var i5, o4 = t9.id, n6 = t9.value, r5 = t9.format, e11 = t9.enableChip, l3 = void 0 !== e11 && e11, a5 = t9.color, c9 = t9.renderContent, h2 = zt(), u3 = Ot(r5);
  if ("function" == typeof c9) i5 = c9();
  else {
    var f3 = n6;
    void 0 !== u3 && void 0 !== f3 && (f3 = u3(f3)), i5 = (0, import_jsx_runtime.jsxs)("div", { style: h2.tooltip.basic, children: [l3 && (0, import_jsx_runtime.jsx)(g, { color: a5, style: h2.tooltip.chip }), void 0 !== f3 ? (0, import_jsx_runtime.jsxs)("span", { children: [o4, ": ", (0, import_jsx_runtime.jsx)("strong", { children: "" + f3 })] }) : o4] });
  }
  return (0, import_jsx_runtime.jsx)("div", { style: h2.tooltip.container, children: i5 });
});
var T = { width: "100%", borderCollapse: "collapse" };
var C = (0, import_react15.memo)(function(t9) {
  var i5, o4 = t9.title, n6 = t9.rows, r5 = void 0 === n6 ? [] : n6, e11 = t9.renderContent, l3 = zt();
  return r5.length ? (i5 = "function" == typeof e11 ? e11() : (0, import_jsx_runtime.jsxs)("div", { children: [o4 && o4, (0, import_jsx_runtime.jsx)("table", { style: v({}, T, l3.tooltip.table), children: (0, import_jsx_runtime.jsx)("tbody", { children: r5.map(function(t10, i6) {
    return (0, import_jsx_runtime.jsx)("tr", { children: t10.map(function(t11, i7) {
      return (0, import_jsx_runtime.jsx)("td", { style: l3.tooltip.tableCell, children: t11 }, i7);
    }) }, i6);
  }) }) })] }), (0, import_jsx_runtime.jsx)("div", { style: l3.tooltip.container, children: i5 })) : null;
});
C.displayName = "TableTooltip";
var E = (0, import_react15.memo)(function(t9) {
  var i5 = t9.x0, n6 = t9.x1, r5 = t9.y0, e11 = t9.y1, l3 = zt(), u3 = Ur(), d3 = u3.animate, y5 = u3.config, f3 = (0, import_react15.useMemo)(function() {
    return v({}, l3.crosshair.line, { pointerEvents: "none" });
  }, [l3.crosshair.line]), x5 = useSpring({ x1: i5, x2: n6, y1: r5, y2: e11, config: y5, immediate: !d3 });
  return (0, import_jsx_runtime.jsx)(animated.line, v({}, x5, { fill: "none", style: f3 }));
});
E.displayName = "CrosshairLine";
var P = (0, import_react15.memo)(function(t9) {
  var i5, o4, n6 = t9.width, r5 = t9.height, e11 = t9.type, l3 = t9.x, a5 = t9.y;
  return "cross" === e11 ? (i5 = { x0: l3, x1: l3, y0: 0, y1: r5 }, o4 = { x0: 0, x1: n6, y0: a5, y1: a5 }) : "top-left" === e11 ? (i5 = { x0: l3, x1: l3, y0: 0, y1: a5 }, o4 = { x0: 0, x1: l3, y0: a5, y1: a5 }) : "top" === e11 ? i5 = { x0: l3, x1: l3, y0: 0, y1: a5 } : "top-right" === e11 ? (i5 = { x0: l3, x1: l3, y0: 0, y1: a5 }, o4 = { x0: l3, x1: n6, y0: a5, y1: a5 }) : "right" === e11 ? o4 = { x0: l3, x1: n6, y0: a5, y1: a5 } : "bottom-right" === e11 ? (i5 = { x0: l3, x1: l3, y0: a5, y1: r5 }, o4 = { x0: l3, x1: n6, y0: a5, y1: a5 }) : "bottom" === e11 ? i5 = { x0: l3, x1: l3, y0: a5, y1: r5 } : "bottom-left" === e11 ? (i5 = { x0: l3, x1: l3, y0: a5, y1: r5 }, o4 = { x0: 0, x1: l3, y0: a5, y1: a5 }) : "left" === e11 ? o4 = { x0: 0, x1: l3, y0: a5, y1: a5 } : "x" === e11 ? i5 = { x0: l3, x1: l3, y0: 0, y1: r5 } : "y" === e11 && (o4 = { x0: 0, x1: n6, y0: a5, y1: a5 }), (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [i5 && (0, import_jsx_runtime.jsx)(E, { x0: i5.x0, x1: i5.x1, y0: i5.y0, y1: i5.y1 }), o4 && (0, import_jsx_runtime.jsx)(E, { x0: o4.x0, x1: o4.x1, y0: o4.y0, y1: o4.y1 })] });
});
P.displayName = "Crosshair";
var j = (0, import_react15.createContext)({ showTooltipAt: function() {
}, showTooltipFromEvent: function() {
}, hideTooltip: function() {
} });
var N = { isVisible: false, position: [null, null], content: null, anchor: null };
var O = (0, import_react15.createContext)(N);
var V = function(t9) {
  var i5 = (0, import_react15.useState)(N), n6 = i5[0], l3 = i5[1], a5 = (0, import_react15.useCallback)(function(t10, i6, o4) {
    var n7 = i6[0], r5 = i6[1];
    void 0 === o4 && (o4 = "top"), l3({ isVisible: true, position: [n7, r5], anchor: o4, content: t10 });
  }, [l3]), c9 = (0, import_react15.useCallback)(function(i6, o4, n7) {
    void 0 === n7 && (n7 = "top");
    var r5 = t9.current.getBoundingClientRect(), e11 = t9.current.offsetWidth, a6 = e11 === r5.width ? 1 : e11 / r5.width, c10 = "touches" in o4 ? o4.touches[0] : o4, s5 = c10.clientX, h2 = c10.clientY, u3 = (s5 - r5.left) * a6, d3 = (h2 - r5.top) * a6;
    "left" !== n7 && "right" !== n7 || (n7 = u3 < r5.width / 2 ? "right" : "left"), l3({ isVisible: true, position: [u3, d3], anchor: n7, content: i6 });
  }, [t9, l3]), s4 = (0, import_react15.useCallback)(function() {
    l3(N);
  }, [l3]);
  return { actions: (0, import_react15.useMemo)(function() {
    return { showTooltipAt: a5, showTooltipFromEvent: c9, hideTooltip: s4 };
  }, [a5, c9, s4]), state: n6 };
};
var k = function() {
  var t9 = (0, import_react15.useContext)(j);
  if (void 0 === t9) throw new Error("useTooltip must be used within a TooltipProvider");
  return t9;
};
var z = function() {
  var t9 = (0, import_react15.useContext)(O);
  if (void 0 === t9) throw new Error("useTooltipState must be used within a TooltipProvider");
  return t9;
};
var A = function(t9) {
  return t9.isVisible;
};
var F = function() {
  var t9 = z();
  return A(t9) ? (0, import_jsx_runtime.jsx)(b, { position: t9.position, anchor: t9.anchor, children: t9.content }) : null;
};
var M = function(t9) {
  var i5 = t9.container, o4 = t9.children, n6 = V(i5), r5 = n6.actions, e11 = n6.state;
  return (0, import_jsx_runtime.jsx)(j.Provider, { value: r5, children: (0, import_jsx_runtime.jsx)(O.Provider, { value: e11, children: o4 }) });
};

// node_modules/@nivo/core/dist/nivo-core.es.js
var import_merge2 = __toESM(require_merge());
var import_get = __toESM(require_get());
var import_set = __toESM(require_set());

// node_modules/d3-color/src/define.js
function define_default(constructor, factory, prototype) {
  constructor.prototype = factory.prototype = prototype;
  prototype.constructor = constructor;
}
function extend(parent, definition) {
  var prototype = Object.create(parent.prototype);
  for (var key in definition) prototype[key] = definition[key];
  return prototype;
}

// node_modules/d3-color/src/color.js
function Color() {
}
var darker = 0.7;
var brighter = 1 / darker;
var reI = "\\s*([+-]?\\d+)\\s*";
var reN = "\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*";
var reP = "\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*";
var reHex = /^#([0-9a-f]{3,8})$/;
var reRgbInteger = new RegExp(`^rgb\\(${reI},${reI},${reI}\\)$`);
var reRgbPercent = new RegExp(`^rgb\\(${reP},${reP},${reP}\\)$`);
var reRgbaInteger = new RegExp(`^rgba\\(${reI},${reI},${reI},${reN}\\)$`);
var reRgbaPercent = new RegExp(`^rgba\\(${reP},${reP},${reP},${reN}\\)$`);
var reHslPercent = new RegExp(`^hsl\\(${reN},${reP},${reP}\\)$`);
var reHslaPercent = new RegExp(`^hsla\\(${reN},${reP},${reP},${reN}\\)$`);
var named = {
  aliceblue: 15792383,
  antiquewhite: 16444375,
  aqua: 65535,
  aquamarine: 8388564,
  azure: 15794175,
  beige: 16119260,
  bisque: 16770244,
  black: 0,
  blanchedalmond: 16772045,
  blue: 255,
  blueviolet: 9055202,
  brown: 10824234,
  burlywood: 14596231,
  cadetblue: 6266528,
  chartreuse: 8388352,
  chocolate: 13789470,
  coral: 16744272,
  cornflowerblue: 6591981,
  cornsilk: 16775388,
  crimson: 14423100,
  cyan: 65535,
  darkblue: 139,
  darkcyan: 35723,
  darkgoldenrod: 12092939,
  darkgray: 11119017,
  darkgreen: 25600,
  darkgrey: 11119017,
  darkkhaki: 12433259,
  darkmagenta: 9109643,
  darkolivegreen: 5597999,
  darkorange: 16747520,
  darkorchid: 10040012,
  darkred: 9109504,
  darksalmon: 15308410,
  darkseagreen: 9419919,
  darkslateblue: 4734347,
  darkslategray: 3100495,
  darkslategrey: 3100495,
  darkturquoise: 52945,
  darkviolet: 9699539,
  deeppink: 16716947,
  deepskyblue: 49151,
  dimgray: 6908265,
  dimgrey: 6908265,
  dodgerblue: 2003199,
  firebrick: 11674146,
  floralwhite: 16775920,
  forestgreen: 2263842,
  fuchsia: 16711935,
  gainsboro: 14474460,
  ghostwhite: 16316671,
  gold: 16766720,
  goldenrod: 14329120,
  gray: 8421504,
  green: 32768,
  greenyellow: 11403055,
  grey: 8421504,
  honeydew: 15794160,
  hotpink: 16738740,
  indianred: 13458524,
  indigo: 4915330,
  ivory: 16777200,
  khaki: 15787660,
  lavender: 15132410,
  lavenderblush: 16773365,
  lawngreen: 8190976,
  lemonchiffon: 16775885,
  lightblue: 11393254,
  lightcoral: 15761536,
  lightcyan: 14745599,
  lightgoldenrodyellow: 16448210,
  lightgray: 13882323,
  lightgreen: 9498256,
  lightgrey: 13882323,
  lightpink: 16758465,
  lightsalmon: 16752762,
  lightseagreen: 2142890,
  lightskyblue: 8900346,
  lightslategray: 7833753,
  lightslategrey: 7833753,
  lightsteelblue: 11584734,
  lightyellow: 16777184,
  lime: 65280,
  limegreen: 3329330,
  linen: 16445670,
  magenta: 16711935,
  maroon: 8388608,
  mediumaquamarine: 6737322,
  mediumblue: 205,
  mediumorchid: 12211667,
  mediumpurple: 9662683,
  mediumseagreen: 3978097,
  mediumslateblue: 8087790,
  mediumspringgreen: 64154,
  mediumturquoise: 4772300,
  mediumvioletred: 13047173,
  midnightblue: 1644912,
  mintcream: 16121850,
  mistyrose: 16770273,
  moccasin: 16770229,
  navajowhite: 16768685,
  navy: 128,
  oldlace: 16643558,
  olive: 8421376,
  olivedrab: 7048739,
  orange: 16753920,
  orangered: 16729344,
  orchid: 14315734,
  palegoldenrod: 15657130,
  palegreen: 10025880,
  paleturquoise: 11529966,
  palevioletred: 14381203,
  papayawhip: 16773077,
  peachpuff: 16767673,
  peru: 13468991,
  pink: 16761035,
  plum: 14524637,
  powderblue: 11591910,
  purple: 8388736,
  rebeccapurple: 6697881,
  red: 16711680,
  rosybrown: 12357519,
  royalblue: 4286945,
  saddlebrown: 9127187,
  salmon: 16416882,
  sandybrown: 16032864,
  seagreen: 3050327,
  seashell: 16774638,
  sienna: 10506797,
  silver: 12632256,
  skyblue: 8900331,
  slateblue: 6970061,
  slategray: 7372944,
  slategrey: 7372944,
  snow: 16775930,
  springgreen: 65407,
  steelblue: 4620980,
  tan: 13808780,
  teal: 32896,
  thistle: 14204888,
  tomato: 16737095,
  turquoise: 4251856,
  violet: 15631086,
  wheat: 16113331,
  white: 16777215,
  whitesmoke: 16119285,
  yellow: 16776960,
  yellowgreen: 10145074
};
define_default(Color, color, {
  copy(channels) {
    return Object.assign(new this.constructor(), this, channels);
  },
  displayable() {
    return this.rgb().displayable();
  },
  hex: color_formatHex,
  // Deprecated! Use color.formatHex.
  formatHex: color_formatHex,
  formatHex8: color_formatHex8,
  formatHsl: color_formatHsl,
  formatRgb: color_formatRgb,
  toString: color_formatRgb
});
function color_formatHex() {
  return this.rgb().formatHex();
}
function color_formatHex8() {
  return this.rgb().formatHex8();
}
function color_formatHsl() {
  return hslConvert(this).formatHsl();
}
function color_formatRgb() {
  return this.rgb().formatRgb();
}
function color(format2) {
  var m5, l3;
  format2 = (format2 + "").trim().toLowerCase();
  return (m5 = reHex.exec(format2)) ? (l3 = m5[1].length, m5 = parseInt(m5[1], 16), l3 === 6 ? rgbn(m5) : l3 === 3 ? new Rgb(m5 >> 8 & 15 | m5 >> 4 & 240, m5 >> 4 & 15 | m5 & 240, (m5 & 15) << 4 | m5 & 15, 1) : l3 === 8 ? rgba2(m5 >> 24 & 255, m5 >> 16 & 255, m5 >> 8 & 255, (m5 & 255) / 255) : l3 === 4 ? rgba2(m5 >> 12 & 15 | m5 >> 8 & 240, m5 >> 8 & 15 | m5 >> 4 & 240, m5 >> 4 & 15 | m5 & 240, ((m5 & 15) << 4 | m5 & 15) / 255) : null) : (m5 = reRgbInteger.exec(format2)) ? new Rgb(m5[1], m5[2], m5[3], 1) : (m5 = reRgbPercent.exec(format2)) ? new Rgb(m5[1] * 255 / 100, m5[2] * 255 / 100, m5[3] * 255 / 100, 1) : (m5 = reRgbaInteger.exec(format2)) ? rgba2(m5[1], m5[2], m5[3], m5[4]) : (m5 = reRgbaPercent.exec(format2)) ? rgba2(m5[1] * 255 / 100, m5[2] * 255 / 100, m5[3] * 255 / 100, m5[4]) : (m5 = reHslPercent.exec(format2)) ? hsla2(m5[1], m5[2] / 100, m5[3] / 100, 1) : (m5 = reHslaPercent.exec(format2)) ? hsla2(m5[1], m5[2] / 100, m5[3] / 100, m5[4]) : named.hasOwnProperty(format2) ? rgbn(named[format2]) : format2 === "transparent" ? new Rgb(NaN, NaN, NaN, 0) : null;
}
function rgbn(n6) {
  return new Rgb(n6 >> 16 & 255, n6 >> 8 & 255, n6 & 255, 1);
}
function rgba2(r5, g5, b5, a5) {
  if (a5 <= 0) r5 = g5 = b5 = NaN;
  return new Rgb(r5, g5, b5, a5);
}
function rgbConvert(o4) {
  if (!(o4 instanceof Color)) o4 = color(o4);
  if (!o4) return new Rgb();
  o4 = o4.rgb();
  return new Rgb(o4.r, o4.g, o4.b, o4.opacity);
}
function rgb2(r5, g5, b5, opacity) {
  return arguments.length === 1 ? rgbConvert(r5) : new Rgb(r5, g5, b5, opacity == null ? 1 : opacity);
}
function Rgb(r5, g5, b5, opacity) {
  this.r = +r5;
  this.g = +g5;
  this.b = +b5;
  this.opacity = +opacity;
}
define_default(Rgb, rgb2, extend(Color, {
  brighter(k7) {
    k7 = k7 == null ? brighter : Math.pow(brighter, k7);
    return new Rgb(this.r * k7, this.g * k7, this.b * k7, this.opacity);
  },
  darker(k7) {
    k7 = k7 == null ? darker : Math.pow(darker, k7);
    return new Rgb(this.r * k7, this.g * k7, this.b * k7, this.opacity);
  },
  rgb() {
    return this;
  },
  clamp() {
    return new Rgb(clampi(this.r), clampi(this.g), clampi(this.b), clampa(this.opacity));
  },
  displayable() {
    return -0.5 <= this.r && this.r < 255.5 && (-0.5 <= this.g && this.g < 255.5) && (-0.5 <= this.b && this.b < 255.5) && (0 <= this.opacity && this.opacity <= 1);
  },
  hex: rgb_formatHex,
  // Deprecated! Use color.formatHex.
  formatHex: rgb_formatHex,
  formatHex8: rgb_formatHex8,
  formatRgb: rgb_formatRgb,
  toString: rgb_formatRgb
}));
function rgb_formatHex() {
  return `#${hex(this.r)}${hex(this.g)}${hex(this.b)}`;
}
function rgb_formatHex8() {
  return `#${hex(this.r)}${hex(this.g)}${hex(this.b)}${hex((isNaN(this.opacity) ? 1 : this.opacity) * 255)}`;
}
function rgb_formatRgb() {
  const a5 = clampa(this.opacity);
  return `${a5 === 1 ? "rgb(" : "rgba("}${clampi(this.r)}, ${clampi(this.g)}, ${clampi(this.b)}${a5 === 1 ? ")" : `, ${a5})`}`;
}
function clampa(opacity) {
  return isNaN(opacity) ? 1 : Math.max(0, Math.min(1, opacity));
}
function clampi(value) {
  return Math.max(0, Math.min(255, Math.round(value) || 0));
}
function hex(value) {
  value = clampi(value);
  return (value < 16 ? "0" : "") + value.toString(16);
}
function hsla2(h2, s4, l3, a5) {
  if (a5 <= 0) h2 = s4 = l3 = NaN;
  else if (l3 <= 0 || l3 >= 1) h2 = s4 = NaN;
  else if (s4 <= 0) h2 = NaN;
  return new Hsl(h2, s4, l3, a5);
}
function hslConvert(o4) {
  if (o4 instanceof Hsl) return new Hsl(o4.h, o4.s, o4.l, o4.opacity);
  if (!(o4 instanceof Color)) o4 = color(o4);
  if (!o4) return new Hsl();
  if (o4 instanceof Hsl) return o4;
  o4 = o4.rgb();
  var r5 = o4.r / 255, g5 = o4.g / 255, b5 = o4.b / 255, min3 = Math.min(r5, g5, b5), max3 = Math.max(r5, g5, b5), h2 = NaN, s4 = max3 - min3, l3 = (max3 + min3) / 2;
  if (s4) {
    if (r5 === max3) h2 = (g5 - b5) / s4 + (g5 < b5) * 6;
    else if (g5 === max3) h2 = (b5 - r5) / s4 + 2;
    else h2 = (r5 - g5) / s4 + 4;
    s4 /= l3 < 0.5 ? max3 + min3 : 2 - max3 - min3;
    h2 *= 60;
  } else {
    s4 = l3 > 0 && l3 < 1 ? 0 : h2;
  }
  return new Hsl(h2, s4, l3, o4.opacity);
}
function hsl2(h2, s4, l3, opacity) {
  return arguments.length === 1 ? hslConvert(h2) : new Hsl(h2, s4, l3, opacity == null ? 1 : opacity);
}
function Hsl(h2, s4, l3, opacity) {
  this.h = +h2;
  this.s = +s4;
  this.l = +l3;
  this.opacity = +opacity;
}
define_default(Hsl, hsl2, extend(Color, {
  brighter(k7) {
    k7 = k7 == null ? brighter : Math.pow(brighter, k7);
    return new Hsl(this.h, this.s, this.l * k7, this.opacity);
  },
  darker(k7) {
    k7 = k7 == null ? darker : Math.pow(darker, k7);
    return new Hsl(this.h, this.s, this.l * k7, this.opacity);
  },
  rgb() {
    var h2 = this.h % 360 + (this.h < 0) * 360, s4 = isNaN(h2) || isNaN(this.s) ? 0 : this.s, l3 = this.l, m22 = l3 + (l3 < 0.5 ? l3 : 1 - l3) * s4, m1 = 2 * l3 - m22;
    return new Rgb(
      hsl2rgb(h2 >= 240 ? h2 - 240 : h2 + 120, m1, m22),
      hsl2rgb(h2, m1, m22),
      hsl2rgb(h2 < 120 ? h2 + 240 : h2 - 120, m1, m22),
      this.opacity
    );
  },
  clamp() {
    return new Hsl(clamph(this.h), clampt(this.s), clampt(this.l), clampa(this.opacity));
  },
  displayable() {
    return (0 <= this.s && this.s <= 1 || isNaN(this.s)) && (0 <= this.l && this.l <= 1) && (0 <= this.opacity && this.opacity <= 1);
  },
  formatHsl() {
    const a5 = clampa(this.opacity);
    return `${a5 === 1 ? "hsl(" : "hsla("}${clamph(this.h)}, ${clampt(this.s) * 100}%, ${clampt(this.l) * 100}%${a5 === 1 ? ")" : `, ${a5})`}`;
  }
}));
function clamph(value) {
  value = (value || 0) % 360;
  return value < 0 ? value + 360 : value;
}
function clampt(value) {
  return Math.max(0, Math.min(1, value || 0));
}
function hsl2rgb(h2, m1, m22) {
  return (h2 < 60 ? m1 + (m22 - m1) * h2 / 60 : h2 < 180 ? m22 : h2 < 240 ? m1 + (m22 - m1) * (240 - h2) / 60 : m1) * 255;
}

// node_modules/d3-color/src/math.js
var radians = Math.PI / 180;
var degrees = 180 / Math.PI;

// node_modules/d3-color/src/lab.js
var K = 18;
var Xn = 0.96422;
var Yn = 1;
var Zn = 0.82521;
var t0 = 4 / 29;
var t1 = 6 / 29;
var t2 = 3 * t1 * t1;
var t3 = t1 * t1 * t1;
function labConvert(o4) {
  if (o4 instanceof Lab) return new Lab(o4.l, o4.a, o4.b, o4.opacity);
  if (o4 instanceof Hcl) return hcl2lab(o4);
  if (!(o4 instanceof Rgb)) o4 = rgbConvert(o4);
  var r5 = rgb2lrgb(o4.r), g5 = rgb2lrgb(o4.g), b5 = rgb2lrgb(o4.b), y5 = xyz2lab((0.2225045 * r5 + 0.7168786 * g5 + 0.0606169 * b5) / Yn), x5, z5;
  if (r5 === g5 && g5 === b5) x5 = z5 = y5;
  else {
    x5 = xyz2lab((0.4360747 * r5 + 0.3850649 * g5 + 0.1430804 * b5) / Xn);
    z5 = xyz2lab((0.0139322 * r5 + 0.0971045 * g5 + 0.7141733 * b5) / Zn);
  }
  return new Lab(116 * y5 - 16, 500 * (x5 - y5), 200 * (y5 - z5), o4.opacity);
}
function lab(l3, a5, b5, opacity) {
  return arguments.length === 1 ? labConvert(l3) : new Lab(l3, a5, b5, opacity == null ? 1 : opacity);
}
function Lab(l3, a5, b5, opacity) {
  this.l = +l3;
  this.a = +a5;
  this.b = +b5;
  this.opacity = +opacity;
}
define_default(Lab, lab, extend(Color, {
  brighter(k7) {
    return new Lab(this.l + K * (k7 == null ? 1 : k7), this.a, this.b, this.opacity);
  },
  darker(k7) {
    return new Lab(this.l - K * (k7 == null ? 1 : k7), this.a, this.b, this.opacity);
  },
  rgb() {
    var y5 = (this.l + 16) / 116, x5 = isNaN(this.a) ? y5 : y5 + this.a / 500, z5 = isNaN(this.b) ? y5 : y5 - this.b / 200;
    x5 = Xn * lab2xyz(x5);
    y5 = Yn * lab2xyz(y5);
    z5 = Zn * lab2xyz(z5);
    return new Rgb(
      lrgb2rgb(3.1338561 * x5 - 1.6168667 * y5 - 0.4906146 * z5),
      lrgb2rgb(-0.9787684 * x5 + 1.9161415 * y5 + 0.033454 * z5),
      lrgb2rgb(0.0719453 * x5 - 0.2289914 * y5 + 1.4052427 * z5),
      this.opacity
    );
  }
}));
function xyz2lab(t9) {
  return t9 > t3 ? Math.pow(t9, 1 / 3) : t9 / t2 + t0;
}
function lab2xyz(t9) {
  return t9 > t1 ? t9 * t9 * t9 : t2 * (t9 - t0);
}
function lrgb2rgb(x5) {
  return 255 * (x5 <= 31308e-7 ? 12.92 * x5 : 1.055 * Math.pow(x5, 1 / 2.4) - 0.055);
}
function rgb2lrgb(x5) {
  return (x5 /= 255) <= 0.04045 ? x5 / 12.92 : Math.pow((x5 + 0.055) / 1.055, 2.4);
}
function hclConvert(o4) {
  if (o4 instanceof Hcl) return new Hcl(o4.h, o4.c, o4.l, o4.opacity);
  if (!(o4 instanceof Lab)) o4 = labConvert(o4);
  if (o4.a === 0 && o4.b === 0) return new Hcl(NaN, 0 < o4.l && o4.l < 100 ? 0 : NaN, o4.l, o4.opacity);
  var h2 = Math.atan2(o4.b, o4.a) * degrees;
  return new Hcl(h2 < 0 ? h2 + 360 : h2, Math.sqrt(o4.a * o4.a + o4.b * o4.b), o4.l, o4.opacity);
}
function hcl(h2, c9, l3, opacity) {
  return arguments.length === 1 ? hclConvert(h2) : new Hcl(h2, c9, l3, opacity == null ? 1 : opacity);
}
function Hcl(h2, c9, l3, opacity) {
  this.h = +h2;
  this.c = +c9;
  this.l = +l3;
  this.opacity = +opacity;
}
function hcl2lab(o4) {
  if (isNaN(o4.h)) return new Lab(o4.l, 0, 0, o4.opacity);
  var h2 = o4.h * radians;
  return new Lab(o4.l, Math.cos(h2) * o4.c, Math.sin(h2) * o4.c, o4.opacity);
}
define_default(Hcl, hcl, extend(Color, {
  brighter(k7) {
    return new Hcl(this.h, this.c, this.l + K * (k7 == null ? 1 : k7), this.opacity);
  },
  darker(k7) {
    return new Hcl(this.h, this.c, this.l - K * (k7 == null ? 1 : k7), this.opacity);
  },
  rgb() {
    return hcl2lab(this).rgb();
  }
}));

// node_modules/d3-color/src/cubehelix.js
var A2 = -0.14861;
var B = 1.78277;
var C2 = -0.29227;
var D = -0.90649;
var E2 = 1.97294;
var ED = E2 * D;
var EB = E2 * B;
var BC_DA = B * C2 - D * A2;
function cubehelixConvert(o4) {
  if (o4 instanceof Cubehelix) return new Cubehelix(o4.h, o4.s, o4.l, o4.opacity);
  if (!(o4 instanceof Rgb)) o4 = rgbConvert(o4);
  var r5 = o4.r / 255, g5 = o4.g / 255, b5 = o4.b / 255, l3 = (BC_DA * b5 + ED * r5 - EB * g5) / (BC_DA + ED - EB), bl = b5 - l3, k7 = (E2 * (g5 - l3) - C2 * bl) / D, s4 = Math.sqrt(k7 * k7 + bl * bl) / (E2 * l3 * (1 - l3)), h2 = s4 ? Math.atan2(k7, bl) * degrees - 120 : NaN;
  return new Cubehelix(h2 < 0 ? h2 + 360 : h2, s4, l3, o4.opacity);
}
function cubehelix(h2, s4, l3, opacity) {
  return arguments.length === 1 ? cubehelixConvert(h2) : new Cubehelix(h2, s4, l3, opacity == null ? 1 : opacity);
}
function Cubehelix(h2, s4, l3, opacity) {
  this.h = +h2;
  this.s = +s4;
  this.l = +l3;
  this.opacity = +opacity;
}
define_default(Cubehelix, cubehelix, extend(Color, {
  brighter(k7) {
    k7 = k7 == null ? brighter : Math.pow(brighter, k7);
    return new Cubehelix(this.h, this.s, this.l * k7, this.opacity);
  },
  darker(k7) {
    k7 = k7 == null ? darker : Math.pow(darker, k7);
    return new Cubehelix(this.h, this.s, this.l * k7, this.opacity);
  },
  rgb() {
    var h2 = isNaN(this.h) ? 0 : (this.h + 120) * radians, l3 = +this.l, a5 = isNaN(this.s) ? 0 : this.s * l3 * (1 - l3), cosh2 = Math.cos(h2), sinh2 = Math.sin(h2);
    return new Rgb(
      255 * (l3 + a5 * (A2 * cosh2 + B * sinh2)),
      255 * (l3 + a5 * (C2 * cosh2 + D * sinh2)),
      255 * (l3 + a5 * (E2 * cosh2)),
      this.opacity
    );
  }
}));

// node_modules/d3-interpolate/src/basis.js
function basis(t14, v0, v1, v22, v32) {
  var t22 = t14 * t14, t32 = t22 * t14;
  return ((1 - 3 * t14 + 3 * t22 - t32) * v0 + (4 - 6 * t22 + 3 * t32) * v1 + (1 + 3 * t14 + 3 * t22 - 3 * t32) * v22 + t32 * v32) / 6;
}
function basis_default(values) {
  var n6 = values.length - 1;
  return function(t9) {
    var i5 = t9 <= 0 ? t9 = 0 : t9 >= 1 ? (t9 = 1, n6 - 1) : Math.floor(t9 * n6), v1 = values[i5], v22 = values[i5 + 1], v0 = i5 > 0 ? values[i5 - 1] : 2 * v1 - v22, v32 = i5 < n6 - 1 ? values[i5 + 2] : 2 * v22 - v1;
    return basis((t9 - i5 / n6) * n6, v0, v1, v22, v32);
  };
}

// node_modules/d3-interpolate/src/basisClosed.js
function basisClosed_default(values) {
  var n6 = values.length;
  return function(t9) {
    var i5 = Math.floor(((t9 %= 1) < 0 ? ++t9 : t9) * n6), v0 = values[(i5 + n6 - 1) % n6], v1 = values[i5 % n6], v22 = values[(i5 + 1) % n6], v32 = values[(i5 + 2) % n6];
    return basis((t9 - i5 / n6) * n6, v0, v1, v22, v32);
  };
}

// node_modules/d3-interpolate/src/constant.js
var constant_default = (x5) => () => x5;

// node_modules/d3-interpolate/src/color.js
function linear(a5, d3) {
  return function(t9) {
    return a5 + t9 * d3;
  };
}
function exponential(a5, b5, y5) {
  return a5 = Math.pow(a5, y5), b5 = Math.pow(b5, y5) - a5, y5 = 1 / y5, function(t9) {
    return Math.pow(a5 + t9 * b5, y5);
  };
}
function hue(a5, b5) {
  var d3 = b5 - a5;
  return d3 ? linear(a5, d3 > 180 || d3 < -180 ? d3 - 360 * Math.round(d3 / 360) : d3) : constant_default(isNaN(a5) ? b5 : a5);
}
function gamma(y5) {
  return (y5 = +y5) === 1 ? nogamma : function(a5, b5) {
    return b5 - a5 ? exponential(a5, b5, y5) : constant_default(isNaN(a5) ? b5 : a5);
  };
}
function nogamma(a5, b5) {
  var d3 = b5 - a5;
  return d3 ? linear(a5, d3) : constant_default(isNaN(a5) ? b5 : a5);
}

// node_modules/d3-interpolate/src/rgb.js
var rgb_default = function rgbGamma(y5) {
  var color2 = gamma(y5);
  function rgb3(start2, end) {
    var r5 = color2((start2 = rgb2(start2)).r, (end = rgb2(end)).r), g5 = color2(start2.g, end.g), b5 = color2(start2.b, end.b), opacity = nogamma(start2.opacity, end.opacity);
    return function(t9) {
      start2.r = r5(t9);
      start2.g = g5(t9);
      start2.b = b5(t9);
      start2.opacity = opacity(t9);
      return start2 + "";
    };
  }
  rgb3.gamma = rgbGamma;
  return rgb3;
}(1);
function rgbSpline(spline) {
  return function(colors3) {
    var n6 = colors3.length, r5 = new Array(n6), g5 = new Array(n6), b5 = new Array(n6), i5, color2;
    for (i5 = 0; i5 < n6; ++i5) {
      color2 = rgb2(colors3[i5]);
      r5[i5] = color2.r || 0;
      g5[i5] = color2.g || 0;
      b5[i5] = color2.b || 0;
    }
    r5 = spline(r5);
    g5 = spline(g5);
    b5 = spline(b5);
    color2.opacity = 1;
    return function(t9) {
      color2.r = r5(t9);
      color2.g = g5(t9);
      color2.b = b5(t9);
      return color2 + "";
    };
  };
}
var rgbBasis = rgbSpline(basis_default);
var rgbBasisClosed = rgbSpline(basisClosed_default);

// node_modules/d3-interpolate/src/numberArray.js
function numberArray_default(a5, b5) {
  if (!b5) b5 = [];
  var n6 = a5 ? Math.min(b5.length, a5.length) : 0, c9 = b5.slice(), i5;
  return function(t9) {
    for (i5 = 0; i5 < n6; ++i5) c9[i5] = a5[i5] * (1 - t9) + b5[i5] * t9;
    return c9;
  };
}
function isNumberArray(x5) {
  return ArrayBuffer.isView(x5) && !(x5 instanceof DataView);
}

// node_modules/d3-interpolate/src/array.js
function genericArray(a5, b5) {
  var nb = b5 ? b5.length : 0, na = a5 ? Math.min(nb, a5.length) : 0, x5 = new Array(na), c9 = new Array(nb), i5;
  for (i5 = 0; i5 < na; ++i5) x5[i5] = value_default(a5[i5], b5[i5]);
  for (; i5 < nb; ++i5) c9[i5] = b5[i5];
  return function(t9) {
    for (i5 = 0; i5 < na; ++i5) c9[i5] = x5[i5](t9);
    return c9;
  };
}

// node_modules/d3-interpolate/src/date.js
function date_default(a5, b5) {
  var d3 = /* @__PURE__ */ new Date();
  return a5 = +a5, b5 = +b5, function(t9) {
    return d3.setTime(a5 * (1 - t9) + b5 * t9), d3;
  };
}

// node_modules/d3-interpolate/src/number.js
function number_default(a5, b5) {
  return a5 = +a5, b5 = +b5, function(t9) {
    return a5 * (1 - t9) + b5 * t9;
  };
}

// node_modules/d3-interpolate/src/object.js
function object_default(a5, b5) {
  var i5 = {}, c9 = {}, k7;
  if (a5 === null || typeof a5 !== "object") a5 = {};
  if (b5 === null || typeof b5 !== "object") b5 = {};
  for (k7 in b5) {
    if (k7 in a5) {
      i5[k7] = value_default(a5[k7], b5[k7]);
    } else {
      c9[k7] = b5[k7];
    }
  }
  return function(t9) {
    for (k7 in i5) c9[k7] = i5[k7](t9);
    return c9;
  };
}

// node_modules/d3-interpolate/src/string.js
var reA = /[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g;
var reB = new RegExp(reA.source, "g");
function zero(b5) {
  return function() {
    return b5;
  };
}
function one(b5) {
  return function(t9) {
    return b5(t9) + "";
  };
}
function string_default(a5, b5) {
  var bi = reA.lastIndex = reB.lastIndex = 0, am, bm, bs, i5 = -1, s4 = [], q4 = [];
  a5 = a5 + "", b5 = b5 + "";
  while ((am = reA.exec(a5)) && (bm = reB.exec(b5))) {
    if ((bs = bm.index) > bi) {
      bs = b5.slice(bi, bs);
      if (s4[i5]) s4[i5] += bs;
      else s4[++i5] = bs;
    }
    if ((am = am[0]) === (bm = bm[0])) {
      if (s4[i5]) s4[i5] += bm;
      else s4[++i5] = bm;
    } else {
      s4[++i5] = null;
      q4.push({ i: i5, x: number_default(am, bm) });
    }
    bi = reB.lastIndex;
  }
  if (bi < b5.length) {
    bs = b5.slice(bi);
    if (s4[i5]) s4[i5] += bs;
    else s4[++i5] = bs;
  }
  return s4.length < 2 ? q4[0] ? one(q4[0].x) : zero(b5) : (b5 = q4.length, function(t9) {
    for (var i6 = 0, o4; i6 < b5; ++i6) s4[(o4 = q4[i6]).i] = o4.x(t9);
    return s4.join("");
  });
}

// node_modules/d3-interpolate/src/value.js
function value_default(a5, b5) {
  var t9 = typeof b5, c9;
  return b5 == null || t9 === "boolean" ? constant_default(b5) : (t9 === "number" ? number_default : t9 === "string" ? (c9 = color(b5)) ? (b5 = c9, rgb_default) : string_default : b5 instanceof color ? rgb_default : b5 instanceof Date ? date_default : isNumberArray(b5) ? numberArray_default : Array.isArray(b5) ? genericArray : typeof b5.valueOf !== "function" && typeof b5.toString !== "function" || isNaN(b5) ? object_default : number_default)(a5, b5);
}

// node_modules/d3-interpolate/src/round.js
function round_default(a5, b5) {
  return a5 = +a5, b5 = +b5, function(t9) {
    return Math.round(a5 * (1 - t9) + b5 * t9);
  };
}

// node_modules/d3-interpolate/src/transform/decompose.js
var degrees2 = 180 / Math.PI;
var identity = {
  translateX: 0,
  translateY: 0,
  rotate: 0,
  skewX: 0,
  scaleX: 1,
  scaleY: 1
};
function decompose_default(a5, b5, c9, d3, e11, f3) {
  var scaleX, scaleY, skewX;
  if (scaleX = Math.sqrt(a5 * a5 + b5 * b5)) a5 /= scaleX, b5 /= scaleX;
  if (skewX = a5 * c9 + b5 * d3) c9 -= a5 * skewX, d3 -= b5 * skewX;
  if (scaleY = Math.sqrt(c9 * c9 + d3 * d3)) c9 /= scaleY, d3 /= scaleY, skewX /= scaleY;
  if (a5 * d3 < b5 * c9) a5 = -a5, b5 = -b5, skewX = -skewX, scaleX = -scaleX;
  return {
    translateX: e11,
    translateY: f3,
    rotate: Math.atan2(b5, a5) * degrees2,
    skewX: Math.atan(skewX) * degrees2,
    scaleX,
    scaleY
  };
}

// node_modules/d3-interpolate/src/transform/parse.js
var svgNode;
function parseCss(value) {
  const m5 = new (typeof DOMMatrix === "function" ? DOMMatrix : WebKitCSSMatrix)(value + "");
  return m5.isIdentity ? identity : decompose_default(m5.a, m5.b, m5.c, m5.d, m5.e, m5.f);
}
function parseSvg(value) {
  if (value == null) return identity;
  if (!svgNode) svgNode = document.createElementNS("http://www.w3.org/2000/svg", "g");
  svgNode.setAttribute("transform", value);
  if (!(value = svgNode.transform.baseVal.consolidate())) return identity;
  value = value.matrix;
  return decompose_default(value.a, value.b, value.c, value.d, value.e, value.f);
}

// node_modules/d3-interpolate/src/transform/index.js
function interpolateTransform(parse, pxComma, pxParen, degParen) {
  function pop(s4) {
    return s4.length ? s4.pop() + " " : "";
  }
  function translate(xa, ya, xb, yb, s4, q4) {
    if (xa !== xb || ya !== yb) {
      var i5 = s4.push("translate(", null, pxComma, null, pxParen);
      q4.push({ i: i5 - 4, x: number_default(xa, xb) }, { i: i5 - 2, x: number_default(ya, yb) });
    } else if (xb || yb) {
      s4.push("translate(" + xb + pxComma + yb + pxParen);
    }
  }
  function rotate(a5, b5, s4, q4) {
    if (a5 !== b5) {
      if (a5 - b5 > 180) b5 += 360;
      else if (b5 - a5 > 180) a5 += 360;
      q4.push({ i: s4.push(pop(s4) + "rotate(", null, degParen) - 2, x: number_default(a5, b5) });
    } else if (b5) {
      s4.push(pop(s4) + "rotate(" + b5 + degParen);
    }
  }
  function skewX(a5, b5, s4, q4) {
    if (a5 !== b5) {
      q4.push({ i: s4.push(pop(s4) + "skewX(", null, degParen) - 2, x: number_default(a5, b5) });
    } else if (b5) {
      s4.push(pop(s4) + "skewX(" + b5 + degParen);
    }
  }
  function scale(xa, ya, xb, yb, s4, q4) {
    if (xa !== xb || ya !== yb) {
      var i5 = s4.push(pop(s4) + "scale(", null, ",", null, ")");
      q4.push({ i: i5 - 4, x: number_default(xa, xb) }, { i: i5 - 2, x: number_default(ya, yb) });
    } else if (xb !== 1 || yb !== 1) {
      s4.push(pop(s4) + "scale(" + xb + "," + yb + ")");
    }
  }
  return function(a5, b5) {
    var s4 = [], q4 = [];
    a5 = parse(a5), b5 = parse(b5);
    translate(a5.translateX, a5.translateY, b5.translateX, b5.translateY, s4, q4);
    rotate(a5.rotate, b5.rotate, s4, q4);
    skewX(a5.skewX, b5.skewX, s4, q4);
    scale(a5.scaleX, a5.scaleY, b5.scaleX, b5.scaleY, s4, q4);
    a5 = b5 = null;
    return function(t9) {
      var i5 = -1, n6 = q4.length, o4;
      while (++i5 < n6) s4[(o4 = q4[i5]).i] = o4.x(t9);
      return s4.join("");
    };
  };
}
var interpolateTransformCss = interpolateTransform(parseCss, "px, ", "px)", "deg)");
var interpolateTransformSvg = interpolateTransform(parseSvg, ", ", ")", ")");

// node_modules/d3-interpolate/src/zoom.js
var epsilon2 = 1e-12;
function cosh(x5) {
  return ((x5 = Math.exp(x5)) + 1 / x5) / 2;
}
function sinh(x5) {
  return ((x5 = Math.exp(x5)) - 1 / x5) / 2;
}
function tanh(x5) {
  return ((x5 = Math.exp(2 * x5)) - 1) / (x5 + 1);
}
var zoom_default = function zoomRho(rho, rho2, rho4) {
  function zoom(p0, p1) {
    var ux0 = p0[0], uy0 = p0[1], w0 = p0[2], ux1 = p1[0], uy1 = p1[1], w1 = p1[2], dx = ux1 - ux0, dy = uy1 - uy0, d22 = dx * dx + dy * dy, i5, S5;
    if (d22 < epsilon2) {
      S5 = Math.log(w1 / w0) / rho;
      i5 = function(t9) {
        return [
          ux0 + t9 * dx,
          uy0 + t9 * dy,
          w0 * Math.exp(rho * t9 * S5)
        ];
      };
    } else {
      var d1 = Math.sqrt(d22), b0 = (w1 * w1 - w0 * w0 + rho4 * d22) / (2 * w0 * rho2 * d1), b1 = (w1 * w1 - w0 * w0 - rho4 * d22) / (2 * w1 * rho2 * d1), r0 = Math.log(Math.sqrt(b0 * b0 + 1) - b0), r1 = Math.log(Math.sqrt(b1 * b1 + 1) - b1);
      S5 = (r1 - r0) / rho;
      i5 = function(t9) {
        var s4 = t9 * S5, coshr0 = cosh(r0), u3 = w0 / (rho2 * d1) * (coshr0 * tanh(rho * s4 + r0) - sinh(r0));
        return [
          ux0 + u3 * dx,
          uy0 + u3 * dy,
          w0 * coshr0 / cosh(rho * s4 + r0)
        ];
      };
    }
    i5.duration = S5 * 1e3 * rho / Math.SQRT2;
    return i5;
  }
  zoom.rho = function(_3) {
    var _1 = Math.max(1e-3, +_3), _22 = _1 * _1, _4 = _22 * _22;
    return zoomRho(_1, _22, _4);
  };
  return zoom;
}(Math.SQRT2, 2, 4);

// node_modules/d3-interpolate/src/hsl.js
function hsl3(hue2) {
  return function(start2, end) {
    var h2 = hue2((start2 = hsl2(start2)).h, (end = hsl2(end)).h), s4 = nogamma(start2.s, end.s), l3 = nogamma(start2.l, end.l), opacity = nogamma(start2.opacity, end.opacity);
    return function(t9) {
      start2.h = h2(t9);
      start2.s = s4(t9);
      start2.l = l3(t9);
      start2.opacity = opacity(t9);
      return start2 + "";
    };
  };
}
var hsl_default = hsl3(hue);
var hslLong = hsl3(nogamma);

// node_modules/d3-interpolate/src/hcl.js
function hcl2(hue2) {
  return function(start2, end) {
    var h2 = hue2((start2 = hcl(start2)).h, (end = hcl(end)).h), c9 = nogamma(start2.c, end.c), l3 = nogamma(start2.l, end.l), opacity = nogamma(start2.opacity, end.opacity);
    return function(t9) {
      start2.h = h2(t9);
      start2.c = c9(t9);
      start2.l = l3(t9);
      start2.opacity = opacity(t9);
      return start2 + "";
    };
  };
}
var hcl_default = hcl2(hue);
var hclLong = hcl2(nogamma);

// node_modules/d3-interpolate/src/cubehelix.js
function cubehelix2(hue2) {
  return function cubehelixGamma(y5) {
    y5 = +y5;
    function cubehelix3(start2, end) {
      var h2 = hue2((start2 = cubehelix(start2)).h, (end = cubehelix(end)).h), s4 = nogamma(start2.s, end.s), l3 = nogamma(start2.l, end.l), opacity = nogamma(start2.opacity, end.opacity);
      return function(t9) {
        start2.h = h2(t9);
        start2.s = s4(t9);
        start2.l = l3(Math.pow(t9, y5));
        start2.opacity = opacity(t9);
        return start2 + "";
      };
    }
    cubehelix3.gamma = cubehelixGamma;
    return cubehelix3;
  }(1);
}
var cubehelix_default = cubehelix2(hue);
var cubehelixLong = cubehelix2(nogamma);

// node_modules/@nivo/core/dist/nivo-core.es.js
var import_isString = __toESM(require_isString());
var import_jsx_runtime2 = __toESM(require_jsx_runtime());
var import_last = __toESM(require_last());
var import_isArray = __toESM(require_isArray());

// node_modules/d3-array/src/ascending.js
function ascending(a5, b5) {
  return a5 == null || b5 == null ? NaN : a5 < b5 ? -1 : a5 > b5 ? 1 : a5 >= b5 ? 0 : NaN;
}

// node_modules/d3-array/src/descending.js
function descending(a5, b5) {
  return a5 == null || b5 == null ? NaN : b5 < a5 ? -1 : b5 > a5 ? 1 : b5 >= a5 ? 0 : NaN;
}

// node_modules/d3-array/src/bisector.js
function bisector(f3) {
  let compare1, compare2, delta;
  if (f3.length !== 2) {
    compare1 = ascending;
    compare2 = (d3, x5) => ascending(f3(d3), x5);
    delta = (d3, x5) => f3(d3) - x5;
  } else {
    compare1 = f3 === ascending || f3 === descending ? f3 : zero2;
    compare2 = f3;
    delta = f3;
  }
  function left(a5, x5, lo = 0, hi = a5.length) {
    if (lo < hi) {
      if (compare1(x5, x5) !== 0) return hi;
      do {
        const mid = lo + hi >>> 1;
        if (compare2(a5[mid], x5) < 0) lo = mid + 1;
        else hi = mid;
      } while (lo < hi);
    }
    return lo;
  }
  function right(a5, x5, lo = 0, hi = a5.length) {
    if (lo < hi) {
      if (compare1(x5, x5) !== 0) return hi;
      do {
        const mid = lo + hi >>> 1;
        if (compare2(a5[mid], x5) <= 0) lo = mid + 1;
        else hi = mid;
      } while (lo < hi);
    }
    return lo;
  }
  function center(a5, x5, lo = 0, hi = a5.length) {
    const i5 = left(a5, x5, lo, hi - 1);
    return i5 > lo && delta(a5[i5 - 1], x5) > -delta(a5[i5], x5) ? i5 - 1 : i5;
  }
  return { left, center, right };
}
function zero2() {
  return 0;
}

// node_modules/d3-array/src/number.js
function number(x5) {
  return x5 === null ? NaN : +x5;
}

// node_modules/d3-array/src/bisect.js
var ascendingBisect = bisector(ascending);
var bisectRight = ascendingBisect.right;
var bisectLeft = ascendingBisect.left;
var bisectCenter = bisector(number).center;
var bisect_default = bisectRight;

// node_modules/d3-array/src/blur.js
var blur2 = Blur2(blurf);
var blurImage = Blur2(blurfImage);
function Blur2(blur3) {
  return function(data, rx, ry = rx) {
    if (!((rx = +rx) >= 0)) throw new RangeError("invalid rx");
    if (!((ry = +ry) >= 0)) throw new RangeError("invalid ry");
    let { data: values, width, height } = data;
    if (!((width = Math.floor(width)) >= 0)) throw new RangeError("invalid width");
    if (!((height = Math.floor(height !== void 0 ? height : values.length / width)) >= 0)) throw new RangeError("invalid height");
    if (!width || !height || !rx && !ry) return data;
    const blurx = rx && blur3(rx);
    const blury = ry && blur3(ry);
    const temp = values.slice();
    if (blurx && blury) {
      blurh(blurx, temp, values, width, height);
      blurh(blurx, values, temp, width, height);
      blurh(blurx, temp, values, width, height);
      blurv(blury, values, temp, width, height);
      blurv(blury, temp, values, width, height);
      blurv(blury, values, temp, width, height);
    } else if (blurx) {
      blurh(blurx, values, temp, width, height);
      blurh(blurx, temp, values, width, height);
      blurh(blurx, values, temp, width, height);
    } else if (blury) {
      blurv(blury, values, temp, width, height);
      blurv(blury, temp, values, width, height);
      blurv(blury, values, temp, width, height);
    }
    return data;
  };
}
function blurh(blur3, T4, S5, w6, h2) {
  for (let y5 = 0, n6 = w6 * h2; y5 < n6; ) {
    blur3(T4, S5, y5, y5 += w6, 1);
  }
}
function blurv(blur3, T4, S5, w6, h2) {
  for (let x5 = 0, n6 = w6 * h2; x5 < w6; ++x5) {
    blur3(T4, S5, x5, x5 + n6, w6);
  }
}
function blurfImage(radius) {
  const blur3 = blurf(radius);
  return (T4, S5, start2, stop2, step) => {
    start2 <<= 2, stop2 <<= 2, step <<= 2;
    blur3(T4, S5, start2 + 0, stop2 + 0, step);
    blur3(T4, S5, start2 + 1, stop2 + 1, step);
    blur3(T4, S5, start2 + 2, stop2 + 2, step);
    blur3(T4, S5, start2 + 3, stop2 + 3, step);
  };
}
function blurf(radius) {
  const radius0 = Math.floor(radius);
  if (radius0 === radius) return bluri(radius);
  const t9 = radius - radius0;
  const w6 = 2 * radius + 1;
  return (T4, S5, start2, stop2, step) => {
    if (!((stop2 -= step) >= start2)) return;
    let sum3 = radius0 * S5[start2];
    const s0 = step * radius0;
    const s1 = s0 + step;
    for (let i5 = start2, j5 = start2 + s0; i5 < j5; i5 += step) {
      sum3 += S5[Math.min(stop2, i5)];
    }
    for (let i5 = start2, j5 = stop2; i5 <= j5; i5 += step) {
      sum3 += S5[Math.min(stop2, i5 + s0)];
      T4[i5] = (sum3 + t9 * (S5[Math.max(start2, i5 - s1)] + S5[Math.min(stop2, i5 + s1)])) / w6;
      sum3 -= S5[Math.max(start2, i5 - s0)];
    }
  };
}
function bluri(radius) {
  const w6 = 2 * radius + 1;
  return (T4, S5, start2, stop2, step) => {
    if (!((stop2 -= step) >= start2)) return;
    let sum3 = radius * S5[start2];
    const s4 = step * radius;
    for (let i5 = start2, j5 = start2 + s4; i5 < j5; i5 += step) {
      sum3 += S5[Math.min(stop2, i5)];
    }
    for (let i5 = start2, j5 = stop2; i5 <= j5; i5 += step) {
      sum3 += S5[Math.min(stop2, i5 + s4)];
      T4[i5] = sum3 / w6;
      sum3 -= S5[Math.max(start2, i5 - s4)];
    }
  };
}

// node_modules/internmap/src/index.js
var InternMap = class extends Map {
  constructor(entries, key = keyof) {
    super();
    Object.defineProperties(this, { _intern: { value: /* @__PURE__ */ new Map() }, _key: { value: key } });
    if (entries != null) for (const [key2, value] of entries) this.set(key2, value);
  }
  get(key) {
    return super.get(intern_get(this, key));
  }
  has(key) {
    return super.has(intern_get(this, key));
  }
  set(key, value) {
    return super.set(intern_set(this, key), value);
  }
  delete(key) {
    return super.delete(intern_delete(this, key));
  }
};
function intern_get({ _intern, _key }, value) {
  const key = _key(value);
  return _intern.has(key) ? _intern.get(key) : value;
}
function intern_set({ _intern, _key }, value) {
  const key = _key(value);
  if (_intern.has(key)) return _intern.get(key);
  _intern.set(key, value);
  return value;
}
function intern_delete({ _intern, _key }, value) {
  const key = _key(value);
  if (_intern.has(key)) {
    value = _intern.get(key);
    _intern.delete(key);
  }
  return value;
}
function keyof(value) {
  return value !== null && typeof value === "object" ? value.valueOf() : value;
}

// node_modules/d3-array/src/array.js
var array = Array.prototype;
var slice = array.slice;
var map = array.map;

// node_modules/d3-array/src/ticks.js
var e10 = Math.sqrt(50);
var e5 = Math.sqrt(10);
var e2 = Math.sqrt(2);
function tickSpec(start2, stop2, count2) {
  const step = (stop2 - start2) / Math.max(0, count2), power = Math.floor(Math.log10(step)), error = step / Math.pow(10, power), factor = error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1;
  let i1, i22, inc;
  if (power < 0) {
    inc = Math.pow(10, -power) / factor;
    i1 = Math.round(start2 * inc);
    i22 = Math.round(stop2 * inc);
    if (i1 / inc < start2) ++i1;
    if (i22 / inc > stop2) --i22;
    inc = -inc;
  } else {
    inc = Math.pow(10, power) * factor;
    i1 = Math.round(start2 / inc);
    i22 = Math.round(stop2 / inc);
    if (i1 * inc < start2) ++i1;
    if (i22 * inc > stop2) --i22;
  }
  if (i22 < i1 && 0.5 <= count2 && count2 < 2) return tickSpec(start2, stop2, count2 * 2);
  return [i1, i22, inc];
}
function ticks(start2, stop2, count2) {
  stop2 = +stop2, start2 = +start2, count2 = +count2;
  if (!(count2 > 0)) return [];
  if (start2 === stop2) return [start2];
  const reverse2 = stop2 < start2, [i1, i22, inc] = reverse2 ? tickSpec(stop2, start2, count2) : tickSpec(start2, stop2, count2);
  if (!(i22 >= i1)) return [];
  const n6 = i22 - i1 + 1, ticks2 = new Array(n6);
  if (reverse2) {
    if (inc < 0) for (let i5 = 0; i5 < n6; ++i5) ticks2[i5] = (i22 - i5) / -inc;
    else for (let i5 = 0; i5 < n6; ++i5) ticks2[i5] = (i22 - i5) * inc;
  } else {
    if (inc < 0) for (let i5 = 0; i5 < n6; ++i5) ticks2[i5] = (i1 + i5) / -inc;
    else for (let i5 = 0; i5 < n6; ++i5) ticks2[i5] = (i1 + i5) * inc;
  }
  return ticks2;
}
function tickIncrement(start2, stop2, count2) {
  stop2 = +stop2, start2 = +start2, count2 = +count2;
  return tickSpec(start2, stop2, count2)[2];
}
function tickStep(start2, stop2, count2) {
  stop2 = +stop2, start2 = +start2, count2 = +count2;
  const reverse2 = stop2 < start2, inc = reverse2 ? tickIncrement(stop2, start2, count2) : tickIncrement(start2, stop2, count2);
  return (reverse2 ? -1 : 1) * (inc < 0 ? 1 / -inc : inc);
}

// node_modules/d3-array/src/range.js
function range(start2, stop2, step) {
  start2 = +start2, stop2 = +stop2, step = (n6 = arguments.length) < 2 ? (stop2 = start2, start2 = 0, 1) : n6 < 3 ? 1 : +step;
  var i5 = -1, n6 = Math.max(0, Math.ceil((stop2 - start2) / step)) | 0, range2 = new Array(n6);
  while (++i5 < n6) {
    range2[i5] = start2 + i5 * step;
  }
  return range2;
}

// node_modules/d3-array/src/shuffle.js
var shuffle_default = shuffler(Math.random);
function shuffler(random) {
  return function shuffle(array2, i0 = 0, i1 = array2.length) {
    let m5 = i1 - (i0 = +i0);
    while (m5) {
      const i5 = random() * m5-- | 0, t9 = array2[m5 + i0];
      array2[m5 + i0] = array2[i5 + i0];
      array2[i5 + i0] = t9;
    }
    return array2;
  };
}

// node_modules/d3-scale/src/init.js
function initRange(domain, range2) {
  switch (arguments.length) {
    case 0:
      break;
    case 1:
      this.range(domain);
      break;
    default:
      this.range(range2).domain(domain);
      break;
  }
  return this;
}

// node_modules/d3-scale/src/ordinal.js
var implicit = Symbol("implicit");
function ordinal() {
  var index2 = new InternMap(), domain = [], range2 = [], unknown = implicit;
  function scale(d3) {
    let i5 = index2.get(d3);
    if (i5 === void 0) {
      if (unknown !== implicit) return unknown;
      index2.set(d3, i5 = domain.push(d3) - 1);
    }
    return range2[i5 % range2.length];
  }
  scale.domain = function(_3) {
    if (!arguments.length) return domain.slice();
    domain = [], index2 = new InternMap();
    for (const value of _3) {
      if (index2.has(value)) continue;
      index2.set(value, domain.push(value) - 1);
    }
    return scale;
  };
  scale.range = function(_3) {
    return arguments.length ? (range2 = Array.from(_3), scale) : range2.slice();
  };
  scale.unknown = function(_3) {
    return arguments.length ? (unknown = _3, scale) : unknown;
  };
  scale.copy = function() {
    return ordinal(domain, range2).unknown(unknown);
  };
  initRange.apply(scale, arguments);
  return scale;
}

// node_modules/d3-scale/src/band.js
function band() {
  var scale = ordinal().unknown(void 0), domain = scale.domain, ordinalRange = scale.range, r0 = 0, r1 = 1, step, bandwidth, round = false, paddingInner = 0, paddingOuter = 0, align = 0.5;
  delete scale.unknown;
  function rescale() {
    var n6 = domain().length, reverse2 = r1 < r0, start2 = reverse2 ? r1 : r0, stop2 = reverse2 ? r0 : r1;
    step = (stop2 - start2) / Math.max(1, n6 - paddingInner + paddingOuter * 2);
    if (round) step = Math.floor(step);
    start2 += (stop2 - start2 - step * (n6 - paddingInner)) * align;
    bandwidth = step * (1 - paddingInner);
    if (round) start2 = Math.round(start2), bandwidth = Math.round(bandwidth);
    var values = range(n6).map(function(i5) {
      return start2 + step * i5;
    });
    return ordinalRange(reverse2 ? values.reverse() : values);
  }
  scale.domain = function(_3) {
    return arguments.length ? (domain(_3), rescale()) : domain();
  };
  scale.range = function(_3) {
    return arguments.length ? ([r0, r1] = _3, r0 = +r0, r1 = +r1, rescale()) : [r0, r1];
  };
  scale.rangeRound = function(_3) {
    return [r0, r1] = _3, r0 = +r0, r1 = +r1, round = true, rescale();
  };
  scale.bandwidth = function() {
    return bandwidth;
  };
  scale.step = function() {
    return step;
  };
  scale.round = function(_3) {
    return arguments.length ? (round = !!_3, rescale()) : round;
  };
  scale.padding = function(_3) {
    return arguments.length ? (paddingInner = Math.min(1, paddingOuter = +_3), rescale()) : paddingInner;
  };
  scale.paddingInner = function(_3) {
    return arguments.length ? (paddingInner = Math.min(1, _3), rescale()) : paddingInner;
  };
  scale.paddingOuter = function(_3) {
    return arguments.length ? (paddingOuter = +_3, rescale()) : paddingOuter;
  };
  scale.align = function(_3) {
    return arguments.length ? (align = Math.max(0, Math.min(1, _3)), rescale()) : align;
  };
  scale.copy = function() {
    return band(domain(), [r0, r1]).round(round).paddingInner(paddingInner).paddingOuter(paddingOuter).align(align);
  };
  return initRange.apply(rescale(), arguments);
}
function pointish(scale) {
  var copy3 = scale.copy;
  scale.padding = scale.paddingOuter;
  delete scale.paddingInner;
  delete scale.paddingOuter;
  scale.copy = function() {
    return pointish(copy3());
  };
  return scale;
}
function point() {
  return pointish(band.apply(null, arguments).paddingInner(1));
}

// node_modules/d3-scale/src/constant.js
function constants(x5) {
  return function() {
    return x5;
  };
}

// node_modules/d3-scale/src/number.js
function number2(x5) {
  return +x5;
}

// node_modules/d3-scale/src/continuous.js
var unit = [0, 1];
function identity3(x5) {
  return x5;
}
function normalize(a5, b5) {
  return (b5 -= a5 = +a5) ? function(x5) {
    return (x5 - a5) / b5;
  } : constants(isNaN(b5) ? NaN : 0.5);
}
function clamper(a5, b5) {
  var t9;
  if (a5 > b5) t9 = a5, a5 = b5, b5 = t9;
  return function(x5) {
    return Math.max(a5, Math.min(b5, x5));
  };
}
function bimap(domain, range2, interpolate2) {
  var d0 = domain[0], d1 = domain[1], r0 = range2[0], r1 = range2[1];
  if (d1 < d0) d0 = normalize(d1, d0), r0 = interpolate2(r1, r0);
  else d0 = normalize(d0, d1), r0 = interpolate2(r0, r1);
  return function(x5) {
    return r0(d0(x5));
  };
}
function polymap(domain, range2, interpolate2) {
  var j5 = Math.min(domain.length, range2.length) - 1, d3 = new Array(j5), r5 = new Array(j5), i5 = -1;
  if (domain[j5] < domain[0]) {
    domain = domain.slice().reverse();
    range2 = range2.slice().reverse();
  }
  while (++i5 < j5) {
    d3[i5] = normalize(domain[i5], domain[i5 + 1]);
    r5[i5] = interpolate2(range2[i5], range2[i5 + 1]);
  }
  return function(x5) {
    var i6 = bisect_default(domain, x5, 1, j5) - 1;
    return r5[i6](d3[i6](x5));
  };
}
function copy(source, target) {
  return target.domain(source.domain()).range(source.range()).interpolate(source.interpolate()).clamp(source.clamp()).unknown(source.unknown());
}
function transformer() {
  var domain = unit, range2 = unit, interpolate2 = value_default, transform, untransform, unknown, clamp2 = identity3, piecewise2, output, input;
  function rescale() {
    var n6 = Math.min(domain.length, range2.length);
    if (clamp2 !== identity3) clamp2 = clamper(domain[0], domain[n6 - 1]);
    piecewise2 = n6 > 2 ? polymap : bimap;
    output = input = null;
    return scale;
  }
  function scale(x5) {
    return x5 == null || isNaN(x5 = +x5) ? unknown : (output || (output = piecewise2(domain.map(transform), range2, interpolate2)))(transform(clamp2(x5)));
  }
  scale.invert = function(y5) {
    return clamp2(untransform((input || (input = piecewise2(range2, domain.map(transform), number_default)))(y5)));
  };
  scale.domain = function(_3) {
    return arguments.length ? (domain = Array.from(_3, number2), rescale()) : domain.slice();
  };
  scale.range = function(_3) {
    return arguments.length ? (range2 = Array.from(_3), rescale()) : range2.slice();
  };
  scale.rangeRound = function(_3) {
    return range2 = Array.from(_3), interpolate2 = round_default, rescale();
  };
  scale.clamp = function(_3) {
    return arguments.length ? (clamp2 = _3 ? true : identity3, rescale()) : clamp2 !== identity3;
  };
  scale.interpolate = function(_3) {
    return arguments.length ? (interpolate2 = _3, rescale()) : interpolate2;
  };
  scale.unknown = function(_3) {
    return arguments.length ? (unknown = _3, scale) : unknown;
  };
  return function(t9, u3) {
    transform = t9, untransform = u3;
    return rescale();
  };
}
function continuous() {
  return transformer()(identity3, identity3);
}

// node_modules/d3-format/src/formatDecimal.js
function formatDecimal_default(x5) {
  return Math.abs(x5 = Math.round(x5)) >= 1e21 ? x5.toLocaleString("en").replace(/,/g, "") : x5.toString(10);
}
function formatDecimalParts(x5, p5) {
  if ((i5 = (x5 = p5 ? x5.toExponential(p5 - 1) : x5.toExponential()).indexOf("e")) < 0) return null;
  var i5, coefficient = x5.slice(0, i5);
  return [
    coefficient.length > 1 ? coefficient[0] + coefficient.slice(2) : coefficient,
    +x5.slice(i5 + 1)
  ];
}

// node_modules/d3-format/src/exponent.js
function exponent_default(x5) {
  return x5 = formatDecimalParts(Math.abs(x5)), x5 ? x5[1] : NaN;
}

// node_modules/d3-format/src/formatGroup.js
function formatGroup_default(grouping, thousands) {
  return function(value, width) {
    var i5 = value.length, t9 = [], j5 = 0, g5 = grouping[0], length = 0;
    while (i5 > 0 && g5 > 0) {
      if (length + g5 + 1 > width) g5 = Math.max(1, width - length);
      t9.push(value.substring(i5 -= g5, i5 + g5));
      if ((length += g5 + 1) > width) break;
      g5 = grouping[j5 = (j5 + 1) % grouping.length];
    }
    return t9.reverse().join(thousands);
  };
}

// node_modules/d3-format/src/formatNumerals.js
function formatNumerals_default(numerals) {
  return function(value) {
    return value.replace(/[0-9]/g, function(i5) {
      return numerals[+i5];
    });
  };
}

// node_modules/d3-format/src/formatSpecifier.js
var re = /^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;
function formatSpecifier(specifier) {
  if (!(match = re.exec(specifier))) throw new Error("invalid format: " + specifier);
  var match;
  return new FormatSpecifier({
    fill: match[1],
    align: match[2],
    sign: match[3],
    symbol: match[4],
    zero: match[5],
    width: match[6],
    comma: match[7],
    precision: match[8] && match[8].slice(1),
    trim: match[9],
    type: match[10]
  });
}
formatSpecifier.prototype = FormatSpecifier.prototype;
function FormatSpecifier(specifier) {
  this.fill = specifier.fill === void 0 ? " " : specifier.fill + "";
  this.align = specifier.align === void 0 ? ">" : specifier.align + "";
  this.sign = specifier.sign === void 0 ? "-" : specifier.sign + "";
  this.symbol = specifier.symbol === void 0 ? "" : specifier.symbol + "";
  this.zero = !!specifier.zero;
  this.width = specifier.width === void 0 ? void 0 : +specifier.width;
  this.comma = !!specifier.comma;
  this.precision = specifier.precision === void 0 ? void 0 : +specifier.precision;
  this.trim = !!specifier.trim;
  this.type = specifier.type === void 0 ? "" : specifier.type + "";
}
FormatSpecifier.prototype.toString = function() {
  return this.fill + this.align + this.sign + this.symbol + (this.zero ? "0" : "") + (this.width === void 0 ? "" : Math.max(1, this.width | 0)) + (this.comma ? "," : "") + (this.precision === void 0 ? "" : "." + Math.max(0, this.precision | 0)) + (this.trim ? "~" : "") + this.type;
};

// node_modules/d3-format/src/formatTrim.js
function formatTrim_default(s4) {
  out: for (var n6 = s4.length, i5 = 1, i0 = -1, i1; i5 < n6; ++i5) {
    switch (s4[i5]) {
      case ".":
        i0 = i1 = i5;
        break;
      case "0":
        if (i0 === 0) i0 = i5;
        i1 = i5;
        break;
      default:
        if (!+s4[i5]) break out;
        if (i0 > 0) i0 = 0;
        break;
    }
  }
  return i0 > 0 ? s4.slice(0, i0) + s4.slice(i1 + 1) : s4;
}

// node_modules/d3-format/src/formatPrefixAuto.js
var prefixExponent;
function formatPrefixAuto_default(x5, p5) {
  var d3 = formatDecimalParts(x5, p5);
  if (!d3) return x5 + "";
  var coefficient = d3[0], exponent = d3[1], i5 = exponent - (prefixExponent = Math.max(-8, Math.min(8, Math.floor(exponent / 3))) * 3) + 1, n6 = coefficient.length;
  return i5 === n6 ? coefficient : i5 > n6 ? coefficient + new Array(i5 - n6 + 1).join("0") : i5 > 0 ? coefficient.slice(0, i5) + "." + coefficient.slice(i5) : "0." + new Array(1 - i5).join("0") + formatDecimalParts(x5, Math.max(0, p5 + i5 - 1))[0];
}

// node_modules/d3-format/src/formatRounded.js
function formatRounded_default(x5, p5) {
  var d3 = formatDecimalParts(x5, p5);
  if (!d3) return x5 + "";
  var coefficient = d3[0], exponent = d3[1];
  return exponent < 0 ? "0." + new Array(-exponent).join("0") + coefficient : coefficient.length > exponent + 1 ? coefficient.slice(0, exponent + 1) + "." + coefficient.slice(exponent + 1) : coefficient + new Array(exponent - coefficient.length + 2).join("0");
}

// node_modules/d3-format/src/formatTypes.js
var formatTypes_default = {
  "%": function(x5, p5) {
    return (x5 * 100).toFixed(p5);
  },
  "b": function(x5) {
    return Math.round(x5).toString(2);
  },
  "c": function(x5) {
    return x5 + "";
  },
  "d": formatDecimal_default,
  "e": function(x5, p5) {
    return x5.toExponential(p5);
  },
  "f": function(x5, p5) {
    return x5.toFixed(p5);
  },
  "g": function(x5, p5) {
    return x5.toPrecision(p5);
  },
  "o": function(x5) {
    return Math.round(x5).toString(8);
  },
  "p": function(x5, p5) {
    return formatRounded_default(x5 * 100, p5);
  },
  "r": formatRounded_default,
  "s": formatPrefixAuto_default,
  "X": function(x5) {
    return Math.round(x5).toString(16).toUpperCase();
  },
  "x": function(x5) {
    return Math.round(x5).toString(16);
  }
};

// node_modules/d3-format/src/identity.js
function identity_default(x5) {
  return x5;
}

// node_modules/d3-format/src/locale.js
var map3 = Array.prototype.map;
var prefixes2 = ["y", "z", "a", "f", "p", "n", "µ", "m", "", "k", "M", "G", "T", "P", "E", "Z", "Y"];
function locale_default(locale3) {
  var group2 = locale3.grouping === void 0 || locale3.thousands === void 0 ? identity_default : formatGroup_default(map3.call(locale3.grouping, Number), locale3.thousands + ""), currencyPrefix = locale3.currency === void 0 ? "" : locale3.currency[0] + "", currencySuffix = locale3.currency === void 0 ? "" : locale3.currency[1] + "", decimal = locale3.decimal === void 0 ? "." : locale3.decimal + "", numerals = locale3.numerals === void 0 ? identity_default : formatNumerals_default(map3.call(locale3.numerals, String)), percent = locale3.percent === void 0 ? "%" : locale3.percent + "", minus = locale3.minus === void 0 ? "-" : locale3.minus + "", nan = locale3.nan === void 0 ? "NaN" : locale3.nan + "";
  function newFormat(specifier) {
    specifier = formatSpecifier(specifier);
    var fill = specifier.fill, align = specifier.align, sign2 = specifier.sign, symbol = specifier.symbol, zero3 = specifier.zero, width = specifier.width, comma = specifier.comma, precision = specifier.precision, trim = specifier.trim, type = specifier.type;
    if (type === "n") comma = true, type = "g";
    else if (!formatTypes_default[type]) precision === void 0 && (precision = 12), trim = true, type = "g";
    if (zero3 || fill === "0" && align === "=") zero3 = true, fill = "0", align = "=";
    var prefix2 = symbol === "$" ? currencyPrefix : symbol === "#" && /[boxX]/.test(type) ? "0" + type.toLowerCase() : "", suffix = symbol === "$" ? currencySuffix : /[%p]/.test(type) ? percent : "";
    var formatType = formatTypes_default[type], maybeSuffix = /[defgprs%]/.test(type);
    precision = precision === void 0 ? 6 : /[gprs]/.test(type) ? Math.max(1, Math.min(21, precision)) : Math.max(0, Math.min(20, precision));
    function format2(value) {
      var valuePrefix = prefix2, valueSuffix = suffix, i5, n6, c9;
      if (type === "c") {
        valueSuffix = formatType(value) + valueSuffix;
        value = "";
      } else {
        value = +value;
        var valueNegative = value < 0 || 1 / value < 0;
        value = isNaN(value) ? nan : formatType(Math.abs(value), precision);
        if (trim) value = formatTrim_default(value);
        if (valueNegative && +value === 0 && sign2 !== "+") valueNegative = false;
        valuePrefix = (valueNegative ? sign2 === "(" ? sign2 : minus : sign2 === "-" || sign2 === "(" ? "" : sign2) + valuePrefix;
        valueSuffix = (type === "s" ? prefixes2[8 + prefixExponent / 3] : "") + valueSuffix + (valueNegative && sign2 === "(" ? ")" : "");
        if (maybeSuffix) {
          i5 = -1, n6 = value.length;
          while (++i5 < n6) {
            if (c9 = value.charCodeAt(i5), 48 > c9 || c9 > 57) {
              valueSuffix = (c9 === 46 ? decimal + value.slice(i5 + 1) : value.slice(i5)) + valueSuffix;
              value = value.slice(0, i5);
              break;
            }
          }
        }
      }
      if (comma && !zero3) value = group2(value, Infinity);
      var length = valuePrefix.length + value.length + valueSuffix.length, padding = length < width ? new Array(width - length + 1).join(fill) : "";
      if (comma && zero3) value = group2(padding + value, padding.length ? width - valueSuffix.length : Infinity), padding = "";
      switch (align) {
        case "<":
          value = valuePrefix + value + valueSuffix + padding;
          break;
        case "=":
          value = valuePrefix + padding + value + valueSuffix;
          break;
        case "^":
          value = padding.slice(0, length = padding.length >> 1) + valuePrefix + value + valueSuffix + padding.slice(length);
          break;
        default:
          value = padding + valuePrefix + value + valueSuffix;
          break;
      }
      return numerals(value);
    }
    format2.toString = function() {
      return specifier + "";
    };
    return format2;
  }
  function formatPrefix2(specifier, value) {
    var f3 = newFormat((specifier = formatSpecifier(specifier), specifier.type = "f", specifier)), e11 = Math.max(-8, Math.min(8, Math.floor(exponent_default(value) / 3))) * 3, k7 = Math.pow(10, -e11), prefix2 = prefixes2[8 + e11 / 3];
    return function(value2) {
      return f3(k7 * value2) + prefix2;
    };
  }
  return {
    format: newFormat,
    formatPrefix: formatPrefix2
  };
}

// node_modules/d3-format/src/defaultLocale.js
var locale;
var format;
var formatPrefix;
defaultLocale({
  decimal: ".",
  thousands: ",",
  grouping: [3],
  currency: ["$", ""],
  minus: "-"
});
function defaultLocale(definition) {
  locale = locale_default(definition);
  format = locale.format;
  formatPrefix = locale.formatPrefix;
  return locale;
}

// node_modules/d3-format/src/precisionFixed.js
function precisionFixed_default(step) {
  return Math.max(0, -exponent_default(Math.abs(step)));
}

// node_modules/d3-format/src/precisionPrefix.js
function precisionPrefix_default(step, value) {
  return Math.max(0, Math.max(-8, Math.min(8, Math.floor(exponent_default(value) / 3))) * 3 - exponent_default(Math.abs(step)));
}

// node_modules/d3-format/src/precisionRound.js
function precisionRound_default(step, max3) {
  step = Math.abs(step), max3 = Math.abs(max3) - step;
  return Math.max(0, exponent_default(max3) - exponent_default(step)) + 1;
}

// node_modules/d3-scale/src/tickFormat.js
function tickFormat(start2, stop2, count2, specifier) {
  var step = tickStep(start2, stop2, count2), precision;
  specifier = formatSpecifier(specifier == null ? ",f" : specifier);
  switch (specifier.type) {
    case "s": {
      var value = Math.max(Math.abs(start2), Math.abs(stop2));
      if (specifier.precision == null && !isNaN(precision = precisionPrefix_default(step, value))) specifier.precision = precision;
      return formatPrefix(specifier, value);
    }
    case "":
    case "e":
    case "g":
    case "p":
    case "r": {
      if (specifier.precision == null && !isNaN(precision = precisionRound_default(step, Math.max(Math.abs(start2), Math.abs(stop2))))) specifier.precision = precision - (specifier.type === "e");
      break;
    }
    case "f":
    case "%": {
      if (specifier.precision == null && !isNaN(precision = precisionFixed_default(step))) specifier.precision = precision - (specifier.type === "%") * 2;
      break;
    }
  }
  return format(specifier);
}

// node_modules/d3-scale/src/linear.js
function linearish(scale) {
  var domain = scale.domain;
  scale.ticks = function(count2) {
    var d3 = domain();
    return ticks(d3[0], d3[d3.length - 1], count2 == null ? 10 : count2);
  };
  scale.tickFormat = function(count2, specifier) {
    var d3 = domain();
    return tickFormat(d3[0], d3[d3.length - 1], count2 == null ? 10 : count2, specifier);
  };
  scale.nice = function(count2) {
    if (count2 == null) count2 = 10;
    var d3 = domain();
    var i0 = 0;
    var i1 = d3.length - 1;
    var start2 = d3[i0];
    var stop2 = d3[i1];
    var prestep;
    var step;
    var maxIter = 10;
    if (stop2 < start2) {
      step = start2, start2 = stop2, stop2 = step;
      step = i0, i0 = i1, i1 = step;
    }
    while (maxIter-- > 0) {
      step = tickIncrement(start2, stop2, count2);
      if (step === prestep) {
        d3[i0] = start2;
        d3[i1] = stop2;
        return domain(d3);
      } else if (step > 0) {
        start2 = Math.floor(start2 / step) * step;
        stop2 = Math.ceil(stop2 / step) * step;
      } else if (step < 0) {
        start2 = Math.ceil(start2 * step) / step;
        stop2 = Math.floor(stop2 * step) / step;
      } else {
        break;
      }
      prestep = step;
    }
    return scale;
  };
  return scale;
}
function linear2() {
  var scale = continuous();
  scale.copy = function() {
    return copy(scale, linear2());
  };
  initRange.apply(scale, arguments);
  return linearish(scale);
}

// node_modules/d3-scale/src/nice.js
function nice2(domain, interval) {
  domain = domain.slice();
  var i0 = 0, i1 = domain.length - 1, x0 = domain[i0], x1 = domain[i1], t9;
  if (x1 < x0) {
    t9 = i0, i0 = i1, i1 = t9;
    t9 = x0, x0 = x1, x1 = t9;
  }
  domain[i0] = interval.floor(x0);
  domain[i1] = interval.ceil(x1);
  return domain;
}

// node_modules/d3-scale/src/log.js
function transformLog(x5) {
  return Math.log(x5);
}
function transformExp(x5) {
  return Math.exp(x5);
}
function transformLogn(x5) {
  return -Math.log(-x5);
}
function transformExpn(x5) {
  return -Math.exp(-x5);
}
function pow10(x5) {
  return isFinite(x5) ? +("1e" + x5) : x5 < 0 ? 0 : x5;
}
function powp(base) {
  return base === 10 ? pow10 : base === Math.E ? Math.exp : (x5) => Math.pow(base, x5);
}
function logp(base) {
  return base === Math.E ? Math.log : base === 10 && Math.log10 || base === 2 && Math.log2 || (base = Math.log(base), (x5) => Math.log(x5) / base);
}
function reflect(f3) {
  return (x5, k7) => -f3(-x5, k7);
}
function loggish(transform) {
  const scale = transform(transformLog, transformExp);
  const domain = scale.domain;
  let base = 10;
  let logs;
  let pows;
  function rescale() {
    logs = logp(base), pows = powp(base);
    if (domain()[0] < 0) {
      logs = reflect(logs), pows = reflect(pows);
      transform(transformLogn, transformExpn);
    } else {
      transform(transformLog, transformExp);
    }
    return scale;
  }
  scale.base = function(_3) {
    return arguments.length ? (base = +_3, rescale()) : base;
  };
  scale.domain = function(_3) {
    return arguments.length ? (domain(_3), rescale()) : domain();
  };
  scale.ticks = (count2) => {
    const d3 = domain();
    let u3 = d3[0];
    let v6 = d3[d3.length - 1];
    const r5 = v6 < u3;
    if (r5) [u3, v6] = [v6, u3];
    let i5 = logs(u3);
    let j5 = logs(v6);
    let k7;
    let t9;
    const n6 = count2 == null ? 10 : +count2;
    let z5 = [];
    if (!(base % 1) && j5 - i5 < n6) {
      i5 = Math.floor(i5), j5 = Math.ceil(j5);
      if (u3 > 0) for (; i5 <= j5; ++i5) {
        for (k7 = 1; k7 < base; ++k7) {
          t9 = i5 < 0 ? k7 / pows(-i5) : k7 * pows(i5);
          if (t9 < u3) continue;
          if (t9 > v6) break;
          z5.push(t9);
        }
      }
      else for (; i5 <= j5; ++i5) {
        for (k7 = base - 1; k7 >= 1; --k7) {
          t9 = i5 > 0 ? k7 / pows(-i5) : k7 * pows(i5);
          if (t9 < u3) continue;
          if (t9 > v6) break;
          z5.push(t9);
        }
      }
      if (z5.length * 2 < n6) z5 = ticks(u3, v6, n6);
    } else {
      z5 = ticks(i5, j5, Math.min(j5 - i5, n6)).map(pows);
    }
    return r5 ? z5.reverse() : z5;
  };
  scale.tickFormat = (count2, specifier) => {
    if (count2 == null) count2 = 10;
    if (specifier == null) specifier = base === 10 ? "s" : ",";
    if (typeof specifier !== "function") {
      if (!(base % 1) && (specifier = formatSpecifier(specifier)).precision == null) specifier.trim = true;
      specifier = format(specifier);
    }
    if (count2 === Infinity) return specifier;
    const k7 = Math.max(1, base * count2 / scale.ticks().length);
    return (d3) => {
      let i5 = d3 / pows(Math.round(logs(d3)));
      if (i5 * base < base - 0.5) i5 *= base;
      return i5 <= k7 ? specifier(d3) : "";
    };
  };
  scale.nice = () => {
    return domain(nice2(domain(), {
      floor: (x5) => pows(Math.floor(logs(x5))),
      ceil: (x5) => pows(Math.ceil(logs(x5)))
    }));
  };
  return scale;
}
function log() {
  const scale = loggish(transformer()).domain([1, 10]);
  scale.copy = () => copy(scale, log()).base(scale.base());
  initRange.apply(scale, arguments);
  return scale;
}

// node_modules/d3-scale/src/symlog.js
function transformSymlog(c9) {
  return function(x5) {
    return Math.sign(x5) * Math.log1p(Math.abs(x5 / c9));
  };
}
function transformSymexp(c9) {
  return function(x5) {
    return Math.sign(x5) * Math.expm1(Math.abs(x5)) * c9;
  };
}
function symlogish(transform) {
  var c9 = 1, scale = transform(transformSymlog(c9), transformSymexp(c9));
  scale.constant = function(_3) {
    return arguments.length ? transform(transformSymlog(c9 = +_3), transformSymexp(c9)) : c9;
  };
  return linearish(scale);
}
function symlog() {
  var scale = symlogish(transformer());
  scale.copy = function() {
    return copy(scale, symlog()).constant(scale.constant());
  };
  return initRange.apply(scale, arguments);
}

// node_modules/d3-scale/node_modules/d3-time/src/interval.js
var t02 = /* @__PURE__ */ new Date();
var t12 = /* @__PURE__ */ new Date();
function timeInterval(floori, offseti, count2, field) {
  function interval(date2) {
    return floori(date2 = arguments.length === 0 ? /* @__PURE__ */ new Date() : /* @__PURE__ */ new Date(+date2)), date2;
  }
  interval.floor = (date2) => {
    return floori(date2 = /* @__PURE__ */ new Date(+date2)), date2;
  };
  interval.ceil = (date2) => {
    return floori(date2 = new Date(date2 - 1)), offseti(date2, 1), floori(date2), date2;
  };
  interval.round = (date2) => {
    const d0 = interval(date2), d1 = interval.ceil(date2);
    return date2 - d0 < d1 - date2 ? d0 : d1;
  };
  interval.offset = (date2, step) => {
    return offseti(date2 = /* @__PURE__ */ new Date(+date2), step == null ? 1 : Math.floor(step)), date2;
  };
  interval.range = (start2, stop2, step) => {
    const range2 = [];
    start2 = interval.ceil(start2);
    step = step == null ? 1 : Math.floor(step);
    if (!(start2 < stop2) || !(step > 0)) return range2;
    let previous;
    do
      range2.push(previous = /* @__PURE__ */ new Date(+start2)), offseti(start2, step), floori(start2);
    while (previous < start2 && start2 < stop2);
    return range2;
  };
  interval.filter = (test) => {
    return timeInterval((date2) => {
      if (date2 >= date2) while (floori(date2), !test(date2)) date2.setTime(date2 - 1);
    }, (date2, step) => {
      if (date2 >= date2) {
        if (step < 0) while (++step <= 0) {
          while (offseti(date2, -1), !test(date2)) {
          }
        }
        else while (--step >= 0) {
          while (offseti(date2, 1), !test(date2)) {
          }
        }
      }
    });
  };
  if (count2) {
    interval.count = (start2, end) => {
      t02.setTime(+start2), t12.setTime(+end);
      floori(t02), floori(t12);
      return Math.floor(count2(t02, t12));
    };
    interval.every = (step) => {
      step = Math.floor(step);
      return !isFinite(step) || !(step > 0) ? null : !(step > 1) ? interval : interval.filter(field ? (d3) => field(d3) % step === 0 : (d3) => interval.count(0, d3) % step === 0);
    };
  }
  return interval;
}

// node_modules/d3-scale/node_modules/d3-time/src/millisecond.js
var millisecond = timeInterval(() => {
}, (date2, step) => {
  date2.setTime(+date2 + step);
}, (start2, end) => {
  return end - start2;
});
millisecond.every = (k7) => {
  k7 = Math.floor(k7);
  if (!isFinite(k7) || !(k7 > 0)) return null;
  if (!(k7 > 1)) return millisecond;
  return timeInterval((date2) => {
    date2.setTime(Math.floor(date2 / k7) * k7);
  }, (date2, step) => {
    date2.setTime(+date2 + step * k7);
  }, (start2, end) => {
    return (end - start2) / k7;
  });
};
var milliseconds = millisecond.range;

// node_modules/d3-scale/node_modules/d3-time/src/duration.js
var durationSecond = 1e3;
var durationMinute = durationSecond * 60;
var durationHour = durationMinute * 60;
var durationDay = durationHour * 24;
var durationWeek = durationDay * 7;
var durationMonth = durationDay * 30;
var durationYear = durationDay * 365;

// node_modules/d3-scale/node_modules/d3-time/src/second.js
var second = timeInterval((date2) => {
  date2.setTime(date2 - date2.getMilliseconds());
}, (date2, step) => {
  date2.setTime(+date2 + step * durationSecond);
}, (start2, end) => {
  return (end - start2) / durationSecond;
}, (date2) => {
  return date2.getUTCSeconds();
});
var seconds = second.range;

// node_modules/d3-scale/node_modules/d3-time/src/minute.js
var timeMinute = timeInterval((date2) => {
  date2.setTime(date2 - date2.getMilliseconds() - date2.getSeconds() * durationSecond);
}, (date2, step) => {
  date2.setTime(+date2 + step * durationMinute);
}, (start2, end) => {
  return (end - start2) / durationMinute;
}, (date2) => {
  return date2.getMinutes();
});
var timeMinutes = timeMinute.range;
var utcMinute = timeInterval((date2) => {
  date2.setUTCSeconds(0, 0);
}, (date2, step) => {
  date2.setTime(+date2 + step * durationMinute);
}, (start2, end) => {
  return (end - start2) / durationMinute;
}, (date2) => {
  return date2.getUTCMinutes();
});
var utcMinutes = utcMinute.range;

// node_modules/d3-scale/node_modules/d3-time/src/hour.js
var timeHour = timeInterval((date2) => {
  date2.setTime(date2 - date2.getMilliseconds() - date2.getSeconds() * durationSecond - date2.getMinutes() * durationMinute);
}, (date2, step) => {
  date2.setTime(+date2 + step * durationHour);
}, (start2, end) => {
  return (end - start2) / durationHour;
}, (date2) => {
  return date2.getHours();
});
var timeHours = timeHour.range;
var utcHour = timeInterval((date2) => {
  date2.setUTCMinutes(0, 0, 0);
}, (date2, step) => {
  date2.setTime(+date2 + step * durationHour);
}, (start2, end) => {
  return (end - start2) / durationHour;
}, (date2) => {
  return date2.getUTCHours();
});
var utcHours = utcHour.range;

// node_modules/d3-scale/node_modules/d3-time/src/day.js
var timeDay = timeInterval(
  (date2) => date2.setHours(0, 0, 0, 0),
  (date2, step) => date2.setDate(date2.getDate() + step),
  (start2, end) => (end - start2 - (end.getTimezoneOffset() - start2.getTimezoneOffset()) * durationMinute) / durationDay,
  (date2) => date2.getDate() - 1
);
var timeDays = timeDay.range;
var utcDay = timeInterval((date2) => {
  date2.setUTCHours(0, 0, 0, 0);
}, (date2, step) => {
  date2.setUTCDate(date2.getUTCDate() + step);
}, (start2, end) => {
  return (end - start2) / durationDay;
}, (date2) => {
  return date2.getUTCDate() - 1;
});
var utcDays = utcDay.range;
var unixDay = timeInterval((date2) => {
  date2.setUTCHours(0, 0, 0, 0);
}, (date2, step) => {
  date2.setUTCDate(date2.getUTCDate() + step);
}, (start2, end) => {
  return (end - start2) / durationDay;
}, (date2) => {
  return Math.floor(date2 / durationDay);
});
var unixDays = unixDay.range;

// node_modules/d3-scale/node_modules/d3-time/src/week.js
function timeWeekday(i5) {
  return timeInterval((date2) => {
    date2.setDate(date2.getDate() - (date2.getDay() + 7 - i5) % 7);
    date2.setHours(0, 0, 0, 0);
  }, (date2, step) => {
    date2.setDate(date2.getDate() + step * 7);
  }, (start2, end) => {
    return (end - start2 - (end.getTimezoneOffset() - start2.getTimezoneOffset()) * durationMinute) / durationWeek;
  });
}
var timeSunday = timeWeekday(0);
var timeMonday = timeWeekday(1);
var timeTuesday = timeWeekday(2);
var timeWednesday = timeWeekday(3);
var timeThursday = timeWeekday(4);
var timeFriday = timeWeekday(5);
var timeSaturday = timeWeekday(6);
var timeSundays = timeSunday.range;
var timeMondays = timeMonday.range;
var timeTuesdays = timeTuesday.range;
var timeWednesdays = timeWednesday.range;
var timeThursdays = timeThursday.range;
var timeFridays = timeFriday.range;
var timeSaturdays = timeSaturday.range;
function utcWeekday(i5) {
  return timeInterval((date2) => {
    date2.setUTCDate(date2.getUTCDate() - (date2.getUTCDay() + 7 - i5) % 7);
    date2.setUTCHours(0, 0, 0, 0);
  }, (date2, step) => {
    date2.setUTCDate(date2.getUTCDate() + step * 7);
  }, (start2, end) => {
    return (end - start2) / durationWeek;
  });
}
var utcSunday = utcWeekday(0);
var utcMonday = utcWeekday(1);
var utcTuesday = utcWeekday(2);
var utcWednesday = utcWeekday(3);
var utcThursday = utcWeekday(4);
var utcFriday = utcWeekday(5);
var utcSaturday = utcWeekday(6);
var utcSundays = utcSunday.range;
var utcMondays = utcMonday.range;
var utcTuesdays = utcTuesday.range;
var utcWednesdays = utcWednesday.range;
var utcThursdays = utcThursday.range;
var utcFridays = utcFriday.range;
var utcSaturdays = utcSaturday.range;

// node_modules/d3-scale/node_modules/d3-time/src/month.js
var timeMonth = timeInterval((date2) => {
  date2.setDate(1);
  date2.setHours(0, 0, 0, 0);
}, (date2, step) => {
  date2.setMonth(date2.getMonth() + step);
}, (start2, end) => {
  return end.getMonth() - start2.getMonth() + (end.getFullYear() - start2.getFullYear()) * 12;
}, (date2) => {
  return date2.getMonth();
});
var timeMonths = timeMonth.range;
var utcMonth = timeInterval((date2) => {
  date2.setUTCDate(1);
  date2.setUTCHours(0, 0, 0, 0);
}, (date2, step) => {
  date2.setUTCMonth(date2.getUTCMonth() + step);
}, (start2, end) => {
  return end.getUTCMonth() - start2.getUTCMonth() + (end.getUTCFullYear() - start2.getUTCFullYear()) * 12;
}, (date2) => {
  return date2.getUTCMonth();
});
var utcMonths = utcMonth.range;

// node_modules/d3-scale/node_modules/d3-time/src/year.js
var timeYear = timeInterval((date2) => {
  date2.setMonth(0, 1);
  date2.setHours(0, 0, 0, 0);
}, (date2, step) => {
  date2.setFullYear(date2.getFullYear() + step);
}, (start2, end) => {
  return end.getFullYear() - start2.getFullYear();
}, (date2) => {
  return date2.getFullYear();
});
timeYear.every = (k7) => {
  return !isFinite(k7 = Math.floor(k7)) || !(k7 > 0) ? null : timeInterval((date2) => {
    date2.setFullYear(Math.floor(date2.getFullYear() / k7) * k7);
    date2.setMonth(0, 1);
    date2.setHours(0, 0, 0, 0);
  }, (date2, step) => {
    date2.setFullYear(date2.getFullYear() + step * k7);
  });
};
var timeYears = timeYear.range;
var utcYear = timeInterval((date2) => {
  date2.setUTCMonth(0, 1);
  date2.setUTCHours(0, 0, 0, 0);
}, (date2, step) => {
  date2.setUTCFullYear(date2.getUTCFullYear() + step);
}, (start2, end) => {
  return end.getUTCFullYear() - start2.getUTCFullYear();
}, (date2) => {
  return date2.getUTCFullYear();
});
utcYear.every = (k7) => {
  return !isFinite(k7 = Math.floor(k7)) || !(k7 > 0) ? null : timeInterval((date2) => {
    date2.setUTCFullYear(Math.floor(date2.getUTCFullYear() / k7) * k7);
    date2.setUTCMonth(0, 1);
    date2.setUTCHours(0, 0, 0, 0);
  }, (date2, step) => {
    date2.setUTCFullYear(date2.getUTCFullYear() + step * k7);
  });
};
var utcYears = utcYear.range;

// node_modules/d3-scale/node_modules/d3-time/src/ticks.js
function ticker(year2, month2, week, day2, hour2, minute2) {
  const tickIntervals = [
    [second, 1, durationSecond],
    [second, 5, 5 * durationSecond],
    [second, 15, 15 * durationSecond],
    [second, 30, 30 * durationSecond],
    [minute2, 1, durationMinute],
    [minute2, 5, 5 * durationMinute],
    [minute2, 15, 15 * durationMinute],
    [minute2, 30, 30 * durationMinute],
    [hour2, 1, durationHour],
    [hour2, 3, 3 * durationHour],
    [hour2, 6, 6 * durationHour],
    [hour2, 12, 12 * durationHour],
    [day2, 1, durationDay],
    [day2, 2, 2 * durationDay],
    [week, 1, durationWeek],
    [month2, 1, durationMonth],
    [month2, 3, 3 * durationMonth],
    [year2, 1, durationYear]
  ];
  function ticks2(start2, stop2, count2) {
    const reverse2 = stop2 < start2;
    if (reverse2) [start2, stop2] = [stop2, start2];
    const interval = count2 && typeof count2.range === "function" ? count2 : tickInterval(start2, stop2, count2);
    const ticks3 = interval ? interval.range(start2, +stop2 + 1) : [];
    return reverse2 ? ticks3.reverse() : ticks3;
  }
  function tickInterval(start2, stop2, count2) {
    const target = Math.abs(stop2 - start2) / count2;
    const i5 = bisector(([, , step2]) => step2).right(tickIntervals, target);
    if (i5 === tickIntervals.length) return year2.every(tickStep(start2 / durationYear, stop2 / durationYear, count2));
    if (i5 === 0) return millisecond.every(Math.max(tickStep(start2, stop2, count2), 1));
    const [t9, step] = tickIntervals[target / tickIntervals[i5 - 1][2] < tickIntervals[i5][2] / target ? i5 - 1 : i5];
    return t9.every(step);
  }
  return [ticks2, tickInterval];
}
var [utcTicks, utcTickInterval] = ticker(utcYear, utcMonth, utcSunday, unixDay, utcHour, utcMinute);
var [timeTicks, timeTickInterval] = ticker(timeYear, timeMonth, timeSunday, timeDay, timeHour, timeMinute);

// node_modules/d3-time/src/interval.js
var t03 = /* @__PURE__ */ new Date();
var t13 = /* @__PURE__ */ new Date();
function newInterval(floori, offseti, count2, field) {
  function interval(date2) {
    return floori(date2 = arguments.length === 0 ? /* @__PURE__ */ new Date() : /* @__PURE__ */ new Date(+date2)), date2;
  }
  interval.floor = function(date2) {
    return floori(date2 = /* @__PURE__ */ new Date(+date2)), date2;
  };
  interval.ceil = function(date2) {
    return floori(date2 = new Date(date2 - 1)), offseti(date2, 1), floori(date2), date2;
  };
  interval.round = function(date2) {
    var d0 = interval(date2), d1 = interval.ceil(date2);
    return date2 - d0 < d1 - date2 ? d0 : d1;
  };
  interval.offset = function(date2, step) {
    return offseti(date2 = /* @__PURE__ */ new Date(+date2), step == null ? 1 : Math.floor(step)), date2;
  };
  interval.range = function(start2, stop2, step) {
    var range2 = [], previous;
    start2 = interval.ceil(start2);
    step = step == null ? 1 : Math.floor(step);
    if (!(start2 < stop2) || !(step > 0)) return range2;
    do
      range2.push(previous = /* @__PURE__ */ new Date(+start2)), offseti(start2, step), floori(start2);
    while (previous < start2 && start2 < stop2);
    return range2;
  };
  interval.filter = function(test) {
    return newInterval(function(date2) {
      if (date2 >= date2) while (floori(date2), !test(date2)) date2.setTime(date2 - 1);
    }, function(date2, step) {
      if (date2 >= date2) {
        if (step < 0) while (++step <= 0) {
          while (offseti(date2, -1), !test(date2)) {
          }
        }
        else while (--step >= 0) {
          while (offseti(date2, 1), !test(date2)) {
          }
        }
      }
    });
  };
  if (count2) {
    interval.count = function(start2, end) {
      t03.setTime(+start2), t13.setTime(+end);
      floori(t03), floori(t13);
      return Math.floor(count2(t03, t13));
    };
    interval.every = function(step) {
      step = Math.floor(step);
      return !isFinite(step) || !(step > 0) ? null : !(step > 1) ? interval : interval.filter(field ? function(d3) {
        return field(d3) % step === 0;
      } : function(d3) {
        return interval.count(0, d3) % step === 0;
      });
    };
  }
  return interval;
}

// node_modules/d3-time/src/millisecond.js
var millisecond2 = newInterval(function() {
}, function(date2, step) {
  date2.setTime(+date2 + step);
}, function(start2, end) {
  return end - start2;
});
millisecond2.every = function(k7) {
  k7 = Math.floor(k7);
  if (!isFinite(k7) || !(k7 > 0)) return null;
  if (!(k7 > 1)) return millisecond2;
  return newInterval(function(date2) {
    date2.setTime(Math.floor(date2 / k7) * k7);
  }, function(date2, step) {
    date2.setTime(+date2 + step * k7);
  }, function(start2, end) {
    return (end - start2) / k7;
  });
};
var millisecond_default = millisecond2;
var milliseconds2 = millisecond2.range;

// node_modules/d3-time/src/duration.js
var durationSecond2 = 1e3;
var durationMinute2 = 6e4;
var durationHour2 = 36e5;
var durationDay2 = 864e5;
var durationWeek2 = 6048e5;

// node_modules/d3-time/src/second.js
var second2 = newInterval(function(date2) {
  date2.setTime(date2 - date2.getMilliseconds());
}, function(date2, step) {
  date2.setTime(+date2 + step * durationSecond2);
}, function(start2, end) {
  return (end - start2) / durationSecond2;
}, function(date2) {
  return date2.getUTCSeconds();
});
var second_default = second2;
var seconds2 = second2.range;

// node_modules/d3-time/src/minute.js
var minute = newInterval(function(date2) {
  date2.setTime(date2 - date2.getMilliseconds() - date2.getSeconds() * durationSecond2);
}, function(date2, step) {
  date2.setTime(+date2 + step * durationMinute2);
}, function(start2, end) {
  return (end - start2) / durationMinute2;
}, function(date2) {
  return date2.getMinutes();
});
var minute_default = minute;
var minutes = minute.range;

// node_modules/d3-time/src/hour.js
var hour = newInterval(function(date2) {
  date2.setTime(date2 - date2.getMilliseconds() - date2.getSeconds() * durationSecond2 - date2.getMinutes() * durationMinute2);
}, function(date2, step) {
  date2.setTime(+date2 + step * durationHour2);
}, function(start2, end) {
  return (end - start2) / durationHour2;
}, function(date2) {
  return date2.getHours();
});
var hour_default = hour;
var hours = hour.range;

// node_modules/d3-time/src/day.js
var day = newInterval(function(date2) {
  date2.setHours(0, 0, 0, 0);
}, function(date2, step) {
  date2.setDate(date2.getDate() + step);
}, function(start2, end) {
  return (end - start2 - (end.getTimezoneOffset() - start2.getTimezoneOffset()) * durationMinute2) / durationDay2;
}, function(date2) {
  return date2.getDate() - 1;
});
var day_default = day;
var days = day.range;

// node_modules/d3-time/src/week.js
function weekday(i5) {
  return newInterval(function(date2) {
    date2.setDate(date2.getDate() - (date2.getDay() + 7 - i5) % 7);
    date2.setHours(0, 0, 0, 0);
  }, function(date2, step) {
    date2.setDate(date2.getDate() + step * 7);
  }, function(start2, end) {
    return (end - start2 - (end.getTimezoneOffset() - start2.getTimezoneOffset()) * durationMinute2) / durationWeek2;
  });
}
var sunday = weekday(0);
var monday = weekday(1);
var tuesday = weekday(2);
var wednesday = weekday(3);
var thursday = weekday(4);
var friday = weekday(5);
var saturday = weekday(6);
var sundays = sunday.range;
var mondays = monday.range;
var tuesdays = tuesday.range;
var wednesdays = wednesday.range;
var thursdays = thursday.range;
var fridays = friday.range;
var saturdays = saturday.range;

// node_modules/d3-time/src/month.js
var month = newInterval(function(date2) {
  date2.setDate(1);
  date2.setHours(0, 0, 0, 0);
}, function(date2, step) {
  date2.setMonth(date2.getMonth() + step);
}, function(start2, end) {
  return end.getMonth() - start2.getMonth() + (end.getFullYear() - start2.getFullYear()) * 12;
}, function(date2) {
  return date2.getMonth();
});
var month_default = month;
var months = month.range;

// node_modules/d3-time/src/year.js
var year = newInterval(function(date2) {
  date2.setMonth(0, 1);
  date2.setHours(0, 0, 0, 0);
}, function(date2, step) {
  date2.setFullYear(date2.getFullYear() + step);
}, function(start2, end) {
  return end.getFullYear() - start2.getFullYear();
}, function(date2) {
  return date2.getFullYear();
});
year.every = function(k7) {
  return !isFinite(k7 = Math.floor(k7)) || !(k7 > 0) ? null : newInterval(function(date2) {
    date2.setFullYear(Math.floor(date2.getFullYear() / k7) * k7);
    date2.setMonth(0, 1);
    date2.setHours(0, 0, 0, 0);
  }, function(date2, step) {
    date2.setFullYear(date2.getFullYear() + step * k7);
  });
};
var year_default = year;
var years = year.range;

// node_modules/d3-time/src/utcMinute.js
var utcMinute2 = newInterval(function(date2) {
  date2.setUTCSeconds(0, 0);
}, function(date2, step) {
  date2.setTime(+date2 + step * durationMinute2);
}, function(start2, end) {
  return (end - start2) / durationMinute2;
}, function(date2) {
  return date2.getUTCMinutes();
});
var utcMinute_default = utcMinute2;
var utcMinutes2 = utcMinute2.range;

// node_modules/d3-time/src/utcHour.js
var utcHour2 = newInterval(function(date2) {
  date2.setUTCMinutes(0, 0, 0);
}, function(date2, step) {
  date2.setTime(+date2 + step * durationHour2);
}, function(start2, end) {
  return (end - start2) / durationHour2;
}, function(date2) {
  return date2.getUTCHours();
});
var utcHour_default = utcHour2;
var utcHours2 = utcHour2.range;

// node_modules/d3-time/src/utcDay.js
var utcDay2 = newInterval(function(date2) {
  date2.setUTCHours(0, 0, 0, 0);
}, function(date2, step) {
  date2.setUTCDate(date2.getUTCDate() + step);
}, function(start2, end) {
  return (end - start2) / durationDay2;
}, function(date2) {
  return date2.getUTCDate() - 1;
});
var utcDay_default = utcDay2;
var utcDays2 = utcDay2.range;

// node_modules/d3-time/src/utcWeek.js
function utcWeekday2(i5) {
  return newInterval(function(date2) {
    date2.setUTCDate(date2.getUTCDate() - (date2.getUTCDay() + 7 - i5) % 7);
    date2.setUTCHours(0, 0, 0, 0);
  }, function(date2, step) {
    date2.setUTCDate(date2.getUTCDate() + step * 7);
  }, function(start2, end) {
    return (end - start2) / durationWeek2;
  });
}
var utcSunday2 = utcWeekday2(0);
var utcMonday2 = utcWeekday2(1);
var utcTuesday2 = utcWeekday2(2);
var utcWednesday2 = utcWeekday2(3);
var utcThursday2 = utcWeekday2(4);
var utcFriday2 = utcWeekday2(5);
var utcSaturday2 = utcWeekday2(6);
var utcSundays2 = utcSunday2.range;
var utcMondays2 = utcMonday2.range;
var utcTuesdays2 = utcTuesday2.range;
var utcWednesdays2 = utcWednesday2.range;
var utcThursdays2 = utcThursday2.range;
var utcFridays2 = utcFriday2.range;
var utcSaturdays2 = utcSaturday2.range;

// node_modules/d3-time/src/utcMonth.js
var utcMonth2 = newInterval(function(date2) {
  date2.setUTCDate(1);
  date2.setUTCHours(0, 0, 0, 0);
}, function(date2, step) {
  date2.setUTCMonth(date2.getUTCMonth() + step);
}, function(start2, end) {
  return end.getUTCMonth() - start2.getUTCMonth() + (end.getUTCFullYear() - start2.getUTCFullYear()) * 12;
}, function(date2) {
  return date2.getUTCMonth();
});
var utcMonth_default = utcMonth2;
var utcMonths2 = utcMonth2.range;

// node_modules/d3-time/src/utcYear.js
var utcYear2 = newInterval(function(date2) {
  date2.setUTCMonth(0, 1);
  date2.setUTCHours(0, 0, 0, 0);
}, function(date2, step) {
  date2.setUTCFullYear(date2.getUTCFullYear() + step);
}, function(start2, end) {
  return end.getUTCFullYear() - start2.getUTCFullYear();
}, function(date2) {
  return date2.getUTCFullYear();
});
utcYear2.every = function(k7) {
  return !isFinite(k7 = Math.floor(k7)) || !(k7 > 0) ? null : newInterval(function(date2) {
    date2.setUTCFullYear(Math.floor(date2.getUTCFullYear() / k7) * k7);
    date2.setUTCMonth(0, 1);
    date2.setUTCHours(0, 0, 0, 0);
  }, function(date2, step) {
    date2.setUTCFullYear(date2.getUTCFullYear() + step * k7);
  });
};
var utcYear_default = utcYear2;
var utcYears2 = utcYear2.range;

// node_modules/d3-time-format/src/locale.js
function localDate(d3) {
  if (0 <= d3.y && d3.y < 100) {
    var date2 = new Date(-1, d3.m, d3.d, d3.H, d3.M, d3.S, d3.L);
    date2.setFullYear(d3.y);
    return date2;
  }
  return new Date(d3.y, d3.m, d3.d, d3.H, d3.M, d3.S, d3.L);
}
function utcDate(d3) {
  if (0 <= d3.y && d3.y < 100) {
    var date2 = new Date(Date.UTC(-1, d3.m, d3.d, d3.H, d3.M, d3.S, d3.L));
    date2.setUTCFullYear(d3.y);
    return date2;
  }
  return new Date(Date.UTC(d3.y, d3.m, d3.d, d3.H, d3.M, d3.S, d3.L));
}
function newDate(y5, m5, d3) {
  return { y: y5, m: m5, d: d3, H: 0, M: 0, S: 0, L: 0 };
}
function formatLocale(locale3) {
  var locale_dateTime = locale3.dateTime, locale_date = locale3.date, locale_time = locale3.time, locale_periods = locale3.periods, locale_weekdays = locale3.days, locale_shortWeekdays = locale3.shortDays, locale_months = locale3.months, locale_shortMonths = locale3.shortMonths;
  var periodRe = formatRe(locale_periods), periodLookup = formatLookup(locale_periods), weekdayRe = formatRe(locale_weekdays), weekdayLookup = formatLookup(locale_weekdays), shortWeekdayRe = formatRe(locale_shortWeekdays), shortWeekdayLookup = formatLookup(locale_shortWeekdays), monthRe = formatRe(locale_months), monthLookup = formatLookup(locale_months), shortMonthRe = formatRe(locale_shortMonths), shortMonthLookup = formatLookup(locale_shortMonths);
  var formats = {
    "a": formatShortWeekday,
    "A": formatWeekday,
    "b": formatShortMonth,
    "B": formatMonth,
    "c": null,
    "d": formatDayOfMonth,
    "e": formatDayOfMonth,
    "f": formatMicroseconds,
    "g": formatYearISO,
    "G": formatFullYearISO,
    "H": formatHour24,
    "I": formatHour12,
    "j": formatDayOfYear,
    "L": formatMilliseconds,
    "m": formatMonthNumber,
    "M": formatMinutes,
    "p": formatPeriod,
    "q": formatQuarter,
    "Q": formatUnixTimestamp,
    "s": formatUnixTimestampSeconds,
    "S": formatSeconds,
    "u": formatWeekdayNumberMonday,
    "U": formatWeekNumberSunday,
    "V": formatWeekNumberISO,
    "w": formatWeekdayNumberSunday,
    "W": formatWeekNumberMonday,
    "x": null,
    "X": null,
    "y": formatYear,
    "Y": formatFullYear,
    "Z": formatZone,
    "%": formatLiteralPercent
  };
  var utcFormats = {
    "a": formatUTCShortWeekday,
    "A": formatUTCWeekday,
    "b": formatUTCShortMonth,
    "B": formatUTCMonth,
    "c": null,
    "d": formatUTCDayOfMonth,
    "e": formatUTCDayOfMonth,
    "f": formatUTCMicroseconds,
    "g": formatUTCYearISO,
    "G": formatUTCFullYearISO,
    "H": formatUTCHour24,
    "I": formatUTCHour12,
    "j": formatUTCDayOfYear,
    "L": formatUTCMilliseconds,
    "m": formatUTCMonthNumber,
    "M": formatUTCMinutes,
    "p": formatUTCPeriod,
    "q": formatUTCQuarter,
    "Q": formatUnixTimestamp,
    "s": formatUnixTimestampSeconds,
    "S": formatUTCSeconds,
    "u": formatUTCWeekdayNumberMonday,
    "U": formatUTCWeekNumberSunday,
    "V": formatUTCWeekNumberISO,
    "w": formatUTCWeekdayNumberSunday,
    "W": formatUTCWeekNumberMonday,
    "x": null,
    "X": null,
    "y": formatUTCYear,
    "Y": formatUTCFullYear,
    "Z": formatUTCZone,
    "%": formatLiteralPercent
  };
  var parses = {
    "a": parseShortWeekday,
    "A": parseWeekday,
    "b": parseShortMonth,
    "B": parseMonth,
    "c": parseLocaleDateTime,
    "d": parseDayOfMonth,
    "e": parseDayOfMonth,
    "f": parseMicroseconds,
    "g": parseYear,
    "G": parseFullYear,
    "H": parseHour24,
    "I": parseHour24,
    "j": parseDayOfYear,
    "L": parseMilliseconds,
    "m": parseMonthNumber,
    "M": parseMinutes,
    "p": parsePeriod,
    "q": parseQuarter,
    "Q": parseUnixTimestamp,
    "s": parseUnixTimestampSeconds,
    "S": parseSeconds,
    "u": parseWeekdayNumberMonday,
    "U": parseWeekNumberSunday,
    "V": parseWeekNumberISO,
    "w": parseWeekdayNumberSunday,
    "W": parseWeekNumberMonday,
    "x": parseLocaleDate,
    "X": parseLocaleTime,
    "y": parseYear,
    "Y": parseFullYear,
    "Z": parseZone,
    "%": parseLiteralPercent
  };
  formats.x = newFormat(locale_date, formats);
  formats.X = newFormat(locale_time, formats);
  formats.c = newFormat(locale_dateTime, formats);
  utcFormats.x = newFormat(locale_date, utcFormats);
  utcFormats.X = newFormat(locale_time, utcFormats);
  utcFormats.c = newFormat(locale_dateTime, utcFormats);
  function newFormat(specifier, formats2) {
    return function(date2) {
      var string = [], i5 = -1, j5 = 0, n6 = specifier.length, c9, pad2, format2;
      if (!(date2 instanceof Date)) date2 = /* @__PURE__ */ new Date(+date2);
      while (++i5 < n6) {
        if (specifier.charCodeAt(i5) === 37) {
          string.push(specifier.slice(j5, i5));
          if ((pad2 = pads[c9 = specifier.charAt(++i5)]) != null) c9 = specifier.charAt(++i5);
          else pad2 = c9 === "e" ? " " : "0";
          if (format2 = formats2[c9]) c9 = format2(date2, pad2);
          string.push(c9);
          j5 = i5 + 1;
        }
      }
      string.push(specifier.slice(j5, i5));
      return string.join("");
    };
  }
  function newParse(specifier, Z2) {
    return function(string) {
      var d3 = newDate(1900, void 0, 1), i5 = parseSpecifier(d3, specifier, string += "", 0), week, day2;
      if (i5 != string.length) return null;
      if ("Q" in d3) return new Date(d3.Q);
      if ("s" in d3) return new Date(d3.s * 1e3 + ("L" in d3 ? d3.L : 0));
      if (Z2 && !("Z" in d3)) d3.Z = 0;
      if ("p" in d3) d3.H = d3.H % 12 + d3.p * 12;
      if (d3.m === void 0) d3.m = "q" in d3 ? d3.q : 0;
      if ("V" in d3) {
        if (d3.V < 1 || d3.V > 53) return null;
        if (!("w" in d3)) d3.w = 1;
        if ("Z" in d3) {
          week = utcDate(newDate(d3.y, 0, 1)), day2 = week.getUTCDay();
          week = day2 > 4 || day2 === 0 ? utcMonday2.ceil(week) : utcMonday2(week);
          week = utcDay_default.offset(week, (d3.V - 1) * 7);
          d3.y = week.getUTCFullYear();
          d3.m = week.getUTCMonth();
          d3.d = week.getUTCDate() + (d3.w + 6) % 7;
        } else {
          week = localDate(newDate(d3.y, 0, 1)), day2 = week.getDay();
          week = day2 > 4 || day2 === 0 ? monday.ceil(week) : monday(week);
          week = day_default.offset(week, (d3.V - 1) * 7);
          d3.y = week.getFullYear();
          d3.m = week.getMonth();
          d3.d = week.getDate() + (d3.w + 6) % 7;
        }
      } else if ("W" in d3 || "U" in d3) {
        if (!("w" in d3)) d3.w = "u" in d3 ? d3.u % 7 : "W" in d3 ? 1 : 0;
        day2 = "Z" in d3 ? utcDate(newDate(d3.y, 0, 1)).getUTCDay() : localDate(newDate(d3.y, 0, 1)).getDay();
        d3.m = 0;
        d3.d = "W" in d3 ? (d3.w + 6) % 7 + d3.W * 7 - (day2 + 5) % 7 : d3.w + d3.U * 7 - (day2 + 6) % 7;
      }
      if ("Z" in d3) {
        d3.H += d3.Z / 100 | 0;
        d3.M += d3.Z % 100;
        return utcDate(d3);
      }
      return localDate(d3);
    };
  }
  function parseSpecifier(d3, specifier, string, j5) {
    var i5 = 0, n6 = specifier.length, m5 = string.length, c9, parse;
    while (i5 < n6) {
      if (j5 >= m5) return -1;
      c9 = specifier.charCodeAt(i5++);
      if (c9 === 37) {
        c9 = specifier.charAt(i5++);
        parse = parses[c9 in pads ? specifier.charAt(i5++) : c9];
        if (!parse || (j5 = parse(d3, string, j5)) < 0) return -1;
      } else if (c9 != string.charCodeAt(j5++)) {
        return -1;
      }
    }
    return j5;
  }
  function parsePeriod(d3, string, i5) {
    var n6 = periodRe.exec(string.slice(i5));
    return n6 ? (d3.p = periodLookup.get(n6[0].toLowerCase()), i5 + n6[0].length) : -1;
  }
  function parseShortWeekday(d3, string, i5) {
    var n6 = shortWeekdayRe.exec(string.slice(i5));
    return n6 ? (d3.w = shortWeekdayLookup.get(n6[0].toLowerCase()), i5 + n6[0].length) : -1;
  }
  function parseWeekday(d3, string, i5) {
    var n6 = weekdayRe.exec(string.slice(i5));
    return n6 ? (d3.w = weekdayLookup.get(n6[0].toLowerCase()), i5 + n6[0].length) : -1;
  }
  function parseShortMonth(d3, string, i5) {
    var n6 = shortMonthRe.exec(string.slice(i5));
    return n6 ? (d3.m = shortMonthLookup.get(n6[0].toLowerCase()), i5 + n6[0].length) : -1;
  }
  function parseMonth(d3, string, i5) {
    var n6 = monthRe.exec(string.slice(i5));
    return n6 ? (d3.m = monthLookup.get(n6[0].toLowerCase()), i5 + n6[0].length) : -1;
  }
  function parseLocaleDateTime(d3, string, i5) {
    return parseSpecifier(d3, locale_dateTime, string, i5);
  }
  function parseLocaleDate(d3, string, i5) {
    return parseSpecifier(d3, locale_date, string, i5);
  }
  function parseLocaleTime(d3, string, i5) {
    return parseSpecifier(d3, locale_time, string, i5);
  }
  function formatShortWeekday(d3) {
    return locale_shortWeekdays[d3.getDay()];
  }
  function formatWeekday(d3) {
    return locale_weekdays[d3.getDay()];
  }
  function formatShortMonth(d3) {
    return locale_shortMonths[d3.getMonth()];
  }
  function formatMonth(d3) {
    return locale_months[d3.getMonth()];
  }
  function formatPeriod(d3) {
    return locale_periods[+(d3.getHours() >= 12)];
  }
  function formatQuarter(d3) {
    return 1 + ~~(d3.getMonth() / 3);
  }
  function formatUTCShortWeekday(d3) {
    return locale_shortWeekdays[d3.getUTCDay()];
  }
  function formatUTCWeekday(d3) {
    return locale_weekdays[d3.getUTCDay()];
  }
  function formatUTCShortMonth(d3) {
    return locale_shortMonths[d3.getUTCMonth()];
  }
  function formatUTCMonth(d3) {
    return locale_months[d3.getUTCMonth()];
  }
  function formatUTCPeriod(d3) {
    return locale_periods[+(d3.getUTCHours() >= 12)];
  }
  function formatUTCQuarter(d3) {
    return 1 + ~~(d3.getUTCMonth() / 3);
  }
  return {
    format: function(specifier) {
      var f3 = newFormat(specifier += "", formats);
      f3.toString = function() {
        return specifier;
      };
      return f3;
    },
    parse: function(specifier) {
      var p5 = newParse(specifier += "", false);
      p5.toString = function() {
        return specifier;
      };
      return p5;
    },
    utcFormat: function(specifier) {
      var f3 = newFormat(specifier += "", utcFormats);
      f3.toString = function() {
        return specifier;
      };
      return f3;
    },
    utcParse: function(specifier) {
      var p5 = newParse(specifier += "", true);
      p5.toString = function() {
        return specifier;
      };
      return p5;
    }
  };
}
var pads = { "-": "", "_": " ", "0": "0" };
var numberRe = /^\s*\d+/;
var percentRe = /^%/;
var requoteRe = /[\\^$*+?|[\]().{}]/g;
function pad(value, fill, width) {
  var sign2 = value < 0 ? "-" : "", string = (sign2 ? -value : value) + "", length = string.length;
  return sign2 + (length < width ? new Array(width - length + 1).join(fill) + string : string);
}
function requote(s4) {
  return s4.replace(requoteRe, "\\$&");
}
function formatRe(names) {
  return new RegExp("^(?:" + names.map(requote).join("|") + ")", "i");
}
function formatLookup(names) {
  return new Map(names.map((name, i5) => [name.toLowerCase(), i5]));
}
function parseWeekdayNumberSunday(d3, string, i5) {
  var n6 = numberRe.exec(string.slice(i5, i5 + 1));
  return n6 ? (d3.w = +n6[0], i5 + n6[0].length) : -1;
}
function parseWeekdayNumberMonday(d3, string, i5) {
  var n6 = numberRe.exec(string.slice(i5, i5 + 1));
  return n6 ? (d3.u = +n6[0], i5 + n6[0].length) : -1;
}
function parseWeekNumberSunday(d3, string, i5) {
  var n6 = numberRe.exec(string.slice(i5, i5 + 2));
  return n6 ? (d3.U = +n6[0], i5 + n6[0].length) : -1;
}
function parseWeekNumberISO(d3, string, i5) {
  var n6 = numberRe.exec(string.slice(i5, i5 + 2));
  return n6 ? (d3.V = +n6[0], i5 + n6[0].length) : -1;
}
function parseWeekNumberMonday(d3, string, i5) {
  var n6 = numberRe.exec(string.slice(i5, i5 + 2));
  return n6 ? (d3.W = +n6[0], i5 + n6[0].length) : -1;
}
function parseFullYear(d3, string, i5) {
  var n6 = numberRe.exec(string.slice(i5, i5 + 4));
  return n6 ? (d3.y = +n6[0], i5 + n6[0].length) : -1;
}
function parseYear(d3, string, i5) {
  var n6 = numberRe.exec(string.slice(i5, i5 + 2));
  return n6 ? (d3.y = +n6[0] + (+n6[0] > 68 ? 1900 : 2e3), i5 + n6[0].length) : -1;
}
function parseZone(d3, string, i5) {
  var n6 = /^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(string.slice(i5, i5 + 6));
  return n6 ? (d3.Z = n6[1] ? 0 : -(n6[2] + (n6[3] || "00")), i5 + n6[0].length) : -1;
}
function parseQuarter(d3, string, i5) {
  var n6 = numberRe.exec(string.slice(i5, i5 + 1));
  return n6 ? (d3.q = n6[0] * 3 - 3, i5 + n6[0].length) : -1;
}
function parseMonthNumber(d3, string, i5) {
  var n6 = numberRe.exec(string.slice(i5, i5 + 2));
  return n6 ? (d3.m = n6[0] - 1, i5 + n6[0].length) : -1;
}
function parseDayOfMonth(d3, string, i5) {
  var n6 = numberRe.exec(string.slice(i5, i5 + 2));
  return n6 ? (d3.d = +n6[0], i5 + n6[0].length) : -1;
}
function parseDayOfYear(d3, string, i5) {
  var n6 = numberRe.exec(string.slice(i5, i5 + 3));
  return n6 ? (d3.m = 0, d3.d = +n6[0], i5 + n6[0].length) : -1;
}
function parseHour24(d3, string, i5) {
  var n6 = numberRe.exec(string.slice(i5, i5 + 2));
  return n6 ? (d3.H = +n6[0], i5 + n6[0].length) : -1;
}
function parseMinutes(d3, string, i5) {
  var n6 = numberRe.exec(string.slice(i5, i5 + 2));
  return n6 ? (d3.M = +n6[0], i5 + n6[0].length) : -1;
}
function parseSeconds(d3, string, i5) {
  var n6 = numberRe.exec(string.slice(i5, i5 + 2));
  return n6 ? (d3.S = +n6[0], i5 + n6[0].length) : -1;
}
function parseMilliseconds(d3, string, i5) {
  var n6 = numberRe.exec(string.slice(i5, i5 + 3));
  return n6 ? (d3.L = +n6[0], i5 + n6[0].length) : -1;
}
function parseMicroseconds(d3, string, i5) {
  var n6 = numberRe.exec(string.slice(i5, i5 + 6));
  return n6 ? (d3.L = Math.floor(n6[0] / 1e3), i5 + n6[0].length) : -1;
}
function parseLiteralPercent(d3, string, i5) {
  var n6 = percentRe.exec(string.slice(i5, i5 + 1));
  return n6 ? i5 + n6[0].length : -1;
}
function parseUnixTimestamp(d3, string, i5) {
  var n6 = numberRe.exec(string.slice(i5));
  return n6 ? (d3.Q = +n6[0], i5 + n6[0].length) : -1;
}
function parseUnixTimestampSeconds(d3, string, i5) {
  var n6 = numberRe.exec(string.slice(i5));
  return n6 ? (d3.s = +n6[0], i5 + n6[0].length) : -1;
}
function formatDayOfMonth(d3, p5) {
  return pad(d3.getDate(), p5, 2);
}
function formatHour24(d3, p5) {
  return pad(d3.getHours(), p5, 2);
}
function formatHour12(d3, p5) {
  return pad(d3.getHours() % 12 || 12, p5, 2);
}
function formatDayOfYear(d3, p5) {
  return pad(1 + day_default.count(year_default(d3), d3), p5, 3);
}
function formatMilliseconds(d3, p5) {
  return pad(d3.getMilliseconds(), p5, 3);
}
function formatMicroseconds(d3, p5) {
  return formatMilliseconds(d3, p5) + "000";
}
function formatMonthNumber(d3, p5) {
  return pad(d3.getMonth() + 1, p5, 2);
}
function formatMinutes(d3, p5) {
  return pad(d3.getMinutes(), p5, 2);
}
function formatSeconds(d3, p5) {
  return pad(d3.getSeconds(), p5, 2);
}
function formatWeekdayNumberMonday(d3) {
  var day2 = d3.getDay();
  return day2 === 0 ? 7 : day2;
}
function formatWeekNumberSunday(d3, p5) {
  return pad(sunday.count(year_default(d3) - 1, d3), p5, 2);
}
function dISO(d3) {
  var day2 = d3.getDay();
  return day2 >= 4 || day2 === 0 ? thursday(d3) : thursday.ceil(d3);
}
function formatWeekNumberISO(d3, p5) {
  d3 = dISO(d3);
  return pad(thursday.count(year_default(d3), d3) + (year_default(d3).getDay() === 4), p5, 2);
}
function formatWeekdayNumberSunday(d3) {
  return d3.getDay();
}
function formatWeekNumberMonday(d3, p5) {
  return pad(monday.count(year_default(d3) - 1, d3), p5, 2);
}
function formatYear(d3, p5) {
  return pad(d3.getFullYear() % 100, p5, 2);
}
function formatYearISO(d3, p5) {
  d3 = dISO(d3);
  return pad(d3.getFullYear() % 100, p5, 2);
}
function formatFullYear(d3, p5) {
  return pad(d3.getFullYear() % 1e4, p5, 4);
}
function formatFullYearISO(d3, p5) {
  var day2 = d3.getDay();
  d3 = day2 >= 4 || day2 === 0 ? thursday(d3) : thursday.ceil(d3);
  return pad(d3.getFullYear() % 1e4, p5, 4);
}
function formatZone(d3) {
  var z5 = d3.getTimezoneOffset();
  return (z5 > 0 ? "-" : (z5 *= -1, "+")) + pad(z5 / 60 | 0, "0", 2) + pad(z5 % 60, "0", 2);
}
function formatUTCDayOfMonth(d3, p5) {
  return pad(d3.getUTCDate(), p5, 2);
}
function formatUTCHour24(d3, p5) {
  return pad(d3.getUTCHours(), p5, 2);
}
function formatUTCHour12(d3, p5) {
  return pad(d3.getUTCHours() % 12 || 12, p5, 2);
}
function formatUTCDayOfYear(d3, p5) {
  return pad(1 + utcDay_default.count(utcYear_default(d3), d3), p5, 3);
}
function formatUTCMilliseconds(d3, p5) {
  return pad(d3.getUTCMilliseconds(), p5, 3);
}
function formatUTCMicroseconds(d3, p5) {
  return formatUTCMilliseconds(d3, p5) + "000";
}
function formatUTCMonthNumber(d3, p5) {
  return pad(d3.getUTCMonth() + 1, p5, 2);
}
function formatUTCMinutes(d3, p5) {
  return pad(d3.getUTCMinutes(), p5, 2);
}
function formatUTCSeconds(d3, p5) {
  return pad(d3.getUTCSeconds(), p5, 2);
}
function formatUTCWeekdayNumberMonday(d3) {
  var dow = d3.getUTCDay();
  return dow === 0 ? 7 : dow;
}
function formatUTCWeekNumberSunday(d3, p5) {
  return pad(utcSunday2.count(utcYear_default(d3) - 1, d3), p5, 2);
}
function UTCdISO(d3) {
  var day2 = d3.getUTCDay();
  return day2 >= 4 || day2 === 0 ? utcThursday2(d3) : utcThursday2.ceil(d3);
}
function formatUTCWeekNumberISO(d3, p5) {
  d3 = UTCdISO(d3);
  return pad(utcThursday2.count(utcYear_default(d3), d3) + (utcYear_default(d3).getUTCDay() === 4), p5, 2);
}
function formatUTCWeekdayNumberSunday(d3) {
  return d3.getUTCDay();
}
function formatUTCWeekNumberMonday(d3, p5) {
  return pad(utcMonday2.count(utcYear_default(d3) - 1, d3), p5, 2);
}
function formatUTCYear(d3, p5) {
  return pad(d3.getUTCFullYear() % 100, p5, 2);
}
function formatUTCYearISO(d3, p5) {
  d3 = UTCdISO(d3);
  return pad(d3.getUTCFullYear() % 100, p5, 2);
}
function formatUTCFullYear(d3, p5) {
  return pad(d3.getUTCFullYear() % 1e4, p5, 4);
}
function formatUTCFullYearISO(d3, p5) {
  var day2 = d3.getUTCDay();
  d3 = day2 >= 4 || day2 === 0 ? utcThursday2(d3) : utcThursday2.ceil(d3);
  return pad(d3.getUTCFullYear() % 1e4, p5, 4);
}
function formatUTCZone() {
  return "+0000";
}
function formatLiteralPercent() {
  return "%";
}
function formatUnixTimestamp(d3) {
  return +d3;
}
function formatUnixTimestampSeconds(d3) {
  return Math.floor(+d3 / 1e3);
}

// node_modules/d3-time-format/src/defaultLocale.js
var locale2;
var timeFormat;
var timeParse;
var utcFormat;
var utcParse;
defaultLocale2({
  dateTime: "%x, %X",
  date: "%-m/%-d/%Y",
  time: "%-I:%M:%S %p",
  periods: ["AM", "PM"],
  days: ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"],
  shortDays: ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"],
  months: ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"],
  shortMonths: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
});
function defaultLocale2(definition) {
  locale2 = formatLocale(definition);
  timeFormat = locale2.format;
  timeParse = locale2.parse;
  utcFormat = locale2.utcFormat;
  utcParse = locale2.utcParse;
  return locale2;
}

// node_modules/d3-time-format/src/isoFormat.js
var isoSpecifier = "%Y-%m-%dT%H:%M:%S.%LZ";
function formatIsoNative(date2) {
  return date2.toISOString();
}
var formatIso = Date.prototype.toISOString ? formatIsoNative : utcFormat(isoSpecifier);

// node_modules/d3-time-format/src/isoParse.js
function parseIsoNative(string) {
  var date2 = new Date(string);
  return isNaN(date2) ? null : date2;
}
var parseIso = +/* @__PURE__ */ new Date("2000-01-01T00:00:00.000Z") ? parseIsoNative : utcParse(isoSpecifier);

// node_modules/d3-scale/src/time.js
function date(t9) {
  return new Date(t9);
}
function number3(t9) {
  return t9 instanceof Date ? +t9 : +/* @__PURE__ */ new Date(+t9);
}
function calendar(ticks2, tickInterval, year2, month2, week, day2, hour2, minute2, second3, format2) {
  var scale = continuous(), invert = scale.invert, domain = scale.domain;
  var formatMillisecond = format2(".%L"), formatSecond = format2(":%S"), formatMinute = format2("%I:%M"), formatHour = format2("%I %p"), formatDay = format2("%a %d"), formatWeek = format2("%b %d"), formatMonth = format2("%B"), formatYear2 = format2("%Y");
  function tickFormat2(date2) {
    return (second3(date2) < date2 ? formatMillisecond : minute2(date2) < date2 ? formatSecond : hour2(date2) < date2 ? formatMinute : day2(date2) < date2 ? formatHour : month2(date2) < date2 ? week(date2) < date2 ? formatDay : formatWeek : year2(date2) < date2 ? formatMonth : formatYear2)(date2);
  }
  scale.invert = function(y5) {
    return new Date(invert(y5));
  };
  scale.domain = function(_3) {
    return arguments.length ? domain(Array.from(_3, number3)) : domain().map(date);
  };
  scale.ticks = function(interval) {
    var d3 = domain();
    return ticks2(d3[0], d3[d3.length - 1], interval == null ? 10 : interval);
  };
  scale.tickFormat = function(count2, specifier) {
    return specifier == null ? tickFormat2 : format2(specifier);
  };
  scale.nice = function(interval) {
    var d3 = domain();
    if (!interval || typeof interval.range !== "function") interval = tickInterval(d3[0], d3[d3.length - 1], interval == null ? 10 : interval);
    return interval ? domain(nice2(d3, interval)) : scale;
  };
  scale.copy = function() {
    return copy(scale, calendar(ticks2, tickInterval, year2, month2, week, day2, hour2, minute2, second3, format2));
  };
  return scale;
}
function time() {
  return initRange.apply(calendar(timeTicks, timeTickInterval, timeYear, timeMonth, timeSunday, timeDay, timeHour, timeMinute, second, timeFormat).domain([new Date(2e3, 0, 1), new Date(2e3, 0, 2)]), arguments);
}

// node_modules/d3-scale/src/utcTime.js
function utcTime() {
  return initRange.apply(calendar(utcTicks, utcTickInterval, utcYear, utcMonth, utcSunday, utcDay, utcHour, utcMinute, second, utcFormat).domain([Date.UTC(2e3, 0, 1), Date.UTC(2e3, 0, 2)]), arguments);
}

// node_modules/d3-scale-chromatic/src/colors.js
function colors_default(specifier) {
  var n6 = specifier.length / 6 | 0, colors3 = new Array(n6), i5 = 0;
  while (i5 < n6) colors3[i5] = "#" + specifier.slice(i5 * 6, ++i5 * 6);
  return colors3;
}

// node_modules/d3-scale-chromatic/src/categorical/category10.js
var category10_default = colors_default("1f77b4ff7f0e2ca02cd627289467bd8c564be377c27f7f7fbcbd2217becf");

// node_modules/d3-scale-chromatic/src/categorical/Accent.js
var Accent_default = colors_default("7fc97fbeaed4fdc086ffff99386cb0f0027fbf5b17666666");

// node_modules/d3-scale-chromatic/src/categorical/Dark2.js
var Dark2_default = colors_default("1b9e77d95f027570b3e7298a66a61ee6ab02a6761d666666");

// node_modules/d3-scale-chromatic/src/categorical/observable10.js
var observable10_default = colors_default("4269d0efb118ff725c6cc5b03ca951ff8ab7a463f297bbf59c6b4e9498a0");

// node_modules/d3-scale-chromatic/src/categorical/Paired.js
var Paired_default = colors_default("a6cee31f78b4b2df8a33a02cfb9a99e31a1cfdbf6fff7f00cab2d66a3d9affff99b15928");

// node_modules/d3-scale-chromatic/src/categorical/Pastel1.js
var Pastel1_default = colors_default("fbb4aeb3cde3ccebc5decbe4fed9a6ffffcce5d8bdfddaecf2f2f2");

// node_modules/d3-scale-chromatic/src/categorical/Pastel2.js
var Pastel2_default = colors_default("b3e2cdfdcdaccbd5e8f4cae4e6f5c9fff2aef1e2cccccccc");

// node_modules/d3-scale-chromatic/src/categorical/Set1.js
var Set1_default = colors_default("e41a1c377eb84daf4a984ea3ff7f00ffff33a65628f781bf999999");

// node_modules/d3-scale-chromatic/src/categorical/Set2.js
var Set2_default = colors_default("66c2a5fc8d628da0cbe78ac3a6d854ffd92fe5c494b3b3b3");

// node_modules/d3-scale-chromatic/src/categorical/Set3.js
var Set3_default = colors_default("8dd3c7ffffb3bebadafb807280b1d3fdb462b3de69fccde5d9d9d9bc80bdccebc5ffed6f");

// node_modules/d3-scale-chromatic/src/categorical/Tableau10.js
var Tableau10_default = colors_default("4e79a7f28e2ce1575976b7b259a14fedc949af7aa1ff9da79c755fbab0ab");

// node_modules/d3-scale-chromatic/src/ramp.js
var ramp_default = (scheme28) => rgbBasis(scheme28[scheme28.length - 1]);

// node_modules/d3-scale-chromatic/src/diverging/BrBG.js
var scheme = new Array(3).concat(
  "d8b365f5f5f55ab4ac",
  "a6611adfc27d80cdc1018571",
  "a6611adfc27df5f5f580cdc1018571",
  "8c510ad8b365f6e8c3c7eae55ab4ac01665e",
  "8c510ad8b365f6e8c3f5f5f5c7eae55ab4ac01665e",
  "8c510abf812ddfc27df6e8c3c7eae580cdc135978f01665e",
  "8c510abf812ddfc27df6e8c3f5f5f5c7eae580cdc135978f01665e",
  "5430058c510abf812ddfc27df6e8c3c7eae580cdc135978f01665e003c30",
  "5430058c510abf812ddfc27df6e8c3f5f5f5c7eae580cdc135978f01665e003c30"
).map(colors_default);
var BrBG_default = ramp_default(scheme);

// node_modules/d3-scale-chromatic/src/diverging/PRGn.js
var scheme2 = new Array(3).concat(
  "af8dc3f7f7f77fbf7b",
  "7b3294c2a5cfa6dba0008837",
  "7b3294c2a5cff7f7f7a6dba0008837",
  "762a83af8dc3e7d4e8d9f0d37fbf7b1b7837",
  "762a83af8dc3e7d4e8f7f7f7d9f0d37fbf7b1b7837",
  "762a839970abc2a5cfe7d4e8d9f0d3a6dba05aae611b7837",
  "762a839970abc2a5cfe7d4e8f7f7f7d9f0d3a6dba05aae611b7837",
  "40004b762a839970abc2a5cfe7d4e8d9f0d3a6dba05aae611b783700441b",
  "40004b762a839970abc2a5cfe7d4e8f7f7f7d9f0d3a6dba05aae611b783700441b"
).map(colors_default);
var PRGn_default = ramp_default(scheme2);

// node_modules/d3-scale-chromatic/src/diverging/PiYG.js
var scheme3 = new Array(3).concat(
  "e9a3c9f7f7f7a1d76a",
  "d01c8bf1b6dab8e1864dac26",
  "d01c8bf1b6daf7f7f7b8e1864dac26",
  "c51b7de9a3c9fde0efe6f5d0a1d76a4d9221",
  "c51b7de9a3c9fde0eff7f7f7e6f5d0a1d76a4d9221",
  "c51b7dde77aef1b6dafde0efe6f5d0b8e1867fbc414d9221",
  "c51b7dde77aef1b6dafde0eff7f7f7e6f5d0b8e1867fbc414d9221",
  "8e0152c51b7dde77aef1b6dafde0efe6f5d0b8e1867fbc414d9221276419",
  "8e0152c51b7dde77aef1b6dafde0eff7f7f7e6f5d0b8e1867fbc414d9221276419"
).map(colors_default);
var PiYG_default = ramp_default(scheme3);

// node_modules/d3-scale-chromatic/src/diverging/PuOr.js
var scheme4 = new Array(3).concat(
  "998ec3f7f7f7f1a340",
  "5e3c99b2abd2fdb863e66101",
  "5e3c99b2abd2f7f7f7fdb863e66101",
  "542788998ec3d8daebfee0b6f1a340b35806",
  "542788998ec3d8daebf7f7f7fee0b6f1a340b35806",
  "5427888073acb2abd2d8daebfee0b6fdb863e08214b35806",
  "5427888073acb2abd2d8daebf7f7f7fee0b6fdb863e08214b35806",
  "2d004b5427888073acb2abd2d8daebfee0b6fdb863e08214b358067f3b08",
  "2d004b5427888073acb2abd2d8daebf7f7f7fee0b6fdb863e08214b358067f3b08"
).map(colors_default);
var PuOr_default = ramp_default(scheme4);

// node_modules/d3-scale-chromatic/src/diverging/RdBu.js
var scheme5 = new Array(3).concat(
  "ef8a62f7f7f767a9cf",
  "ca0020f4a58292c5de0571b0",
  "ca0020f4a582f7f7f792c5de0571b0",
  "b2182bef8a62fddbc7d1e5f067a9cf2166ac",
  "b2182bef8a62fddbc7f7f7f7d1e5f067a9cf2166ac",
  "b2182bd6604df4a582fddbc7d1e5f092c5de4393c32166ac",
  "b2182bd6604df4a582fddbc7f7f7f7d1e5f092c5de4393c32166ac",
  "67001fb2182bd6604df4a582fddbc7d1e5f092c5de4393c32166ac053061",
  "67001fb2182bd6604df4a582fddbc7f7f7f7d1e5f092c5de4393c32166ac053061"
).map(colors_default);
var RdBu_default = ramp_default(scheme5);

// node_modules/d3-scale-chromatic/src/diverging/RdGy.js
var scheme6 = new Array(3).concat(
  "ef8a62ffffff999999",
  "ca0020f4a582bababa404040",
  "ca0020f4a582ffffffbababa404040",
  "b2182bef8a62fddbc7e0e0e09999994d4d4d",
  "b2182bef8a62fddbc7ffffffe0e0e09999994d4d4d",
  "b2182bd6604df4a582fddbc7e0e0e0bababa8787874d4d4d",
  "b2182bd6604df4a582fddbc7ffffffe0e0e0bababa8787874d4d4d",
  "67001fb2182bd6604df4a582fddbc7e0e0e0bababa8787874d4d4d1a1a1a",
  "67001fb2182bd6604df4a582fddbc7ffffffe0e0e0bababa8787874d4d4d1a1a1a"
).map(colors_default);
var RdGy_default = ramp_default(scheme6);

// node_modules/d3-scale-chromatic/src/diverging/RdYlBu.js
var scheme7 = new Array(3).concat(
  "fc8d59ffffbf91bfdb",
  "d7191cfdae61abd9e92c7bb6",
  "d7191cfdae61ffffbfabd9e92c7bb6",
  "d73027fc8d59fee090e0f3f891bfdb4575b4",
  "d73027fc8d59fee090ffffbfe0f3f891bfdb4575b4",
  "d73027f46d43fdae61fee090e0f3f8abd9e974add14575b4",
  "d73027f46d43fdae61fee090ffffbfe0f3f8abd9e974add14575b4",
  "a50026d73027f46d43fdae61fee090e0f3f8abd9e974add14575b4313695",
  "a50026d73027f46d43fdae61fee090ffffbfe0f3f8abd9e974add14575b4313695"
).map(colors_default);
var RdYlBu_default = ramp_default(scheme7);

// node_modules/d3-scale-chromatic/src/diverging/RdYlGn.js
var scheme8 = new Array(3).concat(
  "fc8d59ffffbf91cf60",
  "d7191cfdae61a6d96a1a9641",
  "d7191cfdae61ffffbfa6d96a1a9641",
  "d73027fc8d59fee08bd9ef8b91cf601a9850",
  "d73027fc8d59fee08bffffbfd9ef8b91cf601a9850",
  "d73027f46d43fdae61fee08bd9ef8ba6d96a66bd631a9850",
  "d73027f46d43fdae61fee08bffffbfd9ef8ba6d96a66bd631a9850",
  "a50026d73027f46d43fdae61fee08bd9ef8ba6d96a66bd631a9850006837",
  "a50026d73027f46d43fdae61fee08bffffbfd9ef8ba6d96a66bd631a9850006837"
).map(colors_default);
var RdYlGn_default = ramp_default(scheme8);

// node_modules/d3-scale-chromatic/src/diverging/Spectral.js
var scheme9 = new Array(3).concat(
  "fc8d59ffffbf99d594",
  "d7191cfdae61abdda42b83ba",
  "d7191cfdae61ffffbfabdda42b83ba",
  "d53e4ffc8d59fee08be6f59899d5943288bd",
  "d53e4ffc8d59fee08bffffbfe6f59899d5943288bd",
  "d53e4ff46d43fdae61fee08be6f598abdda466c2a53288bd",
  "d53e4ff46d43fdae61fee08bffffbfe6f598abdda466c2a53288bd",
  "9e0142d53e4ff46d43fdae61fee08be6f598abdda466c2a53288bd5e4fa2",
  "9e0142d53e4ff46d43fdae61fee08bffffbfe6f598abdda466c2a53288bd5e4fa2"
).map(colors_default);
var Spectral_default = ramp_default(scheme9);

// node_modules/d3-scale-chromatic/src/sequential-multi/BuGn.js
var scheme10 = new Array(3).concat(
  "e5f5f999d8c92ca25f",
  "edf8fbb2e2e266c2a4238b45",
  "edf8fbb2e2e266c2a42ca25f006d2c",
  "edf8fbccece699d8c966c2a42ca25f006d2c",
  "edf8fbccece699d8c966c2a441ae76238b45005824",
  "f7fcfde5f5f9ccece699d8c966c2a441ae76238b45005824",
  "f7fcfde5f5f9ccece699d8c966c2a441ae76238b45006d2c00441b"
).map(colors_default);
var BuGn_default = ramp_default(scheme10);

// node_modules/d3-scale-chromatic/src/sequential-multi/BuPu.js
var scheme11 = new Array(3).concat(
  "e0ecf49ebcda8856a7",
  "edf8fbb3cde38c96c688419d",
  "edf8fbb3cde38c96c68856a7810f7c",
  "edf8fbbfd3e69ebcda8c96c68856a7810f7c",
  "edf8fbbfd3e69ebcda8c96c68c6bb188419d6e016b",
  "f7fcfde0ecf4bfd3e69ebcda8c96c68c6bb188419d6e016b",
  "f7fcfde0ecf4bfd3e69ebcda8c96c68c6bb188419d810f7c4d004b"
).map(colors_default);
var BuPu_default = ramp_default(scheme11);

// node_modules/d3-scale-chromatic/src/sequential-multi/GnBu.js
var scheme12 = new Array(3).concat(
  "e0f3dba8ddb543a2ca",
  "f0f9e8bae4bc7bccc42b8cbe",
  "f0f9e8bae4bc7bccc443a2ca0868ac",
  "f0f9e8ccebc5a8ddb57bccc443a2ca0868ac",
  "f0f9e8ccebc5a8ddb57bccc44eb3d32b8cbe08589e",
  "f7fcf0e0f3dbccebc5a8ddb57bccc44eb3d32b8cbe08589e",
  "f7fcf0e0f3dbccebc5a8ddb57bccc44eb3d32b8cbe0868ac084081"
).map(colors_default);
var GnBu_default = ramp_default(scheme12);

// node_modules/d3-scale-chromatic/src/sequential-multi/OrRd.js
var scheme13 = new Array(3).concat(
  "fee8c8fdbb84e34a33",
  "fef0d9fdcc8afc8d59d7301f",
  "fef0d9fdcc8afc8d59e34a33b30000",
  "fef0d9fdd49efdbb84fc8d59e34a33b30000",
  "fef0d9fdd49efdbb84fc8d59ef6548d7301f990000",
  "fff7ecfee8c8fdd49efdbb84fc8d59ef6548d7301f990000",
  "fff7ecfee8c8fdd49efdbb84fc8d59ef6548d7301fb300007f0000"
).map(colors_default);
var OrRd_default = ramp_default(scheme13);

// node_modules/d3-scale-chromatic/src/sequential-multi/PuBuGn.js
var scheme14 = new Array(3).concat(
  "ece2f0a6bddb1c9099",
  "f6eff7bdc9e167a9cf02818a",
  "f6eff7bdc9e167a9cf1c9099016c59",
  "f6eff7d0d1e6a6bddb67a9cf1c9099016c59",
  "f6eff7d0d1e6a6bddb67a9cf3690c002818a016450",
  "fff7fbece2f0d0d1e6a6bddb67a9cf3690c002818a016450",
  "fff7fbece2f0d0d1e6a6bddb67a9cf3690c002818a016c59014636"
).map(colors_default);
var PuBuGn_default = ramp_default(scheme14);

// node_modules/d3-scale-chromatic/src/sequential-multi/PuBu.js
var scheme15 = new Array(3).concat(
  "ece7f2a6bddb2b8cbe",
  "f1eef6bdc9e174a9cf0570b0",
  "f1eef6bdc9e174a9cf2b8cbe045a8d",
  "f1eef6d0d1e6a6bddb74a9cf2b8cbe045a8d",
  "f1eef6d0d1e6a6bddb74a9cf3690c00570b0034e7b",
  "fff7fbece7f2d0d1e6a6bddb74a9cf3690c00570b0034e7b",
  "fff7fbece7f2d0d1e6a6bddb74a9cf3690c00570b0045a8d023858"
).map(colors_default);
var PuBu_default = ramp_default(scheme15);

// node_modules/d3-scale-chromatic/src/sequential-multi/PuRd.js
var scheme16 = new Array(3).concat(
  "e7e1efc994c7dd1c77",
  "f1eef6d7b5d8df65b0ce1256",
  "f1eef6d7b5d8df65b0dd1c77980043",
  "f1eef6d4b9dac994c7df65b0dd1c77980043",
  "f1eef6d4b9dac994c7df65b0e7298ace125691003f",
  "f7f4f9e7e1efd4b9dac994c7df65b0e7298ace125691003f",
  "f7f4f9e7e1efd4b9dac994c7df65b0e7298ace125698004367001f"
).map(colors_default);
var PuRd_default = ramp_default(scheme16);

// node_modules/d3-scale-chromatic/src/sequential-multi/RdPu.js
var scheme17 = new Array(3).concat(
  "fde0ddfa9fb5c51b8a",
  "feebe2fbb4b9f768a1ae017e",
  "feebe2fbb4b9f768a1c51b8a7a0177",
  "feebe2fcc5c0fa9fb5f768a1c51b8a7a0177",
  "feebe2fcc5c0fa9fb5f768a1dd3497ae017e7a0177",
  "fff7f3fde0ddfcc5c0fa9fb5f768a1dd3497ae017e7a0177",
  "fff7f3fde0ddfcc5c0fa9fb5f768a1dd3497ae017e7a017749006a"
).map(colors_default);
var RdPu_default = ramp_default(scheme17);

// node_modules/d3-scale-chromatic/src/sequential-multi/YlGnBu.js
var scheme18 = new Array(3).concat(
  "edf8b17fcdbb2c7fb8",
  "ffffcca1dab441b6c4225ea8",
  "ffffcca1dab441b6c42c7fb8253494",
  "ffffccc7e9b47fcdbb41b6c42c7fb8253494",
  "ffffccc7e9b47fcdbb41b6c41d91c0225ea80c2c84",
  "ffffd9edf8b1c7e9b47fcdbb41b6c41d91c0225ea80c2c84",
  "ffffd9edf8b1c7e9b47fcdbb41b6c41d91c0225ea8253494081d58"
).map(colors_default);
var YlGnBu_default = ramp_default(scheme18);

// node_modules/d3-scale-chromatic/src/sequential-multi/YlGn.js
var scheme19 = new Array(3).concat(
  "f7fcb9addd8e31a354",
  "ffffccc2e69978c679238443",
  "ffffccc2e69978c67931a354006837",
  "ffffccd9f0a3addd8e78c67931a354006837",
  "ffffccd9f0a3addd8e78c67941ab5d238443005a32",
  "ffffe5f7fcb9d9f0a3addd8e78c67941ab5d238443005a32",
  "ffffe5f7fcb9d9f0a3addd8e78c67941ab5d238443006837004529"
).map(colors_default);
var YlGn_default = ramp_default(scheme19);

// node_modules/d3-scale-chromatic/src/sequential-multi/YlOrBr.js
var scheme20 = new Array(3).concat(
  "fff7bcfec44fd95f0e",
  "ffffd4fed98efe9929cc4c02",
  "ffffd4fed98efe9929d95f0e993404",
  "ffffd4fee391fec44ffe9929d95f0e993404",
  "ffffd4fee391fec44ffe9929ec7014cc4c028c2d04",
  "ffffe5fff7bcfee391fec44ffe9929ec7014cc4c028c2d04",
  "ffffe5fff7bcfee391fec44ffe9929ec7014cc4c02993404662506"
).map(colors_default);
var YlOrBr_default = ramp_default(scheme20);

// node_modules/d3-scale-chromatic/src/sequential-multi/YlOrRd.js
var scheme21 = new Array(3).concat(
  "ffeda0feb24cf03b20",
  "ffffb2fecc5cfd8d3ce31a1c",
  "ffffb2fecc5cfd8d3cf03b20bd0026",
  "ffffb2fed976feb24cfd8d3cf03b20bd0026",
  "ffffb2fed976feb24cfd8d3cfc4e2ae31a1cb10026",
  "ffffccffeda0fed976feb24cfd8d3cfc4e2ae31a1cb10026",
  "ffffccffeda0fed976feb24cfd8d3cfc4e2ae31a1cbd0026800026"
).map(colors_default);
var YlOrRd_default = ramp_default(scheme21);

// node_modules/d3-scale-chromatic/src/sequential-single/Blues.js
var scheme22 = new Array(3).concat(
  "deebf79ecae13182bd",
  "eff3ffbdd7e76baed62171b5",
  "eff3ffbdd7e76baed63182bd08519c",
  "eff3ffc6dbef9ecae16baed63182bd08519c",
  "eff3ffc6dbef9ecae16baed64292c62171b5084594",
  "f7fbffdeebf7c6dbef9ecae16baed64292c62171b5084594",
  "f7fbffdeebf7c6dbef9ecae16baed64292c62171b508519c08306b"
).map(colors_default);
var Blues_default = ramp_default(scheme22);

// node_modules/d3-scale-chromatic/src/sequential-single/Greens.js
var scheme23 = new Array(3).concat(
  "e5f5e0a1d99b31a354",
  "edf8e9bae4b374c476238b45",
  "edf8e9bae4b374c47631a354006d2c",
  "edf8e9c7e9c0a1d99b74c47631a354006d2c",
  "edf8e9c7e9c0a1d99b74c47641ab5d238b45005a32",
  "f7fcf5e5f5e0c7e9c0a1d99b74c47641ab5d238b45005a32",
  "f7fcf5e5f5e0c7e9c0a1d99b74c47641ab5d238b45006d2c00441b"
).map(colors_default);
var Greens_default = ramp_default(scheme23);

// node_modules/d3-scale-chromatic/src/sequential-single/Greys.js
var scheme24 = new Array(3).concat(
  "f0f0f0bdbdbd636363",
  "f7f7f7cccccc969696525252",
  "f7f7f7cccccc969696636363252525",
  "f7f7f7d9d9d9bdbdbd969696636363252525",
  "f7f7f7d9d9d9bdbdbd969696737373525252252525",
  "fffffff0f0f0d9d9d9bdbdbd969696737373525252252525",
  "fffffff0f0f0d9d9d9bdbdbd969696737373525252252525000000"
).map(colors_default);
var Greys_default = ramp_default(scheme24);

// node_modules/d3-scale-chromatic/src/sequential-single/Purples.js
var scheme25 = new Array(3).concat(
  "efedf5bcbddc756bb1",
  "f2f0f7cbc9e29e9ac86a51a3",
  "f2f0f7cbc9e29e9ac8756bb154278f",
  "f2f0f7dadaebbcbddc9e9ac8756bb154278f",
  "f2f0f7dadaebbcbddc9e9ac8807dba6a51a34a1486",
  "fcfbfdefedf5dadaebbcbddc9e9ac8807dba6a51a34a1486",
  "fcfbfdefedf5dadaebbcbddc9e9ac8807dba6a51a354278f3f007d"
).map(colors_default);
var Purples_default = ramp_default(scheme25);

// node_modules/d3-scale-chromatic/src/sequential-single/Reds.js
var scheme26 = new Array(3).concat(
  "fee0d2fc9272de2d26",
  "fee5d9fcae91fb6a4acb181d",
  "fee5d9fcae91fb6a4ade2d26a50f15",
  "fee5d9fcbba1fc9272fb6a4ade2d26a50f15",
  "fee5d9fcbba1fc9272fb6a4aef3b2ccb181d99000d",
  "fff5f0fee0d2fcbba1fc9272fb6a4aef3b2ccb181d99000d",
  "fff5f0fee0d2fcbba1fc9272fb6a4aef3b2ccb181da50f1567000d"
).map(colors_default);
var Reds_default = ramp_default(scheme26);

// node_modules/d3-scale-chromatic/src/sequential-single/Oranges.js
var scheme27 = new Array(3).concat(
  "fee6cefdae6be6550d",
  "feeddefdbe85fd8d3cd94701",
  "feeddefdbe85fd8d3ce6550da63603",
  "feeddefdd0a2fdae6bfd8d3ce6550da63603",
  "feeddefdd0a2fdae6bfd8d3cf16913d948018c2d04",
  "fff5ebfee6cefdd0a2fdae6bfd8d3cf16913d948018c2d04",
  "fff5ebfee6cefdd0a2fdae6bfd8d3cf16913d94801a636037f2704"
).map(colors_default);
var Oranges_default = ramp_default(scheme27);

// node_modules/d3-scale-chromatic/src/sequential-multi/cividis.js
function cividis_default(t9) {
  t9 = Math.max(0, Math.min(1, t9));
  return "rgb(" + Math.max(0, Math.min(255, Math.round(-4.54 - t9 * (35.34 - t9 * (2381.73 - t9 * (6402.7 - t9 * (7024.72 - t9 * 2710.57))))))) + ", " + Math.max(0, Math.min(255, Math.round(32.49 + t9 * (170.73 + t9 * (52.82 - t9 * (131.46 - t9 * (176.58 - t9 * 67.37))))))) + ", " + Math.max(0, Math.min(255, Math.round(81.24 + t9 * (442.36 - t9 * (2482.43 - t9 * (6167.24 - t9 * (6614.94 - t9 * 2475.67))))))) + ")";
}

// node_modules/d3-scale-chromatic/src/sequential-multi/cubehelix.js
var cubehelix_default2 = cubehelixLong(cubehelix(300, 0.5, 0), cubehelix(-240, 0.5, 1));

// node_modules/d3-scale-chromatic/src/sequential-multi/rainbow.js
var warm = cubehelixLong(cubehelix(-100, 0.75, 0.35), cubehelix(80, 1.5, 0.8));
var cool = cubehelixLong(cubehelix(260, 0.75, 0.35), cubehelix(80, 1.5, 0.8));
var c = cubehelix();
function rainbow_default(t9) {
  if (t9 < 0 || t9 > 1) t9 -= Math.floor(t9);
  var ts2 = Math.abs(t9 - 0.5);
  c.h = 360 * t9 - 100;
  c.s = 1.5 - 1.5 * ts2;
  c.l = 0.8 - 0.9 * ts2;
  return c + "";
}

// node_modules/d3-scale-chromatic/src/sequential-multi/sinebow.js
var c6 = rgb2();
var pi_1_3 = Math.PI / 3;
var pi_2_3 = Math.PI * 2 / 3;
function sinebow_default(t9) {
  var x5;
  t9 = (0.5 - t9) * Math.PI;
  c6.r = 255 * (x5 = Math.sin(t9)) * x5;
  c6.g = 255 * (x5 = Math.sin(t9 + pi_1_3)) * x5;
  c6.b = 255 * (x5 = Math.sin(t9 + pi_2_3)) * x5;
  return c6 + "";
}

// node_modules/d3-scale-chromatic/src/sequential-multi/turbo.js
function turbo_default(t9) {
  t9 = Math.max(0, Math.min(1, t9));
  return "rgb(" + Math.max(0, Math.min(255, Math.round(34.61 + t9 * (1172.33 - t9 * (10793.56 - t9 * (33300.12 - t9 * (38394.49 - t9 * 14825.05))))))) + ", " + Math.max(0, Math.min(255, Math.round(23.31 + t9 * (557.33 + t9 * (1225.33 - t9 * (3574.96 - t9 * (1073.77 + t9 * 707.56))))))) + ", " + Math.max(0, Math.min(255, Math.round(27.2 + t9 * (3211.1 - t9 * (15327.97 - t9 * (27814 - t9 * (22569.18 - t9 * 6838.66))))))) + ")";
}

// node_modules/d3-scale-chromatic/src/sequential-multi/viridis.js
function ramp(range2) {
  var n6 = range2.length;
  return function(t9) {
    return range2[Math.max(0, Math.min(n6 - 1, Math.floor(t9 * n6)))];
  };
}
var viridis_default = ramp(colors_default("44015444025645045745055946075a46085c460a5d460b5e470d60470e6147106347116447136548146748166848176948186a481a6c481b6d481c6e481d6f481f70482071482173482374482475482576482677482878482979472a7a472c7a472d7b472e7c472f7d46307e46327e46337f463480453581453781453882443983443a83443b84433d84433e85423f854240864241864142874144874045884046883f47883f48893e49893e4a893e4c8a3d4d8a3d4e8a3c4f8a3c508b3b518b3b528b3a538b3a548c39558c39568c38588c38598c375a8c375b8d365c8d365d8d355e8d355f8d34608d34618d33628d33638d32648e32658e31668e31678e31688e30698e306a8e2f6b8e2f6c8e2e6d8e2e6e8e2e6f8e2d708e2d718e2c718e2c728e2c738e2b748e2b758e2a768e2a778e2a788e29798e297a8e297b8e287c8e287d8e277e8e277f8e27808e26818e26828e26828e25838e25848e25858e24868e24878e23888e23898e238a8d228b8d228c8d228d8d218e8d218f8d21908d21918c20928c20928c20938c1f948c1f958b1f968b1f978b1f988b1f998a1f9a8a1e9b8a1e9c891e9d891f9e891f9f881fa0881fa1881fa1871fa28720a38620a48621a58521a68522a78522a88423a98324aa8325ab8225ac8226ad8127ad8128ae8029af7f2ab07f2cb17e2db27d2eb37c2fb47c31b57b32b67a34b67935b77937b87838b9773aba763bbb753dbc743fbc7340bd7242be7144bf7046c06f48c16e4ac16d4cc26c4ec36b50c46a52c56954c56856c66758c7655ac8645cc8635ec96260ca6063cb5f65cb5e67cc5c69cd5b6ccd5a6ece5870cf5773d05675d05477d1537ad1517cd2507fd34e81d34d84d44b86d54989d5488bd6468ed64590d74393d74195d84098d83e9bd93c9dd93ba0da39a2da37a5db36a8db34aadc32addc30b0dd2fb2dd2db5de2bb8de29bade28bddf26c0df25c2df23c5e021c8e020cae11fcde11dd0e11cd2e21bd5e21ad8e219dae319dde318dfe318e2e418e5e419e7e419eae51aece51befe51cf1e51df4e61ef6e620f8e621fbe723fde725"));
var magma = ramp(colors_default("00000401000501010601010802010902020b02020d03030f03031204041405041606051806051a07061c08071e0907200a08220b09240c09260d0a290e0b2b100b2d110c2f120d31130d34140e36150e38160f3b180f3d19103f1a10421c10441d11471e114920114b21114e22115024125325125527125829115a2a115c2c115f2d11612f116331116533106734106936106b38106c390f6e3b0f703d0f713f0f72400f74420f75440f764510774710784910784a10794c117a4e117b4f127b51127c52137c54137d56147d57157e59157e5a167e5c167f5d177f5f187f601880621980641a80651a80671b80681c816a1c816b1d816d1d816e1e81701f81721f817320817521817621817822817922827b23827c23827e24828025828125818326818426818627818827818928818b29818c29818e2a81902a81912b81932b80942c80962c80982d80992d809b2e7f9c2e7f9e2f7fa02f7fa1307ea3307ea5317ea6317da8327daa337dab337cad347cae347bb0357bb2357bb3367ab5367ab73779b83779ba3878bc3978bd3977bf3a77c03a76c23b75c43c75c53c74c73d73c83e73ca3e72cc3f71cd4071cf4070d0416fd2426fd3436ed5446dd6456cd8456cd9466bdb476adc4869de4968df4a68e04c67e24d66e34e65e44f64e55064e75263e85362e95462ea5661eb5760ec5860ed5a5fee5b5eef5d5ef05f5ef1605df2625df2645cf3655cf4675cf4695cf56b5cf66c5cf66e5cf7705cf7725cf8745cf8765cf9785df9795df97b5dfa7d5efa7f5efa815ffb835ffb8560fb8761fc8961fc8a62fc8c63fc8e64fc9065fd9266fd9467fd9668fd9869fd9a6afd9b6bfe9d6cfe9f6dfea16efea36ffea571fea772fea973feaa74feac76feae77feb078feb27afeb47bfeb67cfeb77efeb97ffebb81febd82febf84fec185fec287fec488fec68afec88cfeca8dfecc8ffecd90fecf92fed194fed395fed597fed799fed89afdda9cfddc9efddea0fde0a1fde2a3fde3a5fde5a7fde7a9fde9aafdebacfcecaefceeb0fcf0b2fcf2b4fcf4b6fcf6b8fcf7b9fcf9bbfcfbbdfcfdbf"));
var inferno = ramp(colors_default("00000401000501010601010802010a02020c02020e03021004031204031405041706041907051b08051d09061f0a07220b07240c08260d08290e092b10092d110a30120a32140b34150b37160b39180c3c190c3e1b0c411c0c431e0c451f0c48210c4a230c4c240c4f260c51280b53290b552b0b572d0b592f0a5b310a5c320a5e340a5f3609613809623909633b09643d09653e0966400a67420a68440a68450a69470b6a490b6a4a0c6b4c0c6b4d0d6c4f0d6c510e6c520e6d540f6d550f6d57106e59106e5a116e5c126e5d126e5f136e61136e62146e64156e65156e67166e69166e6a176e6c186e6d186e6f196e71196e721a6e741a6e751b6e771c6d781c6d7a1d6d7c1d6d7d1e6d7f1e6c801f6c82206c84206b85216b87216b88226a8a226a8c23698d23698f24699025689225689326679526679727669827669a28659b29649d29649f2a63a02a63a22b62a32c61a52c60a62d60a82e5fa92e5eab2f5ead305dae305cb0315bb1325ab3325ab43359b63458b73557b93556ba3655bc3754bd3853bf3952c03a51c13a50c33b4fc43c4ec63d4dc73e4cc83f4bca404acb4149cc4248ce4347cf4446d04545d24644d34743d44842d54a41d74b3fd84c3ed94d3dda4e3cdb503bdd513ade5238df5337e05536e15635e25734e35933e45a31e55c30e65d2fe75e2ee8602de9612bea632aeb6429eb6628ec6726ed6925ee6a24ef6c23ef6e21f06f20f1711ff1731df2741cf3761bf37819f47918f57b17f57d15f67e14f68013f78212f78410f8850ff8870ef8890cf98b0bf98c0af98e09fa9008fa9207fa9407fb9606fb9706fb9906fb9b06fb9d07fc9f07fca108fca309fca50afca60cfca80dfcaa0ffcac11fcae12fcb014fcb216fcb418fbb61afbb81dfbba1ffbbc21fbbe23fac026fac228fac42afac62df9c72ff9c932f9cb35f8cd37f8cf3af7d13df7d340f6d543f6d746f5d949f5db4cf4dd4ff4df53f4e156f3e35af3e55df2e661f2e865f2ea69f1ec6df1ed71f1ef75f1f179f2f27df2f482f3f586f3f68af4f88ef5f992f6fa96f8fb9af9fc9dfafda1fcffa4"));
var plasma = ramp(colors_default("0d088710078813078916078a19068c1b068d1d068e20068f2206902406912605912805922a05932c05942e05952f059631059733059735049837049938049a3a049a3c049b3e049c3f049c41049d43039e44039e46039f48039f4903a04b03a14c02a14e02a25002a25102a35302a35502a45601a45801a45901a55b01a55c01a65e01a66001a66100a76300a76400a76600a76700a86900a86a00a86c00a86e00a86f00a87100a87201a87401a87501a87701a87801a87a02a87b02a87d03a87e03a88004a88104a78305a78405a78606a68707a68808a68a09a58b0aa58d0ba58e0ca48f0da4910ea3920fa39410a29511a19613a19814a099159f9a169f9c179e9d189d9e199da01a9ca11b9ba21d9aa31e9aa51f99a62098a72197a82296aa2395ab2494ac2694ad2793ae2892b02991b12a90b22b8fb32c8eb42e8db52f8cb6308bb7318ab83289ba3388bb3488bc3587bd3786be3885bf3984c03a83c13b82c23c81c33d80c43e7fc5407ec6417dc7427cc8437bc9447aca457acb4679cc4778cc4977cd4a76ce4b75cf4c74d04d73d14e72d24f71d35171d45270d5536fd5546ed6556dd7566cd8576bd9586ada5a6ada5b69db5c68dc5d67dd5e66de5f65de6164df6263e06363e16462e26561e26660e3685fe4695ee56a5de56b5de66c5ce76e5be76f5ae87059e97158e97257ea7457eb7556eb7655ec7754ed7953ed7a52ee7b51ef7c51ef7e50f07f4ff0804ef1814df1834cf2844bf3854bf3874af48849f48948f58b47f58c46f68d45f68f44f79044f79143f79342f89441f89540f9973ff9983ef99a3efa9b3dfa9c3cfa9e3bfb9f3afba139fba238fca338fca537fca636fca835fca934fdab33fdac33fdae32fdaf31fdb130fdb22ffdb42ffdb52efeb72dfeb82cfeba2cfebb2bfebd2afebe2afec029fdc229fdc328fdc527fdc627fdc827fdca26fdcb26fccd25fcce25fcd025fcd225fbd324fbd524fbd724fad824fada24f9dc24f9dd25f8df25f8e125f7e225f7e425f6e626f6e826f5e926f5eb27f4ed27f3ee27f3f027f2f227f1f426f1f525f0f724f0f921"));

// node_modules/@nivo/core/dist/nivo-core.es.js
var import_isFunction = __toESM(require_isFunction());
var import_without = __toESM(require_without());

// node_modules/d3-shape/src/constant.js
function constant_default2(x5) {
  return function constant2() {
    return x5;
  };
}

// node_modules/d3-shape/src/math.js
var cos = Math.cos;
var sin = Math.sin;
var sqrt2 = Math.sqrt;
var epsilon = 1e-12;
var pi = Math.PI;
var halfPi = pi / 2;
var tau = 2 * pi;

// node_modules/d3-path/src/path.js
var pi2 = Math.PI;
var tau2 = 2 * pi2;
var epsilon3 = 1e-6;
var tauEpsilon = tau2 - epsilon3;
function append(strings) {
  this._ += strings[0];
  for (let i5 = 1, n6 = strings.length; i5 < n6; ++i5) {
    this._ += arguments[i5] + strings[i5];
  }
}
function appendRound(digits) {
  let d3 = Math.floor(digits);
  if (!(d3 >= 0)) throw new Error(`invalid digits: ${digits}`);
  if (d3 > 15) return append;
  const k7 = 10 ** d3;
  return function(strings) {
    this._ += strings[0];
    for (let i5 = 1, n6 = strings.length; i5 < n6; ++i5) {
      this._ += Math.round(arguments[i5] * k7) / k7 + strings[i5];
    }
  };
}
var Path = class {
  constructor(digits) {
    this._x0 = this._y0 = // start of current subpath
    this._x1 = this._y1 = null;
    this._ = "";
    this._append = digits == null ? append : appendRound(digits);
  }
  moveTo(x5, y5) {
    this._append`M${this._x0 = this._x1 = +x5},${this._y0 = this._y1 = +y5}`;
  }
  closePath() {
    if (this._x1 !== null) {
      this._x1 = this._x0, this._y1 = this._y0;
      this._append`Z`;
    }
  }
  lineTo(x5, y5) {
    this._append`L${this._x1 = +x5},${this._y1 = +y5}`;
  }
  quadraticCurveTo(x1, y1, x5, y5) {
    this._append`Q${+x1},${+y1},${this._x1 = +x5},${this._y1 = +y5}`;
  }
  bezierCurveTo(x1, y1, x22, y22, x5, y5) {
    this._append`C${+x1},${+y1},${+x22},${+y22},${this._x1 = +x5},${this._y1 = +y5}`;
  }
  arcTo(x1, y1, x22, y22, r5) {
    x1 = +x1, y1 = +y1, x22 = +x22, y22 = +y22, r5 = +r5;
    if (r5 < 0) throw new Error(`negative radius: ${r5}`);
    let x0 = this._x1, y0 = this._y1, x21 = x22 - x1, y21 = y22 - y1, x01 = x0 - x1, y01 = y0 - y1, l01_2 = x01 * x01 + y01 * y01;
    if (this._x1 === null) {
      this._append`M${this._x1 = x1},${this._y1 = y1}`;
    } else if (!(l01_2 > epsilon3)) ;
    else if (!(Math.abs(y01 * x21 - y21 * x01) > epsilon3) || !r5) {
      this._append`L${this._x1 = x1},${this._y1 = y1}`;
    } else {
      let x20 = x22 - x0, y20 = y22 - y0, l21_2 = x21 * x21 + y21 * y21, l20_2 = x20 * x20 + y20 * y20, l21 = Math.sqrt(l21_2), l01 = Math.sqrt(l01_2), l3 = r5 * Math.tan((pi2 - Math.acos((l21_2 + l01_2 - l20_2) / (2 * l21 * l01))) / 2), t01 = l3 / l01, t21 = l3 / l21;
      if (Math.abs(t01 - 1) > epsilon3) {
        this._append`L${x1 + t01 * x01},${y1 + t01 * y01}`;
      }
      this._append`A${r5},${r5},0,0,${+(y01 * x20 > x01 * y20)},${this._x1 = x1 + t21 * x21},${this._y1 = y1 + t21 * y21}`;
    }
  }
  arc(x5, y5, r5, a0, a1, ccw) {
    x5 = +x5, y5 = +y5, r5 = +r5, ccw = !!ccw;
    if (r5 < 0) throw new Error(`negative radius: ${r5}`);
    let dx = r5 * Math.cos(a0), dy = r5 * Math.sin(a0), x0 = x5 + dx, y0 = y5 + dy, cw = 1 ^ ccw, da = ccw ? a0 - a1 : a1 - a0;
    if (this._x1 === null) {
      this._append`M${x0},${y0}`;
    } else if (Math.abs(this._x1 - x0) > epsilon3 || Math.abs(this._y1 - y0) > epsilon3) {
      this._append`L${x0},${y0}`;
    }
    if (!r5) return;
    if (da < 0) da = da % tau2 + tau2;
    if (da > tauEpsilon) {
      this._append`A${r5},${r5},0,1,${cw},${x5 - dx},${y5 - dy}A${r5},${r5},0,1,${cw},${this._x1 = x0},${this._y1 = y0}`;
    } else if (da > epsilon3) {
      this._append`A${r5},${r5},0,${+(da >= pi2)},${cw},${this._x1 = x5 + r5 * Math.cos(a1)},${this._y1 = y5 + r5 * Math.sin(a1)}`;
    }
  }
  rect(x5, y5, w6, h2) {
    this._append`M${this._x0 = this._x1 = +x5},${this._y0 = this._y1 = +y5}h${w6 = +w6}v${+h2}h${-w6}Z`;
  }
  toString() {
    return this._;
  }
};
function path() {
  return new Path();
}
path.prototype = Path.prototype;

// node_modules/d3-shape/src/array.js
var slice2 = Array.prototype.slice;
function array_default2(x5) {
  return typeof x5 === "object" && "length" in x5 ? x5 : Array.from(x5);
}

// node_modules/d3-shape/src/curve/linear.js
function Linear(context) {
  this._context = context;
}
Linear.prototype = {
  areaStart: function() {
    this._line = 0;
  },
  areaEnd: function() {
    this._line = NaN;
  },
  lineStart: function() {
    this._point = 0;
  },
  lineEnd: function() {
    if (this._line || this._line !== 0 && this._point === 1) this._context.closePath();
    this._line = 1 - this._line;
  },
  point: function(x5, y5) {
    x5 = +x5, y5 = +y5;
    switch (this._point) {
      case 0:
        this._point = 1;
        this._line ? this._context.lineTo(x5, y5) : this._context.moveTo(x5, y5);
        break;
      case 1:
        this._point = 2;
      // falls through
      default:
        this._context.lineTo(x5, y5);
        break;
    }
  }
};
function linear_default(context) {
  return new Linear(context);
}

// node_modules/d3-shape/src/curve/radial.js
var curveRadialLinear = curveRadial(linear_default);
function Radial(curve) {
  this._curve = curve;
}
Radial.prototype = {
  areaStart: function() {
    this._curve.areaStart();
  },
  areaEnd: function() {
    this._curve.areaEnd();
  },
  lineStart: function() {
    this._curve.lineStart();
  },
  lineEnd: function() {
    this._curve.lineEnd();
  },
  point: function(a5, r5) {
    this._curve.point(r5 * Math.sin(a5), r5 * -Math.cos(a5));
  }
};
function curveRadial(curve) {
  function radial2(context) {
    return new Radial(curve(context));
  }
  radial2._curve = curve;
  return radial2;
}

// node_modules/d3-shape/src/symbol/asterisk.js
var sqrt3 = sqrt2(3);

// node_modules/d3-shape/src/symbol/diamond.js
var tan30 = sqrt2(1 / 3);
var tan30_2 = tan30 * 2;

// node_modules/d3-shape/src/symbol/star.js
var kr = sin(pi / 10) / sin(7 * pi / 10);
var kx = sin(tau / 10) * kr;
var ky = -cos(tau / 10) * kr;

// node_modules/d3-shape/src/symbol/triangle.js
var sqrt32 = sqrt2(3);

// node_modules/d3-shape/src/symbol/triangle2.js
var sqrt33 = sqrt2(3);

// node_modules/d3-shape/src/symbol/wye.js
var s = sqrt2(3) / 2;
var k2 = 1 / sqrt2(12);
var a = (k2 / 2 + 1) * 3;

// node_modules/d3-shape/src/noop.js
function noop_default() {
}

// node_modules/d3-shape/src/curve/basis.js
function point2(that, x5, y5) {
  that._context.bezierCurveTo(
    (2 * that._x0 + that._x1) / 3,
    (2 * that._y0 + that._y1) / 3,
    (that._x0 + 2 * that._x1) / 3,
    (that._y0 + 2 * that._y1) / 3,
    (that._x0 + 4 * that._x1 + x5) / 6,
    (that._y0 + 4 * that._y1 + y5) / 6
  );
}
function Basis(context) {
  this._context = context;
}
Basis.prototype = {
  areaStart: function() {
    this._line = 0;
  },
  areaEnd: function() {
    this._line = NaN;
  },
  lineStart: function() {
    this._x0 = this._x1 = this._y0 = this._y1 = NaN;
    this._point = 0;
  },
  lineEnd: function() {
    switch (this._point) {
      case 3:
        point2(this, this._x1, this._y1);
      // falls through
      case 2:
        this._context.lineTo(this._x1, this._y1);
        break;
    }
    if (this._line || this._line !== 0 && this._point === 1) this._context.closePath();
    this._line = 1 - this._line;
  },
  point: function(x5, y5) {
    x5 = +x5, y5 = +y5;
    switch (this._point) {
      case 0:
        this._point = 1;
        this._line ? this._context.lineTo(x5, y5) : this._context.moveTo(x5, y5);
        break;
      case 1:
        this._point = 2;
        break;
      case 2:
        this._point = 3;
        this._context.lineTo((5 * this._x0 + this._x1) / 6, (5 * this._y0 + this._y1) / 6);
      // falls through
      default:
        point2(this, x5, y5);
        break;
    }
    this._x0 = this._x1, this._x1 = x5;
    this._y0 = this._y1, this._y1 = y5;
  }
};
function basis_default2(context) {
  return new Basis(context);
}

// node_modules/d3-shape/src/curve/basisClosed.js
function BasisClosed(context) {
  this._context = context;
}
BasisClosed.prototype = {
  areaStart: noop_default,
  areaEnd: noop_default,
  lineStart: function() {
    this._x0 = this._x1 = this._x2 = this._x3 = this._x4 = this._y0 = this._y1 = this._y2 = this._y3 = this._y4 = NaN;
    this._point = 0;
  },
  lineEnd: function() {
    switch (this._point) {
      case 1: {
        this._context.moveTo(this._x2, this._y2);
        this._context.closePath();
        break;
      }
      case 2: {
        this._context.moveTo((this._x2 + 2 * this._x3) / 3, (this._y2 + 2 * this._y3) / 3);
        this._context.lineTo((this._x3 + 2 * this._x2) / 3, (this._y3 + 2 * this._y2) / 3);
        this._context.closePath();
        break;
      }
      case 3: {
        this.point(this._x2, this._y2);
        this.point(this._x3, this._y3);
        this.point(this._x4, this._y4);
        break;
      }
    }
  },
  point: function(x5, y5) {
    x5 = +x5, y5 = +y5;
    switch (this._point) {
      case 0:
        this._point = 1;
        this._x2 = x5, this._y2 = y5;
        break;
      case 1:
        this._point = 2;
        this._x3 = x5, this._y3 = y5;
        break;
      case 2:
        this._point = 3;
        this._x4 = x5, this._y4 = y5;
        this._context.moveTo((this._x0 + 4 * this._x1 + x5) / 6, (this._y0 + 4 * this._y1 + y5) / 6);
        break;
      default:
        point2(this, x5, y5);
        break;
    }
    this._x0 = this._x1, this._x1 = x5;
    this._y0 = this._y1, this._y1 = y5;
  }
};
function basisClosed_default2(context) {
  return new BasisClosed(context);
}

// node_modules/d3-shape/src/curve/basisOpen.js
function BasisOpen(context) {
  this._context = context;
}
BasisOpen.prototype = {
  areaStart: function() {
    this._line = 0;
  },
  areaEnd: function() {
    this._line = NaN;
  },
  lineStart: function() {
    this._x0 = this._x1 = this._y0 = this._y1 = NaN;
    this._point = 0;
  },
  lineEnd: function() {
    if (this._line || this._line !== 0 && this._point === 3) this._context.closePath();
    this._line = 1 - this._line;
  },
  point: function(x5, y5) {
    x5 = +x5, y5 = +y5;
    switch (this._point) {
      case 0:
        this._point = 1;
        break;
      case 1:
        this._point = 2;
        break;
      case 2:
        this._point = 3;
        var x0 = (this._x0 + 4 * this._x1 + x5) / 6, y0 = (this._y0 + 4 * this._y1 + y5) / 6;
        this._line ? this._context.lineTo(x0, y0) : this._context.moveTo(x0, y0);
        break;
      case 3:
        this._point = 4;
      // falls through
      default:
        point2(this, x5, y5);
        break;
    }
    this._x0 = this._x1, this._x1 = x5;
    this._y0 = this._y1, this._y1 = y5;
  }
};
function basisOpen_default(context) {
  return new BasisOpen(context);
}

// node_modules/d3-shape/src/curve/bundle.js
function Bundle(context, beta) {
  this._basis = new Basis(context);
  this._beta = beta;
}
Bundle.prototype = {
  lineStart: function() {
    this._x = [];
    this._y = [];
    this._basis.lineStart();
  },
  lineEnd: function() {
    var x5 = this._x, y5 = this._y, j5 = x5.length - 1;
    if (j5 > 0) {
      var x0 = x5[0], y0 = y5[0], dx = x5[j5] - x0, dy = y5[j5] - y0, i5 = -1, t9;
      while (++i5 <= j5) {
        t9 = i5 / j5;
        this._basis.point(
          this._beta * x5[i5] + (1 - this._beta) * (x0 + t9 * dx),
          this._beta * y5[i5] + (1 - this._beta) * (y0 + t9 * dy)
        );
      }
    }
    this._x = this._y = null;
    this._basis.lineEnd();
  },
  point: function(x5, y5) {
    this._x.push(+x5);
    this._y.push(+y5);
  }
};
var bundle_default = function custom(beta) {
  function bundle(context) {
    return beta === 1 ? new Basis(context) : new Bundle(context, beta);
  }
  bundle.beta = function(beta2) {
    return custom(+beta2);
  };
  return bundle;
}(0.85);

// node_modules/d3-shape/src/curve/cardinal.js
function point3(that, x5, y5) {
  that._context.bezierCurveTo(
    that._x1 + that._k * (that._x2 - that._x0),
    that._y1 + that._k * (that._y2 - that._y0),
    that._x2 + that._k * (that._x1 - x5),
    that._y2 + that._k * (that._y1 - y5),
    that._x2,
    that._y2
  );
}
function Cardinal(context, tension) {
  this._context = context;
  this._k = (1 - tension) / 6;
}
Cardinal.prototype = {
  areaStart: function() {
    this._line = 0;
  },
  areaEnd: function() {
    this._line = NaN;
  },
  lineStart: function() {
    this._x0 = this._x1 = this._x2 = this._y0 = this._y1 = this._y2 = NaN;
    this._point = 0;
  },
  lineEnd: function() {
    switch (this._point) {
      case 2:
        this._context.lineTo(this._x2, this._y2);
        break;
      case 3:
        point3(this, this._x1, this._y1);
        break;
    }
    if (this._line || this._line !== 0 && this._point === 1) this._context.closePath();
    this._line = 1 - this._line;
  },
  point: function(x5, y5) {
    x5 = +x5, y5 = +y5;
    switch (this._point) {
      case 0:
        this._point = 1;
        this._line ? this._context.lineTo(x5, y5) : this._context.moveTo(x5, y5);
        break;
      case 1:
        this._point = 2;
        this._x1 = x5, this._y1 = y5;
        break;
      case 2:
        this._point = 3;
      // falls through
      default:
        point3(this, x5, y5);
        break;
    }
    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x5;
    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y5;
  }
};
var cardinal_default = function custom2(tension) {
  function cardinal(context) {
    return new Cardinal(context, tension);
  }
  cardinal.tension = function(tension2) {
    return custom2(+tension2);
  };
  return cardinal;
}(0);

// node_modules/d3-shape/src/curve/cardinalClosed.js
function CardinalClosed(context, tension) {
  this._context = context;
  this._k = (1 - tension) / 6;
}
CardinalClosed.prototype = {
  areaStart: noop_default,
  areaEnd: noop_default,
  lineStart: function() {
    this._x0 = this._x1 = this._x2 = this._x3 = this._x4 = this._x5 = this._y0 = this._y1 = this._y2 = this._y3 = this._y4 = this._y5 = NaN;
    this._point = 0;
  },
  lineEnd: function() {
    switch (this._point) {
      case 1: {
        this._context.moveTo(this._x3, this._y3);
        this._context.closePath();
        break;
      }
      case 2: {
        this._context.lineTo(this._x3, this._y3);
        this._context.closePath();
        break;
      }
      case 3: {
        this.point(this._x3, this._y3);
        this.point(this._x4, this._y4);
        this.point(this._x5, this._y5);
        break;
      }
    }
  },
  point: function(x5, y5) {
    x5 = +x5, y5 = +y5;
    switch (this._point) {
      case 0:
        this._point = 1;
        this._x3 = x5, this._y3 = y5;
        break;
      case 1:
        this._point = 2;
        this._context.moveTo(this._x4 = x5, this._y4 = y5);
        break;
      case 2:
        this._point = 3;
        this._x5 = x5, this._y5 = y5;
        break;
      default:
        point3(this, x5, y5);
        break;
    }
    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x5;
    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y5;
  }
};
var cardinalClosed_default = function custom3(tension) {
  function cardinal(context) {
    return new CardinalClosed(context, tension);
  }
  cardinal.tension = function(tension2) {
    return custom3(+tension2);
  };
  return cardinal;
}(0);

// node_modules/d3-shape/src/curve/cardinalOpen.js
function CardinalOpen(context, tension) {
  this._context = context;
  this._k = (1 - tension) / 6;
}
CardinalOpen.prototype = {
  areaStart: function() {
    this._line = 0;
  },
  areaEnd: function() {
    this._line = NaN;
  },
  lineStart: function() {
    this._x0 = this._x1 = this._x2 = this._y0 = this._y1 = this._y2 = NaN;
    this._point = 0;
  },
  lineEnd: function() {
    if (this._line || this._line !== 0 && this._point === 3) this._context.closePath();
    this._line = 1 - this._line;
  },
  point: function(x5, y5) {
    x5 = +x5, y5 = +y5;
    switch (this._point) {
      case 0:
        this._point = 1;
        break;
      case 1:
        this._point = 2;
        break;
      case 2:
        this._point = 3;
        this._line ? this._context.lineTo(this._x2, this._y2) : this._context.moveTo(this._x2, this._y2);
        break;
      case 3:
        this._point = 4;
      // falls through
      default:
        point3(this, x5, y5);
        break;
    }
    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x5;
    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y5;
  }
};
var cardinalOpen_default = function custom4(tension) {
  function cardinal(context) {
    return new CardinalOpen(context, tension);
  }
  cardinal.tension = function(tension2) {
    return custom4(+tension2);
  };
  return cardinal;
}(0);

// node_modules/d3-shape/src/curve/catmullRom.js
function point4(that, x5, y5) {
  var x1 = that._x1, y1 = that._y1, x22 = that._x2, y22 = that._y2;
  if (that._l01_a > epsilon) {
    var a5 = 2 * that._l01_2a + 3 * that._l01_a * that._l12_a + that._l12_2a, n6 = 3 * that._l01_a * (that._l01_a + that._l12_a);
    x1 = (x1 * a5 - that._x0 * that._l12_2a + that._x2 * that._l01_2a) / n6;
    y1 = (y1 * a5 - that._y0 * that._l12_2a + that._y2 * that._l01_2a) / n6;
  }
  if (that._l23_a > epsilon) {
    var b5 = 2 * that._l23_2a + 3 * that._l23_a * that._l12_a + that._l12_2a, m5 = 3 * that._l23_a * (that._l23_a + that._l12_a);
    x22 = (x22 * b5 + that._x1 * that._l23_2a - x5 * that._l12_2a) / m5;
    y22 = (y22 * b5 + that._y1 * that._l23_2a - y5 * that._l12_2a) / m5;
  }
  that._context.bezierCurveTo(x1, y1, x22, y22, that._x2, that._y2);
}
function CatmullRom(context, alpha) {
  this._context = context;
  this._alpha = alpha;
}
CatmullRom.prototype = {
  areaStart: function() {
    this._line = 0;
  },
  areaEnd: function() {
    this._line = NaN;
  },
  lineStart: function() {
    this._x0 = this._x1 = this._x2 = this._y0 = this._y1 = this._y2 = NaN;
    this._l01_a = this._l12_a = this._l23_a = this._l01_2a = this._l12_2a = this._l23_2a = this._point = 0;
  },
  lineEnd: function() {
    switch (this._point) {
      case 2:
        this._context.lineTo(this._x2, this._y2);
        break;
      case 3:
        this.point(this._x2, this._y2);
        break;
    }
    if (this._line || this._line !== 0 && this._point === 1) this._context.closePath();
    this._line = 1 - this._line;
  },
  point: function(x5, y5) {
    x5 = +x5, y5 = +y5;
    if (this._point) {
      var x23 = this._x2 - x5, y23 = this._y2 - y5;
      this._l23_a = Math.sqrt(this._l23_2a = Math.pow(x23 * x23 + y23 * y23, this._alpha));
    }
    switch (this._point) {
      case 0:
        this._point = 1;
        this._line ? this._context.lineTo(x5, y5) : this._context.moveTo(x5, y5);
        break;
      case 1:
        this._point = 2;
        break;
      case 2:
        this._point = 3;
      // falls through
      default:
        point4(this, x5, y5);
        break;
    }
    this._l01_a = this._l12_a, this._l12_a = this._l23_a;
    this._l01_2a = this._l12_2a, this._l12_2a = this._l23_2a;
    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x5;
    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y5;
  }
};
var catmullRom_default = function custom5(alpha) {
  function catmullRom(context) {
    return alpha ? new CatmullRom(context, alpha) : new Cardinal(context, 0);
  }
  catmullRom.alpha = function(alpha2) {
    return custom5(+alpha2);
  };
  return catmullRom;
}(0.5);

// node_modules/d3-shape/src/curve/catmullRomClosed.js
function CatmullRomClosed(context, alpha) {
  this._context = context;
  this._alpha = alpha;
}
CatmullRomClosed.prototype = {
  areaStart: noop_default,
  areaEnd: noop_default,
  lineStart: function() {
    this._x0 = this._x1 = this._x2 = this._x3 = this._x4 = this._x5 = this._y0 = this._y1 = this._y2 = this._y3 = this._y4 = this._y5 = NaN;
    this._l01_a = this._l12_a = this._l23_a = this._l01_2a = this._l12_2a = this._l23_2a = this._point = 0;
  },
  lineEnd: function() {
    switch (this._point) {
      case 1: {
        this._context.moveTo(this._x3, this._y3);
        this._context.closePath();
        break;
      }
      case 2: {
        this._context.lineTo(this._x3, this._y3);
        this._context.closePath();
        break;
      }
      case 3: {
        this.point(this._x3, this._y3);
        this.point(this._x4, this._y4);
        this.point(this._x5, this._y5);
        break;
      }
    }
  },
  point: function(x5, y5) {
    x5 = +x5, y5 = +y5;
    if (this._point) {
      var x23 = this._x2 - x5, y23 = this._y2 - y5;
      this._l23_a = Math.sqrt(this._l23_2a = Math.pow(x23 * x23 + y23 * y23, this._alpha));
    }
    switch (this._point) {
      case 0:
        this._point = 1;
        this._x3 = x5, this._y3 = y5;
        break;
      case 1:
        this._point = 2;
        this._context.moveTo(this._x4 = x5, this._y4 = y5);
        break;
      case 2:
        this._point = 3;
        this._x5 = x5, this._y5 = y5;
        break;
      default:
        point4(this, x5, y5);
        break;
    }
    this._l01_a = this._l12_a, this._l12_a = this._l23_a;
    this._l01_2a = this._l12_2a, this._l12_2a = this._l23_2a;
    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x5;
    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y5;
  }
};
var catmullRomClosed_default = function custom6(alpha) {
  function catmullRom(context) {
    return alpha ? new CatmullRomClosed(context, alpha) : new CardinalClosed(context, 0);
  }
  catmullRom.alpha = function(alpha2) {
    return custom6(+alpha2);
  };
  return catmullRom;
}(0.5);

// node_modules/d3-shape/src/curve/catmullRomOpen.js
function CatmullRomOpen(context, alpha) {
  this._context = context;
  this._alpha = alpha;
}
CatmullRomOpen.prototype = {
  areaStart: function() {
    this._line = 0;
  },
  areaEnd: function() {
    this._line = NaN;
  },
  lineStart: function() {
    this._x0 = this._x1 = this._x2 = this._y0 = this._y1 = this._y2 = NaN;
    this._l01_a = this._l12_a = this._l23_a = this._l01_2a = this._l12_2a = this._l23_2a = this._point = 0;
  },
  lineEnd: function() {
    if (this._line || this._line !== 0 && this._point === 3) this._context.closePath();
    this._line = 1 - this._line;
  },
  point: function(x5, y5) {
    x5 = +x5, y5 = +y5;
    if (this._point) {
      var x23 = this._x2 - x5, y23 = this._y2 - y5;
      this._l23_a = Math.sqrt(this._l23_2a = Math.pow(x23 * x23 + y23 * y23, this._alpha));
    }
    switch (this._point) {
      case 0:
        this._point = 1;
        break;
      case 1:
        this._point = 2;
        break;
      case 2:
        this._point = 3;
        this._line ? this._context.lineTo(this._x2, this._y2) : this._context.moveTo(this._x2, this._y2);
        break;
      case 3:
        this._point = 4;
      // falls through
      default:
        point4(this, x5, y5);
        break;
    }
    this._l01_a = this._l12_a, this._l12_a = this._l23_a;
    this._l01_2a = this._l12_2a, this._l12_2a = this._l23_2a;
    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x5;
    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y5;
  }
};
var catmullRomOpen_default = function custom7(alpha) {
  function catmullRom(context) {
    return alpha ? new CatmullRomOpen(context, alpha) : new CardinalOpen(context, 0);
  }
  catmullRom.alpha = function(alpha2) {
    return custom7(+alpha2);
  };
  return catmullRom;
}(0.5);

// node_modules/d3-shape/src/curve/linearClosed.js
function LinearClosed(context) {
  this._context = context;
}
LinearClosed.prototype = {
  areaStart: noop_default,
  areaEnd: noop_default,
  lineStart: function() {
    this._point = 0;
  },
  lineEnd: function() {
    if (this._point) this._context.closePath();
  },
  point: function(x5, y5) {
    x5 = +x5, y5 = +y5;
    if (this._point) this._context.lineTo(x5, y5);
    else this._point = 1, this._context.moveTo(x5, y5);
  }
};
function linearClosed_default(context) {
  return new LinearClosed(context);
}

// node_modules/d3-shape/src/curve/monotone.js
function sign(x5) {
  return x5 < 0 ? -1 : 1;
}
function slope3(that, x22, y22) {
  var h0 = that._x1 - that._x0, h1 = x22 - that._x1, s0 = (that._y1 - that._y0) / (h0 || h1 < 0 && -0), s1 = (y22 - that._y1) / (h1 || h0 < 0 && -0), p5 = (s0 * h1 + s1 * h0) / (h0 + h1);
  return (sign(s0) + sign(s1)) * Math.min(Math.abs(s0), Math.abs(s1), 0.5 * Math.abs(p5)) || 0;
}
function slope2(that, t9) {
  var h2 = that._x1 - that._x0;
  return h2 ? (3 * (that._y1 - that._y0) / h2 - t9) / 2 : t9;
}
function point5(that, t04, t14) {
  var x0 = that._x0, y0 = that._y0, x1 = that._x1, y1 = that._y1, dx = (x1 - x0) / 3;
  that._context.bezierCurveTo(x0 + dx, y0 + dx * t04, x1 - dx, y1 - dx * t14, x1, y1);
}
function MonotoneX(context) {
  this._context = context;
}
MonotoneX.prototype = {
  areaStart: function() {
    this._line = 0;
  },
  areaEnd: function() {
    this._line = NaN;
  },
  lineStart: function() {
    this._x0 = this._x1 = this._y0 = this._y1 = this._t0 = NaN;
    this._point = 0;
  },
  lineEnd: function() {
    switch (this._point) {
      case 2:
        this._context.lineTo(this._x1, this._y1);
        break;
      case 3:
        point5(this, this._t0, slope2(this, this._t0));
        break;
    }
    if (this._line || this._line !== 0 && this._point === 1) this._context.closePath();
    this._line = 1 - this._line;
  },
  point: function(x5, y5) {
    var t14 = NaN;
    x5 = +x5, y5 = +y5;
    if (x5 === this._x1 && y5 === this._y1) return;
    switch (this._point) {
      case 0:
        this._point = 1;
        this._line ? this._context.lineTo(x5, y5) : this._context.moveTo(x5, y5);
        break;
      case 1:
        this._point = 2;
        break;
      case 2:
        this._point = 3;
        point5(this, slope2(this, t14 = slope3(this, x5, y5)), t14);
        break;
      default:
        point5(this, this._t0, t14 = slope3(this, x5, y5));
        break;
    }
    this._x0 = this._x1, this._x1 = x5;
    this._y0 = this._y1, this._y1 = y5;
    this._t0 = t14;
  }
};
function MonotoneY(context) {
  this._context = new ReflectContext(context);
}
(MonotoneY.prototype = Object.create(MonotoneX.prototype)).point = function(x5, y5) {
  MonotoneX.prototype.point.call(this, y5, x5);
};
function ReflectContext(context) {
  this._context = context;
}
ReflectContext.prototype = {
  moveTo: function(x5, y5) {
    this._context.moveTo(y5, x5);
  },
  closePath: function() {
    this._context.closePath();
  },
  lineTo: function(x5, y5) {
    this._context.lineTo(y5, x5);
  },
  bezierCurveTo: function(x1, y1, x22, y22, x5, y5) {
    this._context.bezierCurveTo(y1, x1, y22, x22, y5, x5);
  }
};
function monotoneX(context) {
  return new MonotoneX(context);
}
function monotoneY(context) {
  return new MonotoneY(context);
}

// node_modules/d3-shape/src/curve/natural.js
function Natural(context) {
  this._context = context;
}
Natural.prototype = {
  areaStart: function() {
    this._line = 0;
  },
  areaEnd: function() {
    this._line = NaN;
  },
  lineStart: function() {
    this._x = [];
    this._y = [];
  },
  lineEnd: function() {
    var x5 = this._x, y5 = this._y, n6 = x5.length;
    if (n6) {
      this._line ? this._context.lineTo(x5[0], y5[0]) : this._context.moveTo(x5[0], y5[0]);
      if (n6 === 2) {
        this._context.lineTo(x5[1], y5[1]);
      } else {
        var px = controlPoints(x5), py = controlPoints(y5);
        for (var i0 = 0, i1 = 1; i1 < n6; ++i0, ++i1) {
          this._context.bezierCurveTo(px[0][i0], py[0][i0], px[1][i0], py[1][i0], x5[i1], y5[i1]);
        }
      }
    }
    if (this._line || this._line !== 0 && n6 === 1) this._context.closePath();
    this._line = 1 - this._line;
    this._x = this._y = null;
  },
  point: function(x5, y5) {
    this._x.push(+x5);
    this._y.push(+y5);
  }
};
function controlPoints(x5) {
  var i5, n6 = x5.length - 1, m5, a5 = new Array(n6), b5 = new Array(n6), r5 = new Array(n6);
  a5[0] = 0, b5[0] = 2, r5[0] = x5[0] + 2 * x5[1];
  for (i5 = 1; i5 < n6 - 1; ++i5) a5[i5] = 1, b5[i5] = 4, r5[i5] = 4 * x5[i5] + 2 * x5[i5 + 1];
  a5[n6 - 1] = 2, b5[n6 - 1] = 7, r5[n6 - 1] = 8 * x5[n6 - 1] + x5[n6];
  for (i5 = 1; i5 < n6; ++i5) m5 = a5[i5] / b5[i5 - 1], b5[i5] -= m5, r5[i5] -= m5 * r5[i5 - 1];
  a5[n6 - 1] = r5[n6 - 1] / b5[n6 - 1];
  for (i5 = n6 - 2; i5 >= 0; --i5) a5[i5] = (r5[i5] - a5[i5 + 1]) / b5[i5];
  b5[n6 - 1] = (x5[n6] + a5[n6 - 1]) / 2;
  for (i5 = 0; i5 < n6 - 1; ++i5) b5[i5] = 2 * x5[i5 + 1] - a5[i5 + 1];
  return [a5, b5];
}
function natural_default(context) {
  return new Natural(context);
}

// node_modules/d3-shape/src/curve/step.js
function Step(context, t9) {
  this._context = context;
  this._t = t9;
}
Step.prototype = {
  areaStart: function() {
    this._line = 0;
  },
  areaEnd: function() {
    this._line = NaN;
  },
  lineStart: function() {
    this._x = this._y = NaN;
    this._point = 0;
  },
  lineEnd: function() {
    if (0 < this._t && this._t < 1 && this._point === 2) this._context.lineTo(this._x, this._y);
    if (this._line || this._line !== 0 && this._point === 1) this._context.closePath();
    if (this._line >= 0) this._t = 1 - this._t, this._line = 1 - this._line;
  },
  point: function(x5, y5) {
    x5 = +x5, y5 = +y5;
    switch (this._point) {
      case 0:
        this._point = 1;
        this._line ? this._context.lineTo(x5, y5) : this._context.moveTo(x5, y5);
        break;
      case 1:
        this._point = 2;
      // falls through
      default: {
        if (this._t <= 0) {
          this._context.lineTo(this._x, y5);
          this._context.lineTo(x5, y5);
        } else {
          var x1 = this._x * (1 - this._t) + x5 * this._t;
          this._context.lineTo(x1, this._y);
          this._context.lineTo(x1, y5);
        }
        break;
      }
    }
    this._x = x5, this._y = y5;
  }
};
function step_default(context) {
  return new Step(context, 0.5);
}
function stepBefore(context) {
  return new Step(context, 0);
}
function stepAfter(context) {
  return new Step(context, 1);
}

// node_modules/d3-shape/src/offset/none.js
function none_default(series, order) {
  if (!((n6 = series.length) > 1)) return;
  for (var i5 = 1, j5, s0, s1 = series[order[0]], n6, m5 = s1.length; i5 < n6; ++i5) {
    s0 = s1, s1 = series[order[i5]];
    for (j5 = 0; j5 < m5; ++j5) {
      s1[j5][1] += s1[j5][0] = isNaN(s0[j5][1]) ? s0[j5][0] : s0[j5][1];
    }
  }
}

// node_modules/d3-shape/src/order/none.js
function none_default2(series) {
  var n6 = series.length, o4 = new Array(n6);
  while (--n6 >= 0) o4[n6] = n6;
  return o4;
}

// node_modules/d3-shape/src/stack.js
function stackValue(d3, key) {
  return d3[key];
}
function stackSeries(key) {
  const series = [];
  series.key = key;
  return series;
}
function stack_default() {
  var keys = constant_default2([]), order = none_default2, offset = none_default, value = stackValue;
  function stack(data) {
    var sz = Array.from(keys.apply(this, arguments), stackSeries), i5, n6 = sz.length, j5 = -1, oz;
    for (const d3 of data) {
      for (i5 = 0, ++j5; i5 < n6; ++i5) {
        (sz[i5][j5] = [0, +value(d3, sz[i5].key, j5, data)]).data = d3;
      }
    }
    for (i5 = 0, oz = array_default2(order(sz)); i5 < n6; ++i5) {
      sz[oz[i5]].index = i5;
    }
    offset(sz, oz);
    return sz;
  }
  stack.keys = function(_3) {
    return arguments.length ? (keys = typeof _3 === "function" ? _3 : constant_default2(Array.from(_3)), stack) : keys;
  };
  stack.value = function(_3) {
    return arguments.length ? (value = typeof _3 === "function" ? _3 : constant_default2(+_3), stack) : value;
  };
  stack.order = function(_3) {
    return arguments.length ? (order = _3 == null ? none_default2 : typeof _3 === "function" ? _3 : constant_default2(Array.from(_3)), stack) : order;
  };
  stack.offset = function(_3) {
    return arguments.length ? (offset = _3 == null ? none_default : _3, stack) : offset;
  };
  return stack;
}

// node_modules/d3-shape/src/offset/expand.js
function expand_default(series, order) {
  if (!((n6 = series.length) > 0)) return;
  for (var i5, n6, j5 = 0, m5 = series[0].length, y5; j5 < m5; ++j5) {
    for (y5 = i5 = 0; i5 < n6; ++i5) y5 += series[i5][j5][1] || 0;
    if (y5) for (i5 = 0; i5 < n6; ++i5) series[i5][j5][1] /= y5;
  }
  none_default(series, order);
}

// node_modules/d3-shape/src/offset/diverging.js
function diverging_default(series, order) {
  if (!((n6 = series.length) > 0)) return;
  for (var i5, j5 = 0, d3, dy, yp, yn2, n6, m5 = series[order[0]].length; j5 < m5; ++j5) {
    for (yp = yn2 = 0, i5 = 0; i5 < n6; ++i5) {
      if ((dy = (d3 = series[order[i5]][j5])[1] - d3[0]) > 0) {
        d3[0] = yp, d3[1] = yp += dy;
      } else if (dy < 0) {
        d3[1] = yn2, d3[0] = yn2 += dy;
      } else {
        d3[0] = 0, d3[1] = dy;
      }
    }
  }
}

// node_modules/d3-shape/src/offset/silhouette.js
function silhouette_default(series, order) {
  if (!((n6 = series.length) > 0)) return;
  for (var j5 = 0, s0 = series[order[0]], n6, m5 = s0.length; j5 < m5; ++j5) {
    for (var i5 = 0, y5 = 0; i5 < n6; ++i5) y5 += series[i5][j5][1] || 0;
    s0[j5][1] += s0[j5][0] = -y5 / 2;
  }
  none_default(series, order);
}

// node_modules/d3-shape/src/offset/wiggle.js
function wiggle_default(series, order) {
  if (!((n6 = series.length) > 0) || !((m5 = (s0 = series[order[0]]).length) > 0)) return;
  for (var y5 = 0, j5 = 1, s0, m5, n6; j5 < m5; ++j5) {
    for (var i5 = 0, s1 = 0, s22 = 0; i5 < n6; ++i5) {
      var si = series[order[i5]], sij0 = si[j5][1] || 0, sij1 = si[j5 - 1][1] || 0, s32 = (sij0 - sij1) / 2;
      for (var k7 = 0; k7 < i5; ++k7) {
        var sk = series[order[k7]], skj0 = sk[j5][1] || 0, skj1 = sk[j5 - 1][1] || 0;
        s32 += skj0 - skj1;
      }
      s1 += sij0, s22 += s32 * sij0;
    }
    s0[j5 - 1][1] += s0[j5 - 1][0] = y5;
    if (s1) y5 -= s22 / s1;
  }
  s0[j5 - 1][1] += s0[j5 - 1][0] = y5;
  none_default(series, order);
}

// node_modules/d3-shape/src/order/appearance.js
function appearance_default(series) {
  var peaks = series.map(peak);
  return none_default2(series).sort(function(a5, b5) {
    return peaks[a5] - peaks[b5];
  });
}
function peak(series) {
  var i5 = -1, j5 = 0, n6 = series.length, vi, vj = -Infinity;
  while (++i5 < n6) if ((vi = +series[i5][1]) > vj) vj = vi, j5 = i5;
  return j5;
}

// node_modules/d3-shape/src/order/ascending.js
function ascending_default(series) {
  var sums = series.map(sum2);
  return none_default2(series).sort(function(a5, b5) {
    return sums[a5] - sums[b5];
  });
}
function sum2(series) {
  var s4 = 0, i5 = -1, n6 = series.length, v6;
  while (++i5 < n6) if (v6 = +series[i5][1]) s4 += v6;
  return s4;
}

// node_modules/d3-shape/src/order/descending.js
function descending_default2(series) {
  return ascending_default(series).reverse();
}

// node_modules/d3-shape/src/order/insideOut.js
function insideOut_default(series) {
  var n6 = series.length, i5, j5, sums = series.map(sum2), order = appearance_default(series), top = 0, bottom = 0, tops = [], bottoms = [];
  for (i5 = 0; i5 < n6; ++i5) {
    j5 = order[i5];
    if (top < bottom) {
      top += sums[j5];
      tops.push(j5);
    } else {
      bottom += sums[j5];
      bottoms.push(j5);
    }
  }
  return bottoms.reverse().concat(tops);
}

// node_modules/d3-shape/src/order/reverse.js
function reverse_default(series) {
  return none_default2(series).reverse();
}

// node_modules/@nivo/core/dist/nivo-core.es.js
var import_isPlainObject = __toESM(require_isPlainObject());
var import_pick = __toESM(require_pick());
var import_isEqual = __toESM(require_isEqual());
var Pr = { background: "transparent", text: { fontFamily: "sans-serif", fontSize: 11, fill: "#333333", outlineWidth: 0, outlineColor: "transparent", outlineOpacity: 1 }, axis: { domain: { line: { stroke: "transparent", strokeWidth: 1 } }, ticks: { line: { stroke: "#777777", strokeWidth: 1 }, text: {} }, legend: { text: { fontSize: 12 } } }, grid: { line: { stroke: "#dddddd", strokeWidth: 1 } }, legends: { hidden: { symbol: { fill: "#333333", opacity: 0.6 }, text: { fill: "#333333", opacity: 0.6 } }, text: {}, ticks: { line: { stroke: "#777777", strokeWidth: 1 }, text: { fontSize: 10 } }, title: { text: {} } }, labels: { text: {} }, markers: { lineColor: "#000000", lineStrokeWidth: 1, text: {} }, dots: { text: {} }, tooltip: { container: { background: "white", color: "inherit", fontSize: "inherit", borderRadius: "2px", boxShadow: "0 1px 2px rgba(0, 0, 0, 0.25)", padding: "5px 9px" }, basic: { whiteSpace: "pre", display: "flex", alignItems: "center" }, chip: { marginRight: 7 }, table: {}, tableCell: { padding: "3px 5px" }, tableCellValue: { fontWeight: "bold" } }, crosshair: { line: { stroke: "#000000", strokeWidth: 1, strokeOpacity: 0.75, strokeDasharray: "6 6" } }, annotations: { text: { fontSize: 13, outlineWidth: 2, outlineColor: "#ffffff", outlineOpacity: 1 }, link: { stroke: "#000000", strokeWidth: 1, outlineWidth: 2, outlineColor: "#ffffff", outlineOpacity: 1 }, outline: { fill: "none", stroke: "#000000", strokeWidth: 2, outlineWidth: 2, outlineColor: "#ffffff", outlineOpacity: 1 }, symbol: { fill: "#000000", outlineWidth: 2, outlineColor: "#ffffff", outlineOpacity: 1 } } };
function jr() {
  return jr = Object.assign ? Object.assign.bind() : function(e11) {
    for (var r5 = 1; r5 < arguments.length; r5++) {
      var t9 = arguments[r5];
      for (var n6 in t9) Object.prototype.hasOwnProperty.call(t9, n6) && (e11[n6] = t9[n6]);
    }
    return e11;
  }, jr.apply(this, arguments);
}
function Br(e11, r5) {
  if (null == e11) return {};
  var t9, n6, i5 = {}, o4 = Object.keys(e11);
  for (n6 = 0; n6 < o4.length; n6++) t9 = o4[n6], r5.indexOf(t9) >= 0 || (i5[t9] = e11[t9]);
  return i5;
}
var Gr = ["axis.ticks.text", "axis.legend.text", "legends.title.text", "legends.text", "legends.ticks.text", "legends.title.text", "labels.text", "dots.text", "markers.text", "annotations.text"];
var Lr = function(e11, r5) {
  return jr({}, r5, e11);
};
var Ir = function(e11, r5) {
  var t9 = (0, import_merge2.default)({}, e11, r5);
  return Gr.forEach(function(e12) {
    (0, import_set.default)(t9, e12, Lr((0, import_get.default)(t9, e12), t9.text));
  }), t9;
};
var Yr = (0, import_react16.createContext)();
var Ar = function(e11) {
  var t9 = e11.children, n6 = e11.animate, i5 = void 0 === n6 || n6, o4 = e11.config, l3 = void 0 === o4 ? "default" : o4, a5 = (0, import_react16.useMemo)(function() {
    var e12 = (0, import_isString.default)(l3) ? config[l3] : l3;
    return { animate: i5, config: e12 };
  }, [i5, l3]);
  return (0, import_jsx_runtime2.jsx)(Yr.Provider, { value: a5, children: t9 });
};
var Er = { animate: import_prop_types.default.bool, motionConfig: import_prop_types.default.oneOfType([import_prop_types.default.oneOf(Object.keys(config)), import_prop_types.default.shape({ mass: import_prop_types.default.number, tension: import_prop_types.default.number, friction: import_prop_types.default.number, clamp: import_prop_types.default.bool, precision: import_prop_types.default.number, velocity: import_prop_types.default.number, duration: import_prop_types.default.number, easing: import_prop_types.default.func })]) };
Ar.propTypes = { children: import_prop_types.default.node.isRequired, animate: Er.animate, config: Er.motionConfig };
var Ur = function() {
  return (0, import_react16.useContext)(Yr);
};
var Fr = function(e11) {
  var t9 = Ur(), o4 = t9.animate, l3 = t9.config, a5 = function(e12) {
    var r5 = (0, import_react16.useRef)();
    return (0, import_react16.useEffect)(function() {
      r5.current = e12;
    }, [e12]), r5.current;
  }(e11), d3 = (0, import_react16.useMemo)(function() {
    return string_default(a5, e11);
  }, [a5, e11]), s4 = useSpring({ from: { value: 0 }, to: { value: 1 }, reset: true, config: l3, immediate: !o4 }).value;
  return to2(s4, d3);
};
var Xr = { nivo: ["#d76445", "#f47560", "#e8c1a0", "#97e3d5", "#61cdbb", "#00b0a7"], BrBG: (0, import_last.default)(scheme), PRGn: (0, import_last.default)(scheme2), PiYG: (0, import_last.default)(scheme3), PuOr: (0, import_last.default)(scheme4), RdBu: (0, import_last.default)(scheme5), RdGy: (0, import_last.default)(scheme6), RdYlBu: (0, import_last.default)(scheme7), RdYlGn: (0, import_last.default)(scheme8), spectral: (0, import_last.default)(scheme9), blues: (0, import_last.default)(scheme22), greens: (0, import_last.default)(scheme23), greys: (0, import_last.default)(scheme24), oranges: (0, import_last.default)(scheme27), purples: (0, import_last.default)(scheme25), reds: (0, import_last.default)(scheme26), BuGn: (0, import_last.default)(scheme10), BuPu: (0, import_last.default)(scheme11), GnBu: (0, import_last.default)(scheme12), OrRd: (0, import_last.default)(scheme13), PuBuGn: (0, import_last.default)(scheme14), PuBu: (0, import_last.default)(scheme15), PuRd: (0, import_last.default)(scheme16), RdPu: (0, import_last.default)(scheme17), YlGnBu: (0, import_last.default)(scheme18), YlGn: (0, import_last.default)(scheme19), YlOrBr: (0, import_last.default)(scheme20), YlOrRd: (0, import_last.default)(scheme21) };
var Nr = Object.keys(Xr);
var Kr = { nivo: ["#e8c1a0", "#f47560", "#f1e15b", "#e8a838", "#61cdbb", "#97e3d5"], category10: category10_default, accent: Accent_default, dark2: Dark2_default, paired: Paired_default, pastel1: Pastel1_default, pastel2: Pastel2_default, set1: Set1_default, set2: Set2_default, set3: Set3_default, brown_blueGreen: (0, import_last.default)(scheme), purpleRed_green: (0, import_last.default)(scheme2), pink_yellowGreen: (0, import_last.default)(scheme3), purple_orange: (0, import_last.default)(scheme4), red_blue: (0, import_last.default)(scheme5), red_grey: (0, import_last.default)(scheme6), red_yellow_blue: (0, import_last.default)(scheme7), red_yellow_green: (0, import_last.default)(scheme8), spectral: (0, import_last.default)(scheme9), blues: (0, import_last.default)(scheme22), greens: (0, import_last.default)(scheme23), greys: (0, import_last.default)(scheme24), oranges: (0, import_last.default)(scheme27), purples: (0, import_last.default)(scheme25), reds: (0, import_last.default)(scheme26), blue_green: (0, import_last.default)(scheme10), blue_purple: (0, import_last.default)(scheme11), green_blue: (0, import_last.default)(scheme12), orange_red: (0, import_last.default)(scheme13), purple_blue_green: (0, import_last.default)(scheme14), purple_blue: (0, import_last.default)(scheme15), purple_red: (0, import_last.default)(scheme16), red_purple: (0, import_last.default)(scheme17), yellow_green_blue: (0, import_last.default)(scheme18), yellow_green: (0, import_last.default)(scheme19), yellow_orange_brown: (0, import_last.default)(scheme20), yellow_orange_red: (0, import_last.default)(scheme21) };
var et = import_prop_types.default.oneOfType([import_prop_types.default.oneOf(Nr), import_prop_types.default.func, import_prop_types.default.arrayOf(import_prop_types.default.string)]);
var rt = { basis: basis_default2, basisClosed: basisClosed_default2, basisOpen: basisOpen_default, bundle: bundle_default, cardinal: cardinal_default, cardinalClosed: cardinalClosed_default, cardinalOpen: cardinalOpen_default, catmullRom: catmullRom_default, catmullRomClosed: catmullRomClosed_default, catmullRomOpen: catmullRomOpen_default, linear: linear_default, linearClosed: linearClosed_default, monotoneX, monotoneY, natural: natural_default, step: step_default, stepAfter, stepBefore };
var tt = Object.keys(rt);
var nt = tt.filter(function(e11) {
  return e11.endsWith("Closed");
});
var it = (0, import_without.default)(tt, "bundle", "basisClosed", "basisOpen", "cardinalClosed", "cardinalOpen", "catmullRomClosed", "catmullRomOpen", "linearClosed");
var ot = (0, import_without.default)(tt, "bundle", "basisClosed", "basisOpen", "cardinalClosed", "cardinalOpen", "catmullRomClosed", "catmullRomOpen", "linearClosed");
var at = { ascending: ascending_default, descending: descending_default2, insideOut: insideOut_default, none: none_default2, reverse: reverse_default };
var dt = Object.keys(at);
var ut = { expand: expand_default, diverging: diverging_default, none: none_default, silhouette: silhouette_default, wiggle: wiggle_default };
var ct = Object.keys(ut);
var pt = import_prop_types.default.shape({ top: import_prop_types.default.number, right: import_prop_types.default.number, bottom: import_prop_types.default.number, left: import_prop_types.default.number }).isRequired;
var ht = ["normal", "multiply", "screen", "overlay", "darken", "lighten", "color-dodge", "color-burn", "hard-light", "soft-light", "difference", "exclusion", "hue", "saturation", "color", "luminosity"];
var gt = import_prop_types.default.oneOf(ht);
var vt = ordinal(Set3_default);
var _t = { top: 0, right: 0, bottom: 0, left: 0 };
var wt = function(e11, t9, n6) {
  return void 0 === n6 && (n6 = {}), (0, import_react16.useMemo)(function() {
    var r5 = jr({}, _t, n6);
    return { margin: r5, innerWidth: e11 - r5.left - r5.right, innerHeight: t9 - r5.top - r5.bottom, outerWidth: e11, outerHeight: t9 };
  }, [e11, t9, n6.top, n6.right, n6.bottom, n6.left]);
};
var kt = function() {
  var e11 = (0, import_react16.useRef)(null), r5 = (0, import_react16.useState)({ left: 0, top: 0, width: 0, height: 0 }), t9 = r5[0], l3 = r5[1], a5 = (0, import_react16.useState)(function() {
    return "undefined" == typeof ResizeObserver ? null : new ResizeObserver(function(e12) {
      var r6 = e12[0];
      return l3(r6.contentRect);
    });
  })[0];
  return (0, import_react16.useEffect)(function() {
    return e11.current && null !== a5 && a5.observe(e11.current), function() {
      null !== a5 && a5.disconnect();
    };
  }, []), [e11, t9];
};
var Rt = function(e11) {
  return (0, import_react16.useMemo)(function() {
    return Ir(Pr, e11);
  }, [e11]);
};
var xt = function(e11) {
  return "function" == typeof e11 ? e11 : "string" == typeof e11 ? 0 === e11.indexOf("time:") ? timeFormat(e11.slice("5")) : format(e11) : function(e12) {
    return "" + e12;
  };
};
var Ot = function(e11) {
  return (0, import_react16.useMemo)(function() {
    return xt(e11);
  }, [e11]);
};
var qt = (0, import_react16.createContext)();
var Ct = {};
var Wt = function(e11) {
  var r5 = e11.theme, t9 = void 0 === r5 ? Ct : r5, n6 = e11.children, i5 = Rt(t9);
  return (0, import_jsx_runtime2.jsx)(qt.Provider, { value: i5, children: n6 });
};
Wt.propTypes = { children: import_prop_types.default.node.isRequired, theme: import_prop_types.default.object };
var zt = function() {
  return (0, import_react16.useContext)(qt);
};
var Tt = ["outlineWidth", "outlineColor", "outlineOpacity"];
var Mt = function(e11) {
  return e11.outlineWidth, e11.outlineColor, e11.outlineOpacity, Br(e11, Tt);
};
var Pt = function(e11) {
  var r5 = e11.children, t9 = e11.condition, n6 = e11.wrapper;
  return t9 ? (0, import_react16.cloneElement)(n6, {}, r5) : r5;
};
Pt.propTypes = { children: import_prop_types.default.node.isRequired, condition: import_prop_types.default.bool.isRequired, wrapper: import_prop_types.default.element.isRequired };
var jt = { position: "relative" };
var St = function(e11) {
  var r5 = e11.children, t9 = e11.theme, i5 = e11.renderWrapper, o4 = void 0 === i5 || i5, l3 = e11.isInteractive, a5 = void 0 === l3 || l3, d3 = e11.animate, s4 = e11.motionConfig, u3 = (0, import_react16.useRef)(null);
  return (0, import_jsx_runtime2.jsx)(Wt, { theme: t9, children: (0, import_jsx_runtime2.jsx)(Ar, { animate: d3, config: s4, children: (0, import_jsx_runtime2.jsx)(M, { container: u3, children: (0, import_jsx_runtime2.jsxs)(Pt, { condition: o4, wrapper: (0, import_jsx_runtime2.jsx)("div", { style: jt, ref: u3 }), children: [r5, a5 && (0, import_jsx_runtime2.jsx)(F, {})] }) }) }) });
};
St.propTypes = { children: import_prop_types.default.element.isRequired, isInteractive: import_prop_types.default.bool, renderWrapper: import_prop_types.default.bool, theme: import_prop_types.default.object, animate: import_prop_types.default.bool, motionConfig: import_prop_types.default.oneOfType([import_prop_types.default.string, Er.motionConfig]) };
var Bt = function() {
};
var Gt = { position: "relative" };
var Lt = function(e11) {
  var t9 = e11.children, i5 = e11.theme, o4 = e11.isInteractive, l3 = void 0 === o4 || o4, d3 = e11.renderWrapper, s4 = void 0 === d3 || d3, u3 = e11.animate, c9 = e11.motionConfig, f3 = (0, import_react16.useRef)(null), m5 = V(f3), y5 = m5.actions, v6 = m5.state, _3 = (0, import_react16.useCallback)(function(e12, r5) {
    return y5.showTooltipFromEvent(e12, r5);
  }, [y5.showTooltipFromEvent]), w6 = (0, import_react16.useMemo)(function() {
    return { showTooltip: l3 ? _3 : Bt, hideTooltip: l3 ? y5.hideTooltip : Bt };
  }, [y5.hideTooltip, l3, _3]);
  return (0, import_jsx_runtime2.jsx)(Wt, { theme: i5, children: (0, import_jsx_runtime2.jsx)(Ar, { animate: u3, config: c9, children: (0, import_jsx_runtime2.jsx)(j.Provider, { value: y5, children: (0, import_jsx_runtime2.jsx)(O.Provider, { value: v6, children: (0, import_jsx_runtime2.jsxs)(Pt, { condition: s4, wrapper: (0, import_jsx_runtime2.jsx)("div", { style: Gt, ref: f3 }), children: [t9(w6), l3 && (0, import_jsx_runtime2.jsx)(F, {})] }) }) }) }) });
};
Lt.propTypes = { children: import_prop_types.default.func.isRequired, isInteractive: import_prop_types.default.bool, renderWrapper: import_prop_types.default.bool, theme: import_prop_types.default.object.isRequired, animate: import_prop_types.default.bool.isRequired, motionConfig: import_prop_types.default.oneOfType([import_prop_types.default.string, Er.motionConfig]) };
var It = function(e11) {
  var r5 = e11.children, t9 = kt(), n6 = t9[0], i5 = t9[1], o4 = i5.width > 0 && i5.height > 0;
  return (0, import_jsx_runtime2.jsx)("div", { ref: n6, style: { width: "100%", height: "100%" }, children: o4 && r5({ width: i5.width, height: i5.height }) });
};
It.propTypes = { children: import_prop_types.default.func.isRequired };
var Yt = ["id", "colors"];
var Dt = function(e11) {
  var r5 = e11.id, t9 = e11.colors, n6 = Br(e11, Yt);
  return (0, import_jsx_runtime2.jsx)("linearGradient", jr({ id: r5, x1: 0, x2: 0, y1: 0, y2: 1 }, n6, { children: t9.map(function(e12) {
    var r6 = e12.offset, t10 = e12.color, n7 = e12.opacity;
    return (0, import_jsx_runtime2.jsx)("stop", { offset: r6 + "%", stopColor: t10, stopOpacity: void 0 !== n7 ? n7 : 1 }, r6);
  }) }));
};
Dt.propTypes = { id: import_prop_types.default.string.isRequired, colors: import_prop_types.default.arrayOf(import_prop_types.default.shape({ offset: import_prop_types.default.number.isRequired, color: import_prop_types.default.string.isRequired, opacity: import_prop_types.default.number })).isRequired, gradientTransform: import_prop_types.default.string };
var Et = { linearGradient: Dt };
var Ut = { color: "#000000", background: "#ffffff", size: 4, padding: 4, stagger: false };
var Ft = (0, import_react16.memo)(function(e11) {
  var r5 = e11.id, t9 = e11.background, n6 = void 0 === t9 ? Ut.background : t9, i5 = e11.color, o4 = void 0 === i5 ? Ut.color : i5, l3 = e11.size, a5 = void 0 === l3 ? Ut.size : l3, d3 = e11.padding, s4 = void 0 === d3 ? Ut.padding : d3, u3 = e11.stagger, c9 = void 0 === u3 ? Ut.stagger : u3, f3 = a5 + s4, p5 = a5 / 2, h2 = s4 / 2;
  return true === c9 && (f3 = 2 * a5 + 2 * s4), (0, import_jsx_runtime2.jsxs)("pattern", { id: r5, width: f3, height: f3, patternUnits: "userSpaceOnUse", children: [(0, import_jsx_runtime2.jsx)("rect", { width: f3, height: f3, fill: n6 }), (0, import_jsx_runtime2.jsx)("circle", { cx: h2 + p5, cy: h2 + p5, r: p5, fill: o4 }), c9 && (0, import_jsx_runtime2.jsx)("circle", { cx: 1.5 * s4 + a5 + p5, cy: 1.5 * s4 + a5 + p5, r: p5, fill: o4 })] });
});
Ft.displayName = "PatternDots", Ft.propTypes = { id: import_prop_types.default.string.isRequired, color: import_prop_types.default.string.isRequired, background: import_prop_types.default.string.isRequired, size: import_prop_types.default.number.isRequired, padding: import_prop_types.default.number.isRequired, stagger: import_prop_types.default.bool.isRequired };
var Nt = 2 * Math.PI;
var Ht = function(e11) {
  return e11 * Math.PI / 180;
};
var Kt = function(e11) {
  return 180 * e11 / Math.PI;
};
var Jt = function(e11, r5) {
  return { x: Math.cos(e11) * r5, y: Math.sin(e11) * r5 };
};
var Qt = function(e11) {
  var r5 = e11 % 360;
  return r5 < 0 && (r5 += 360), r5;
};
var rn = { svg: { align: { left: "start", center: "middle", right: "end", start: "start", middle: "middle", end: "end" }, baseline: { top: "text-before-edge", center: "central", bottom: "alphabetic" } }, canvas: { align: { left: "left", center: "center", right: "right", start: "left", middle: "center", end: "right" }, baseline: { top: "top", center: "middle", bottom: "bottom" } } };
var nn = { spacing: 5, rotation: 0, background: "#000000", color: "#ffffff", lineWidth: 2 };
var on = (0, import_react16.memo)(function(e11) {
  var r5 = e11.id, t9 = e11.spacing, n6 = void 0 === t9 ? nn.spacing : t9, i5 = e11.rotation, o4 = void 0 === i5 ? nn.rotation : i5, l3 = e11.background, a5 = void 0 === l3 ? nn.background : l3, d3 = e11.color, s4 = void 0 === d3 ? nn.color : d3, u3 = e11.lineWidth, c9 = void 0 === u3 ? nn.lineWidth : u3, f3 = Math.round(o4) % 360, p5 = Math.abs(n6);
  f3 > 180 ? f3 -= 360 : f3 > 90 ? f3 -= 180 : f3 < -180 ? f3 += 360 : f3 < -90 && (f3 += 180);
  var h2, g5 = p5, b5 = p5;
  return 0 === f3 ? h2 = "\n                M 0 0 L " + g5 + " 0\n                M 0 " + b5 + " L " + g5 + " " + b5 + "\n            " : 90 === f3 ? h2 = "\n                M 0 0 L 0 " + b5 + "\n                M " + g5 + " 0 L " + g5 + " " + b5 + "\n            " : (g5 = Math.abs(p5 / Math.sin(Ht(f3))), b5 = p5 / Math.sin(Ht(90 - f3)), h2 = f3 > 0 ? "\n                    M 0 " + -b5 + " L " + 2 * g5 + " " + b5 + "\n                    M " + -g5 + " " + -b5 + " L " + g5 + " " + b5 + "\n                    M " + -g5 + " 0 L " + g5 + " " + 2 * b5 + "\n                " : "\n                    M " + -g5 + " " + b5 + " L " + g5 + " " + -b5 + "\n                    M " + -g5 + " " + 2 * b5 + " L " + 2 * g5 + " " + -b5 + "\n                    M 0 " + 2 * b5 + " L " + 2 * g5 + " 0\n                "), (0, import_jsx_runtime2.jsxs)("pattern", { id: r5, width: g5, height: b5, patternUnits: "userSpaceOnUse", children: [(0, import_jsx_runtime2.jsx)("rect", { width: g5, height: b5, fill: a5, stroke: "rgba(255, 0, 0, 0.1)", strokeWidth: 0 }), (0, import_jsx_runtime2.jsx)("path", { d: h2, strokeWidth: c9, stroke: s4, strokeLinecap: "square" })] });
});
on.displayName = "PatternLines", on.propTypes = { id: import_prop_types.default.string.isRequired, spacing: import_prop_types.default.number.isRequired, rotation: import_prop_types.default.number.isRequired, background: import_prop_types.default.string.isRequired, color: import_prop_types.default.string.isRequired, lineWidth: import_prop_types.default.number.isRequired };
var an = { color: "#000000", background: "#ffffff", size: 4, padding: 4, stagger: false };
var dn = (0, import_react16.memo)(function(e11) {
  var r5 = e11.id, t9 = e11.color, n6 = void 0 === t9 ? an.color : t9, i5 = e11.background, o4 = void 0 === i5 ? an.background : i5, l3 = e11.size, a5 = void 0 === l3 ? an.size : l3, d3 = e11.padding, s4 = void 0 === d3 ? an.padding : d3, u3 = e11.stagger, c9 = void 0 === u3 ? an.stagger : u3, f3 = a5 + s4, p5 = s4 / 2;
  return true === c9 && (f3 = 2 * a5 + 2 * s4), (0, import_jsx_runtime2.jsxs)("pattern", { id: r5, width: f3, height: f3, patternUnits: "userSpaceOnUse", children: [(0, import_jsx_runtime2.jsx)("rect", { width: f3, height: f3, fill: o4 }), (0, import_jsx_runtime2.jsx)("rect", { x: p5, y: p5, width: a5, height: a5, fill: n6 }), c9 && (0, import_jsx_runtime2.jsx)("rect", { x: 1.5 * s4 + a5, y: 1.5 * s4 + a5, width: a5, height: a5, fill: n6 })] });
});
dn.displayName = "PatternSquares", dn.propTypes = { id: import_prop_types.default.string.isRequired, color: import_prop_types.default.string.isRequired, background: import_prop_types.default.string.isRequired, size: import_prop_types.default.number.isRequired, padding: import_prop_types.default.number.isRequired, stagger: import_prop_types.default.bool.isRequired };
var un = { patternDots: Ft, patternLines: on, patternSquares: dn };
var cn = ["type"];
var fn = jr({}, Et, un);
var pn = function(e11) {
  var r5 = e11.defs;
  return !r5 || r5.length < 1 ? null : (0, import_jsx_runtime2.jsx)("defs", { "aria-hidden": true, children: r5.map(function(e12) {
    var r6 = e12.type, t9 = Br(e12, cn);
    return fn[r6] ? (0, import_react16.createElement)(fn[r6], jr({ key: t9.id }, t9)) : null;
  }) });
};
pn.propTypes = { defs: import_prop_types.default.arrayOf(import_prop_types.default.shape({ type: import_prop_types.default.oneOf(Object.keys(fn)).isRequired, id: import_prop_types.default.string.isRequired })) };
var hn = (0, import_react16.memo)(pn);
var gn = function(e11) {
  var r5 = e11.width, t9 = e11.height, n6 = e11.margin, i5 = e11.defs, o4 = e11.children, l3 = e11.role, a5 = e11.ariaLabel, d3 = e11.ariaLabelledBy, s4 = e11.ariaDescribedBy, u3 = e11.isFocusable, c9 = zt();
  return (0, import_jsx_runtime2.jsxs)("svg", { xmlns: "http://www.w3.org/2000/svg", width: r5, height: t9, role: l3, "aria-label": a5, "aria-labelledby": d3, "aria-describedby": s4, focusable: u3, tabIndex: u3 ? 0 : void 0, children: [(0, import_jsx_runtime2.jsx)(hn, { defs: i5 }), (0, import_jsx_runtime2.jsx)("rect", { width: r5, height: t9, fill: c9.background }), (0, import_jsx_runtime2.jsx)("g", { transform: "translate(" + n6.left + "," + n6.top + ")", children: o4 })] });
};
gn.propTypes = { width: import_prop_types.default.number.isRequired, height: import_prop_types.default.number.isRequired, margin: import_prop_types.default.shape({ top: import_prop_types.default.number.isRequired, left: import_prop_types.default.number.isRequired }).isRequired, defs: import_prop_types.default.array, children: import_prop_types.default.oneOfType([import_prop_types.default.arrayOf(import_prop_types.default.node), import_prop_types.default.node]).isRequired, role: import_prop_types.default.string, isFocusable: import_prop_types.default.bool, ariaLabel: import_prop_types.default.string, ariaLabelledBy: import_prop_types.default.string, ariaDescribedBy: import_prop_types.default.string };
var bn = function(e11) {
  var r5 = e11.size, t9 = e11.color, n6 = e11.borderWidth, i5 = e11.borderColor;
  return (0, import_jsx_runtime2.jsx)("circle", { r: r5 / 2, fill: t9, stroke: i5, strokeWidth: n6, style: { pointerEvents: "none" } });
};
bn.propTypes = { size: import_prop_types.default.number.isRequired, color: import_prop_types.default.string.isRequired, borderWidth: import_prop_types.default.number.isRequired, borderColor: import_prop_types.default.string.isRequired };
var mn = (0, import_react16.memo)(bn);
var yn = function(e11) {
  var r5 = e11.x, t9 = e11.y, n6 = e11.symbol, i5 = void 0 === n6 ? mn : n6, o4 = e11.size, l3 = e11.datum, a5 = e11.color, d3 = e11.borderWidth, u3 = e11.borderColor, c9 = e11.label, f3 = e11.labelTextAnchor, p5 = void 0 === f3 ? "middle" : f3, h2 = e11.labelYOffset, g5 = void 0 === h2 ? -12 : h2, b5 = zt(), m5 = Ur(), y5 = m5.animate, v6 = m5.config, _3 = useSpring({ transform: "translate(" + r5 + ", " + t9 + ")", config: v6, immediate: !y5 });
  return (0, import_jsx_runtime2.jsxs)(animated.g, { transform: _3.transform, style: { pointerEvents: "none" }, children: [(0, import_react16.createElement)(i5, { size: o4, color: a5, datum: l3, borderWidth: d3, borderColor: u3 }), c9 && (0, import_jsx_runtime2.jsx)("text", { textAnchor: p5, y: g5, style: Mt(b5.dots.text), children: c9 })] });
};
yn.propTypes = { x: import_prop_types.default.number.isRequired, y: import_prop_types.default.number.isRequired, datum: import_prop_types.default.object.isRequired, size: import_prop_types.default.number.isRequired, color: import_prop_types.default.string.isRequired, borderWidth: import_prop_types.default.number.isRequired, borderColor: import_prop_types.default.string.isRequired, symbol: import_prop_types.default.oneOfType([import_prop_types.default.func, import_prop_types.default.object]), label: import_prop_types.default.oneOfType([import_prop_types.default.string, import_prop_types.default.number]), labelTextAnchor: import_prop_types.default.oneOf(["start", "middle", "end"]), labelYOffset: import_prop_types.default.number };
var vn = (0, import_react16.memo)(yn);
var _n = function(e11) {
  var r5 = e11.width, t9 = e11.height, n6 = e11.axis, i5 = e11.scale, o4 = e11.value, l3 = e11.lineStyle, a5 = e11.textStyle, d3 = e11.legend, s4 = e11.legendNode, u3 = e11.legendPosition, c9 = void 0 === u3 ? "top-right" : u3, f3 = e11.legendOffsetX, p5 = void 0 === f3 ? 14 : f3, h2 = e11.legendOffsetY, g5 = void 0 === h2 ? 14 : h2, b5 = e11.legendOrientation, m5 = void 0 === b5 ? "horizontal" : b5, y5 = zt(), v6 = 0, _3 = 0, w6 = 0, k7 = 0;
  if ("y" === n6 ? (w6 = i5(o4), _3 = r5) : (v6 = i5(o4), k7 = t9), d3 && !s4) {
    var R2 = function(e12) {
      var r6 = e12.axis, t10 = e12.width, n7 = e12.height, i6 = e12.position, o5 = e12.offsetX, l4 = e12.offsetY, a6 = e12.orientation, d4 = 0, s5 = 0, u4 = "vertical" === a6 ? -90 : 0, c10 = "start";
      if ("x" === r6) switch (i6) {
        case "top-left":
          d4 = -o5, s5 = l4, c10 = "end";
          break;
        case "top":
          s5 = -l4, c10 = "horizontal" === a6 ? "middle" : "start";
          break;
        case "top-right":
          d4 = o5, s5 = l4, c10 = "horizontal" === a6 ? "start" : "end";
          break;
        case "right":
          d4 = o5, s5 = n7 / 2, c10 = "horizontal" === a6 ? "start" : "middle";
          break;
        case "bottom-right":
          d4 = o5, s5 = n7 - l4, c10 = "start";
          break;
        case "bottom":
          s5 = n7 + l4, c10 = "horizontal" === a6 ? "middle" : "end";
          break;
        case "bottom-left":
          s5 = n7 - l4, d4 = -o5, c10 = "horizontal" === a6 ? "end" : "start";
          break;
        case "left":
          d4 = -o5, s5 = n7 / 2, c10 = "horizontal" === a6 ? "end" : "middle";
      }
      else switch (i6) {
        case "top-left":
          d4 = o5, s5 = -l4, c10 = "start";
          break;
        case "top":
          d4 = t10 / 2, s5 = -l4, c10 = "horizontal" === a6 ? "middle" : "start";
          break;
        case "top-right":
          d4 = t10 - o5, s5 = -l4, c10 = "horizontal" === a6 ? "end" : "start";
          break;
        case "right":
          d4 = t10 + o5, c10 = "horizontal" === a6 ? "start" : "middle";
          break;
        case "bottom-right":
          d4 = t10 - o5, s5 = l4, c10 = "end";
          break;
        case "bottom":
          d4 = t10 / 2, s5 = l4, c10 = "horizontal" === a6 ? "middle" : "end";
          break;
        case "bottom-left":
          d4 = o5, s5 = l4, c10 = "horizontal" === a6 ? "start" : "end";
          break;
        case "left":
          d4 = -o5, c10 = "horizontal" === a6 ? "end" : "middle";
      }
      return { x: d4, y: s5, rotation: u4, textAnchor: c10 };
    }({ axis: n6, width: r5, height: t9, position: c9, offsetX: p5, offsetY: g5, orientation: m5 });
    s4 = (0, import_jsx_runtime2.jsx)("text", { transform: "translate(" + R2.x + ", " + R2.y + ") rotate(" + R2.rotation + ")", textAnchor: R2.textAnchor, dominantBaseline: "central", style: a5, children: d3 });
  }
  return (0, import_jsx_runtime2.jsxs)("g", { transform: "translate(" + v6 + ", " + w6 + ")", children: [(0, import_jsx_runtime2.jsx)("line", { x1: 0, x2: _3, y1: 0, y2: k7, stroke: y5.markers.lineColor, strokeWidth: y5.markers.lineStrokeWidth, style: l3 }), s4] });
};
_n.propTypes = { width: import_prop_types.default.number.isRequired, height: import_prop_types.default.number.isRequired, axis: import_prop_types.default.oneOf(["x", "y"]).isRequired, scale: import_prop_types.default.func.isRequired, value: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.instanceOf(Date)]).isRequired, lineStyle: import_prop_types.default.object, textStyle: import_prop_types.default.object, legend: import_prop_types.default.string, legendPosition: import_prop_types.default.oneOf(["top-left", "top", "top-right", "right", "bottom-right", "bottom", "bottom-left", "left"]), legendOffsetX: import_prop_types.default.number.isRequired, legendOffsetY: import_prop_types.default.number.isRequired, legendOrientation: import_prop_types.default.oneOf(["horizontal", "vertical"]).isRequired };
var wn = (0, import_react16.memo)(_n);
var kn = function(e11) {
  var r5 = e11.markers, t9 = e11.width, n6 = e11.height, i5 = e11.xScale, o4 = e11.yScale;
  return r5 && 0 !== r5.length ? r5.map(function(e12, r6) {
    return (0, import_jsx_runtime2.jsx)(wn, jr({}, e12, { width: t9, height: n6, scale: "y" === e12.axis ? o4 : i5 }), r6);
  }) : null;
};
kn.propTypes = { width: import_prop_types.default.number.isRequired, height: import_prop_types.default.number.isRequired, xScale: import_prop_types.default.func.isRequired, yScale: import_prop_types.default.func.isRequired, markers: import_prop_types.default.arrayOf(import_prop_types.default.shape({ axis: import_prop_types.default.oneOf(["x", "y"]).isRequired, value: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string, import_prop_types.default.instanceOf(Date)]).isRequired, lineStyle: import_prop_types.default.object, textStyle: import_prop_types.default.object })) };
var Rn = (0, import_react16.memo)(kn);
var Cn = function(e11) {
  return (0, import_isFunction.default)(e11) ? e11 : function(r5) {
    return (0, import_get.default)(r5, e11);
  };
};
var Wn = function(e11) {
  return (0, import_react16.useMemo)(function() {
    return Cn(e11);
  }, [e11]);
};
var jn = function(e11, r5, t9, n6, i5, o4) {
  return e11 <= i5 && i5 <= e11 + t9 && r5 <= o4 && o4 <= r5 + n6;
};
var Sn = function(e11, r5) {
  var t9, n6 = "touches" in r5 ? r5.touches[0] : r5, i5 = n6.clientX, o4 = n6.clientY, l3 = e11.getBoundingClientRect(), a5 = (t9 = void 0 !== e11.getBBox ? e11.getBBox() : { width: e11.offsetWidth || 0, height: e11.offsetHeight || 0 }).width === l3.width ? 1 : t9.width / l3.width;
  return [(i5 - l3.left) * a5, (o4 - l3.top) * a5];
};
var Bn = Object.keys(Et);
var Gn = Object.keys(un);
var Ln = function(e11, r5, t9) {
  if ("*" === e11) return true;
  if ((0, import_isFunction.default)(e11)) return e11(r5);
  if ((0, import_isPlainObject.default)(e11)) {
    var n6 = t9 ? (0, import_get.default)(r5, t9) : r5;
    return (0, import_isEqual.default)((0, import_pick.default)(n6, Object.keys(e11)), e11);
  }
  return false;
};
var In = function(e11, r5, t9, n6) {
  var i5 = void 0 === n6 ? {} : n6, o4 = i5.dataKey, l3 = i5.colorKey, a5 = void 0 === l3 ? "color" : l3, d3 = i5.targetKey, s4 = void 0 === d3 ? "fill" : d3, u3 = [], c9 = {};
  return e11.length && r5.length && (u3 = [].concat(e11), r5.forEach(function(r6) {
    for (var n7 = function() {
      var n8 = t9[i6], l4 = n8.id, d4 = n8.match;
      if (Ln(d4, r6, o4)) {
        var f3 = e11.find(function(e12) {
          return e12.id === l4;
        });
        if (f3) {
          if (Gn.includes(f3.type)) if ("inherit" === f3.background || "inherit" === f3.color) {
            var p5 = (0, import_get.default)(r6, a5), h2 = f3.background, g5 = f3.color, b5 = l4;
            "inherit" === f3.background && (b5 = b5 + ".bg." + p5, h2 = p5), "inherit" === f3.color && (b5 = b5 + ".fg." + p5, g5 = p5), (0, import_set.default)(r6, s4, "url(#" + b5 + ")"), c9[b5] || (u3.push(jr({}, f3, { id: b5, background: h2, color: g5 })), c9[b5] = 1);
          } else (0, import_set.default)(r6, s4, "url(#" + l4 + ")");
          else if (Bn.includes(f3.type)) {
            if (f3.colors.map(function(e12) {
              return e12.color;
            }).includes("inherit")) {
              var m5 = (0, import_get.default)(r6, a5), _3 = l4, w6 = jr({}, f3, { colors: f3.colors.map(function(e12, r7) {
                return "inherit" !== e12.color ? e12 : (_3 = _3 + "." + r7 + "." + m5, jr({}, e12, { color: "inherit" === e12.color ? m5 : e12.color }));
              }) });
              w6.id = _3, (0, import_set.default)(r6, s4, "url(#" + _3 + ")"), c9[_3] || (u3.push(w6), c9[_3] = 1);
            } else (0, import_set.default)(r6, s4, "url(#" + l4 + ")");
          }
        }
        return "break";
      }
    }, i6 = 0; i6 < t9.length; i6++) {
      if ("break" === n7()) break;
    }
  })), u3;
};

// node_modules/@nivo/scales/dist/nivo-scales.es.js
var import_uniq = __toESM(require_uniq());
var import_uniqBy = __toESM(require_uniqBy());
var import_sortBy = __toESM(require_sortBy());
var import_last2 = __toESM(require_last());
var import_isDate = __toESM(require_isDate());
var J = [function(n6) {
  return n6.setMilliseconds(0);
}, function(n6) {
  return n6.setSeconds(0);
}, function(n6) {
  return n6.setMinutes(0);
}, function(n6) {
  return n6.setHours(0);
}, function(n6) {
  return n6.setDate(1);
}, function(n6) {
  return n6.setMonth(0);
}];
var K2 = { millisecond: [], second: J.slice(0, 1), minute: J.slice(0, 2), hour: J.slice(0, 3), day: J.slice(0, 4), month: J.slice(0, 5), year: J.slice(0, 6) };
var L = function(n6) {
  return function(t9) {
    return K2[n6].forEach(function(n7) {
      n7(t9);
    }), t9;
  };
};
var Q = function(n6) {
  var t9 = n6.format, r5 = void 0 === t9 ? "native" : t9, e11 = n6.precision, a5 = void 0 === e11 ? "millisecond" : e11, u3 = n6.useUTC, c9 = void 0 === u3 || u3, s4 = L(a5);
  return function(n7) {
    if (void 0 === n7) return n7;
    if ("native" === r5 || n7 instanceof Date) return s4(n7);
    var t10 = c9 ? utcParse(r5) : timeParse(r5);
    return s4(t10(n7));
  };
};
var W2 = function(n6, t9, r5, e11) {
  var a5, i5, o4, c9, s4 = n6.min, d3 = void 0 === s4 ? 0 : s4, f3 = n6.max, l3 = void 0 === f3 ? "auto" : f3, m5 = n6.stacked, v6 = void 0 !== m5 && m5, y5 = n6.reverse, p5 = void 0 !== y5 && y5, h2 = n6.clamp, g5 = void 0 !== h2 && h2, x5 = n6.nice, k7 = void 0 !== x5 && x5;
  "auto" === d3 ? a5 = true === v6 ? null != (i5 = t9.minStacked) ? i5 : 0 : t9.min : a5 = d3;
  "auto" === l3 ? o4 = true === v6 ? null != (c9 = t9.maxStacked) ? c9 : 0 : t9.max : o4 = l3;
  var T4 = linear2().rangeRound("x" === e11 ? [0, r5] : [r5, 0]).domain(p5 ? [o4, a5] : [a5, o4]).clamp(g5);
  return true === k7 ? T4.nice() : "number" == typeof k7 && T4.nice(k7), X(T4, v6);
};
var X = function(n6, t9) {
  void 0 === t9 && (t9 = false);
  var r5 = n6;
  return r5.type = "linear", r5.stacked = t9, r5;
};
var Y = function(n6, t9, r5) {
  var e11 = point().range([0, r5]).domain(t9.all);
  return e11.type = "point", e11;
};
var _ = function(n6, t9, r5, e11) {
  var a5 = n6.round, i5 = void 0 === a5 || a5, o4 = band().range("x" === e11 ? [0, r5] : [r5, 0]).domain(t9.all).round(i5);
  return nn2(o4);
};
var nn2 = function(n6) {
  var t9 = n6;
  return t9.type = "band", t9;
};
var tn = function(n6, t9, r5) {
  var e11, a5, i5 = n6.format, o4 = void 0 === i5 ? "native" : i5, u3 = n6.precision, c9 = void 0 === u3 ? "millisecond" : u3, s4 = n6.min, l3 = void 0 === s4 ? "auto" : s4, m5 = n6.max, v6 = void 0 === m5 ? "auto" : m5, y5 = n6.useUTC, p5 = void 0 === y5 || y5, h2 = n6.nice, g5 = void 0 !== h2 && h2, x5 = Q({ format: o4, precision: c9, useUTC: p5 });
  e11 = "auto" === l3 ? x5(t9.min) : "native" !== o4 ? x5(l3) : l3, a5 = "auto" === v6 ? x5(t9.max) : "native" !== o4 ? x5(v6) : v6;
  var k7 = p5 ? utcTime() : time();
  k7.range([0, r5]), e11 && a5 && k7.domain([e11, a5]), true === g5 ? k7.nice() : "object" != typeof g5 && "number" != typeof g5 || k7.nice(g5);
  var T4 = k7;
  return T4.type = "time", T4.useUTC = p5, T4;
};
var rn2 = function(n6, t9, r5, e11) {
  var a5, i5 = n6.base, o4 = void 0 === i5 ? 10 : i5, u3 = n6.min, c9 = void 0 === u3 ? "auto" : u3, s4 = n6.max, d3 = void 0 === s4 ? "auto" : s4;
  if (t9.all.some(function(n7) {
    return 0 === n7;
  })) throw new Error("a log scale domain must not include or cross zero");
  var f3, m5, v6 = false;
  if (t9.all.filter(function(n7) {
    return null != n7;
  }).forEach(function(n7) {
    v6 || (void 0 === a5 ? a5 = Math.sign(n7) : Math.sign(n7) !== a5 && (v6 = true));
  }), v6) throw new Error("a log scale domain must be strictly-positive or strictly-negative");
  f3 = "auto" === c9 ? t9.min : c9, m5 = "auto" === d3 ? t9.max : d3;
  var y5 = log().domain([f3, m5]).rangeRound("x" === e11 ? [0, r5] : [r5, 0]).base(o4).nice();
  return y5.type = "log", y5;
};
var en = function(n6, t9, r5, e11) {
  var a5, i5, o4 = n6.constant, u3 = void 0 === o4 ? 1 : o4, c9 = n6.min, s4 = void 0 === c9 ? "auto" : c9, d3 = n6.max, f3 = void 0 === d3 ? "auto" : d3, l3 = n6.reverse, v6 = void 0 !== l3 && l3;
  a5 = "auto" === s4 ? t9.min : s4, i5 = "auto" === f3 ? t9.max : f3;
  var y5 = symlog().constant(u3).rangeRound("x" === e11 ? [0, r5] : [r5, 0]).nice();
  true === v6 ? y5.domain([i5, a5]) : y5.domain([a5, i5]);
  var p5 = y5;
  return p5.type = "symlog", p5;
};
function cn2(n6, t9, r5, e11) {
  switch (n6.type) {
    case "linear":
      return W2(n6, t9, r5, e11);
    case "point":
      return Y(n6, t9, r5);
    case "band":
      return _(n6, t9, r5, e11);
    case "time":
      return tn(n6, t9, r5);
    case "log":
      return rn2(n6, t9, r5, e11);
    case "symlog":
      return en(n6, t9, r5, e11);
    default:
      throw new Error("invalid scale spec");
  }
}
var pn2 = function(n6) {
  var t9 = n6.bandwidth();
  if (0 === t9) return n6;
  var r5 = t9 / 2;
  return n6.round() && (r5 = Math.round(r5)), function(t10) {
    var e11;
    return (null != (e11 = n6(t10)) ? e11 : 0) + r5;
  };
};
var hn2 = { millisecond: [millisecond_default, millisecond_default], second: [second_default, second_default], minute: [minute_default, utcMinute_default], hour: [hour_default, utcHour_default], day: [newInterval(function(n6) {
  return n6.setHours(0, 0, 0, 0);
}, function(n6, t9) {
  return n6.setDate(n6.getDate() + t9);
}, function(n6, t9) {
  return (t9.getTime() - n6.getTime()) / 864e5;
}, function(n6) {
  return Math.floor(n6.getTime() / 864e5);
}), newInterval(function(n6) {
  return n6.setUTCHours(0, 0, 0, 0);
}, function(n6, t9) {
  return n6.setUTCDate(n6.getUTCDate() + t9);
}, function(n6, t9) {
  return (t9.getTime() - n6.getTime()) / 864e5;
}, function(n6) {
  return Math.floor(n6.getTime() / 864e5);
})], week: [sunday, utcSunday2], sunday: [sunday, utcSunday2], monday: [monday, utcMonday2], tuesday: [tuesday, utcTuesday2], wednesday: [wednesday, utcWednesday2], thursday: [thursday, utcThursday2], friday: [friday, utcFriday2], saturday: [saturday, utcSaturday2], month: [month_default, utcMonth_default], year: [year_default, utcYear_default] };
var gn2 = Object.keys(hn2);
var xn = new RegExp("^every\\s*(\\d+)?\\s*(" + gn2.join("|") + ")s?$", "i");
var kn2 = function(n6, t9) {
  if (Array.isArray(t9)) return t9;
  if ("string" == typeof t9 && "useUTC" in n6) {
    var r5 = t9.match(xn);
    if (r5) {
      var e11 = r5[1], a5 = r5[2], i5 = hn2[a5][n6.useUTC ? 1 : 0];
      if ("day" === a5) {
        var o4, u3, c9 = n6.domain(), s4 = c9[0], d3 = c9[1], f3 = new Date(d3);
        return f3.setDate(f3.getDate() + 1), null != (o4 = null == (u3 = i5.every(Number(null != e11 ? e11 : 1))) ? void 0 : u3.range(s4, f3)) ? o4 : [];
      }
      if (void 0 === e11) return n6.ticks(i5);
      var l3 = i5.every(Number(e11));
      if (l3) return n6.ticks(l3);
    }
    throw new Error("Invalid tickValues: " + t9);
  }
  if ("ticks" in n6) {
    if (void 0 === t9) return n6.ticks();
    if ("number" == typeof (m5 = t9) && isFinite(m5) && Math.floor(m5) === m5) return n6.ticks(t9);
  }
  var m5;
  return n6.domain();
};

// node_modules/@nivo/axes/dist/nivo-axes.es.js
var import_jsx_runtime3 = __toESM(require_jsx_runtime());
function p2() {
  return p2 = Object.assign ? Object.assign.bind() : function(t9) {
    for (var e11 = 1; e11 < arguments.length; e11++) {
      var i5 = arguments[e11];
      for (var n6 in i5) Object.prototype.hasOwnProperty.call(i5, n6) && (t9[n6] = i5[n6]);
    }
    return t9;
  }, p2.apply(this, arguments);
}
var b2 = function(t9) {
  var e11, i5 = t9.axis, n6 = t9.scale, r5 = t9.ticksPosition, o4 = t9.tickValues, l3 = t9.tickSize, s4 = t9.tickPadding, c9 = t9.tickRotation, f3 = t9.truncateTickAt, u3 = t9.engine, d3 = void 0 === u3 ? "svg" : u3, x5 = kn2(n6, o4), m5 = rn[d3], k7 = "bandwidth" in n6 ? pn2(n6) : n6, g5 = { lineX: 0, lineY: 0 }, v6 = { textX: 0, textY: 0 }, b5 = "object" == typeof document && "rtl" === document.dir, P5 = m5.align.center, T4 = m5.baseline.center;
  "x" === i5 ? (e11 = function(t10) {
    var e12;
    return { x: null != (e12 = k7(t10)) ? e12 : 0, y: 0 };
  }, g5.lineY = l3 * ("after" === r5 ? 1 : -1), v6.textY = (l3 + s4) * ("after" === r5 ? 1 : -1), T4 = "after" === r5 ? m5.baseline.top : m5.baseline.bottom, 0 === c9 ? P5 = m5.align.center : "after" === r5 && c9 < 0 || "before" === r5 && c9 > 0 ? (P5 = m5.align[b5 ? "left" : "right"], T4 = m5.baseline.center) : ("after" === r5 && c9 > 0 || "before" === r5 && c9 < 0) && (P5 = m5.align[b5 ? "right" : "left"], T4 = m5.baseline.center)) : (e11 = function(t10) {
    var e12;
    return { x: 0, y: null != (e12 = k7(t10)) ? e12 : 0 };
  }, g5.lineX = l3 * ("after" === r5 ? 1 : -1), v6.textX = (l3 + s4) * ("after" === r5 ? 1 : -1), P5 = "after" === r5 ? m5.align.left : m5.align.right);
  return { ticks: x5.map(function(t10) {
    var i6 = "string" == typeof t10 ? function(t11) {
      var e12 = String(t11).length;
      return f3 && f3 > 0 && e12 > f3 ? "" + String(t11).slice(0, f3).concat("...") : "" + t11;
    }(t10) : t10;
    return p2({ key: t10 instanceof Date ? "" + t10.valueOf() : "" + t10, value: i6 }, e11(t10), g5, v6);
  }), textAlign: P5, textBaseline: T4 };
};
var P2 = function(t9, e11) {
  if (void 0 === t9 || "function" == typeof t9) return t9;
  if ("time" === e11.type) {
    var i5 = timeFormat(t9);
    return function(t10) {
      return i5(t10 instanceof Date ? t10 : new Date(t10));
    };
  }
  return format(t9);
};
var T2 = function(t9) {
  var e11, i5 = t9.width, n6 = t9.height, r5 = t9.scale, a5 = t9.axis, o4 = t9.values, l3 = (e11 = o4, Array.isArray(e11) ? o4 : void 0) || kn2(r5, o4), s4 = "bandwidth" in r5 ? pn2(r5) : r5, c9 = "x" === a5 ? l3.map(function(t10) {
    var e12, i6;
    return { key: t10 instanceof Date ? "" + t10.valueOf() : "" + t10, x1: null != (e12 = s4(t10)) ? e12 : 0, x2: null != (i6 = s4(t10)) ? i6 : 0, y1: 0, y2: n6 };
  }) : l3.map(function(t10) {
    var e12, n7;
    return { key: t10 instanceof Date ? "" + t10.valueOf() : "" + t10, x1: 0, x2: i5, y1: null != (e12 = s4(t10)) ? e12 : 0, y2: null != (n7 = s4(t10)) ? n7 : 0 };
  });
  return c9;
};
var A3 = (0, import_react17.memo)(function(t9) {
  var e11, n6 = t9.value, r5 = t9.format, a5 = t9.lineX, s4 = t9.lineY, c9 = t9.onClick, u3 = t9.textBaseline, d3 = t9.textAnchor, x5 = t9.animatedProps, m5 = zt(), y5 = m5.axis.ticks.line, h2 = m5.axis.ticks.text, v6 = null != (e11 = null == r5 ? void 0 : r5(n6)) ? e11 : n6, b5 = (0, import_react17.useMemo)(function() {
    var t10 = { opacity: x5.opacity };
    return c9 ? { style: p2({}, t10, { cursor: "pointer" }), onClick: function(t11) {
      return c9(t11, v6);
    } } : { style: t10 };
  }, [x5.opacity, c9, v6]);
  return (0, import_jsx_runtime3.jsxs)(animated.g, p2({ transform: x5.transform }, b5, { children: [(0, import_jsx_runtime3.jsx)("line", { x1: 0, x2: a5, y1: 0, y2: s4, style: y5 }), h2.outlineWidth > 0 && (0, import_jsx_runtime3.jsx)(animated.text, { dominantBaseline: u3, textAnchor: d3, transform: x5.textTransform, style: h2, strokeWidth: 2 * h2.outlineWidth, stroke: h2.outlineColor, strokeLinejoin: "round", children: "" + v6 }), (0, import_jsx_runtime3.jsx)(animated.text, { dominantBaseline: u3, textAnchor: d3, transform: x5.textTransform, style: Mt(h2), children: "" + v6 })] }));
});
var S = function(e11) {
  var r5 = e11.axis, a5 = e11.scale, l3 = e11.x, c9 = void 0 === l3 ? 0 : l3, x5 = e11.y, m5 = void 0 === x5 ? 0 : x5, y5 = e11.length, h2 = e11.ticksPosition, T4 = e11.tickValues, S5 = e11.tickSize, W5 = void 0 === S5 ? 5 : S5, w6 = e11.tickPadding, B4 = void 0 === w6 ? 5 : w6, X4 = e11.tickRotation, Y4 = void 0 === X4 ? 0 : X4, C7 = e11.format, O6 = e11.renderTick, j5 = void 0 === O6 ? A3 : O6, z5 = e11.truncateTickAt, V3 = e11.legend, D3 = e11.legendPosition, R2 = void 0 === D3 ? "end" : D3, E5 = e11.legendOffset, q4 = void 0 === E5 ? 0 : E5, F2 = e11.onClick, L4 = e11.ariaHidden, N3 = zt(), H2 = N3.axis.legend.text, I3 = (0, import_react17.useMemo)(function() {
    return P2(C7, a5);
  }, [C7, a5]), J4 = b2({ axis: r5, scale: a5, ticksPosition: h2, tickValues: T4, tickSize: W5, tickPadding: B4, tickRotation: Y4, truncateTickAt: z5 }), G = J4.ticks, K4 = J4.textAlign, M4 = J4.textBaseline, Q3 = null;
  if (void 0 !== V3) {
    var U2, Z2 = 0, $2 = 0, _3 = 0;
    "y" === r5 ? (_3 = -90, Z2 = q4, "start" === R2 ? (U2 = "start", $2 = y5) : "middle" === R2 ? (U2 = "middle", $2 = y5 / 2) : "end" === R2 && (U2 = "end")) : ($2 = q4, "start" === R2 ? U2 = "start" : "middle" === R2 ? (U2 = "middle", Z2 = y5 / 2) : "end" === R2 && (U2 = "end", Z2 = y5)), Q3 = (0, import_jsx_runtime3.jsxs)(import_jsx_runtime3.Fragment, { children: [H2.outlineWidth > 0 && (0, import_jsx_runtime3.jsx)("text", { transform: "translate(" + Z2 + ", " + $2 + ") rotate(" + _3 + ")", textAnchor: U2, style: p2({ dominantBaseline: "central" }, H2), strokeWidth: 2 * H2.outlineWidth, stroke: H2.outlineColor, strokeLinejoin: "round", children: V3 }), (0, import_jsx_runtime3.jsx)("text", { transform: "translate(" + Z2 + ", " + $2 + ") rotate(" + _3 + ")", textAnchor: U2, style: p2({ dominantBaseline: "central" }, H2), children: V3 })] });
  }
  var tt2 = Ur(), et2 = tt2.animate, it2 = tt2.config, nt2 = useSpring({ transform: "translate(" + c9 + "," + m5 + ")", lineX2: "x" === r5 ? y5 : 0, lineY2: "x" === r5 ? 0 : y5, config: it2, immediate: !et2 }), rt2 = (0, import_react17.useCallback)(function(t9) {
    return { opacity: 1, transform: "translate(" + t9.x + "," + t9.y + ")", textTransform: "translate(" + t9.textX + "," + t9.textY + ") rotate(" + Y4 + ")" };
  }, [Y4]), at2 = (0, import_react17.useCallback)(function(t9) {
    return { opacity: 0, transform: "translate(" + t9.x + "," + t9.y + ")", textTransform: "translate(" + t9.textX + "," + t9.textY + ") rotate(" + Y4 + ")" };
  }, [Y4]), ot2 = useTransition(G, { keys: function(t9) {
    return t9.key;
  }, initial: rt2, from: at2, enter: rt2, update: rt2, leave: { opacity: 0 }, config: it2, immediate: !et2 });
  return (0, import_jsx_runtime3.jsxs)(animated.g, { transform: nt2.transform, "aria-hidden": L4, children: [ot2(function(e12, i5, n6, r6) {
    return t6.createElement(j5, p2({ tickIndex: r6, format: I3, rotate: Y4, textBaseline: M4, textAnchor: K4, truncateTickAt: z5, animatedProps: e12 }, i5, F2 ? { onClick: F2 } : {}));
  }), (0, import_jsx_runtime3.jsx)(animated.line, { style: N3.axis.domain.line, x1: 0, x2: nt2.lineX2, y1: 0, y2: nt2.lineY2 }), Q3] });
};
var W3 = (0, import_react17.memo)(S);
var w2 = ["top", "right", "bottom", "left"];
var B2 = (0, import_react17.memo)(function(t9) {
  var e11 = t9.xScale, i5 = t9.yScale, n6 = t9.width, r5 = t9.height, a5 = { top: t9.top, right: t9.right, bottom: t9.bottom, left: t9.left };
  return (0, import_jsx_runtime3.jsx)(import_jsx_runtime3.Fragment, { children: w2.map(function(t10) {
    var o4 = a5[t10];
    if (!o4) return null;
    var l3 = "top" === t10 || "bottom" === t10;
    return (0, import_jsx_runtime3.jsx)(W3, p2({}, o4, { axis: l3 ? "x" : "y", x: "right" === t10 ? n6 : 0, y: "bottom" === t10 ? r5 : 0, scale: l3 ? e11 : i5, length: l3 ? n6 : r5, ticksPosition: "top" === t10 || "left" === t10 ? "before" : "after", truncateTickAt: o4.truncateTickAt }), t10);
  }) });
});
var X2 = (0, import_react17.memo)(function(t9) {
  var e11 = t9.animatedProps, i5 = zt();
  return (0, import_jsx_runtime3.jsx)(animated.line, p2({}, e11, i5.grid.line));
});
var Y2 = (0, import_react17.memo)(function(t9) {
  var e11 = t9.lines, i5 = Ur(), n6 = i5.animate, a5 = i5.config, o4 = useTransition(e11, { keys: function(t10) {
    return t10.key;
  }, initial: function(t10) {
    return { opacity: 1, x1: t10.x1, x2: t10.x2, y1: t10.y1, y2: t10.y2 };
  }, from: function(t10) {
    return { opacity: 0, x1: t10.x1, x2: t10.x2, y1: t10.y1, y2: t10.y2 };
  }, enter: function(t10) {
    return { opacity: 1, x1: t10.x1, x2: t10.x2, y1: t10.y1, y2: t10.y2 };
  }, update: function(t10) {
    return { opacity: 1, x1: t10.x1, x2: t10.x2, y1: t10.y1, y2: t10.y2 };
  }, leave: { opacity: 0 }, config: a5, immediate: !n6 });
  return (0, import_jsx_runtime3.jsx)("g", { children: o4(function(t10, e12) {
    return (0, import_react17.createElement)(X2, p2({}, e12, { key: e12.key, animatedProps: t10 }));
  }) });
});
var C4 = (0, import_react17.memo)(function(t9) {
  var e11 = t9.width, n6 = t9.height, r5 = t9.xScale, a5 = t9.yScale, o4 = t9.xValues, l3 = t9.yValues, s4 = (0, import_react17.useMemo)(function() {
    return !!r5 && T2({ width: e11, height: n6, scale: r5, axis: "x", values: o4 });
  }, [r5, o4, e11, n6]), c9 = (0, import_react17.useMemo)(function() {
    return !!a5 && T2({ width: e11, height: n6, scale: a5, axis: "y", values: l3 });
  }, [n6, e11, a5, l3]);
  return (0, import_jsx_runtime3.jsxs)(import_jsx_runtime3.Fragment, { children: [s4 && (0, import_jsx_runtime3.jsx)(Y2, { lines: s4 }), c9 && (0, import_jsx_runtime3.jsx)(Y2, { lines: c9 })] });
});
var O3 = function(t9, e11) {
  var i5, n6 = e11.axis, r5 = e11.scale, a5 = e11.x, o4 = void 0 === a5 ? 0 : a5, l3 = e11.y, s4 = void 0 === l3 ? 0 : l3, f3 = e11.length, u3 = e11.ticksPosition, d3 = e11.tickValues, x5 = e11.tickSize, m5 = void 0 === x5 ? 5 : x5, y5 = e11.tickPadding, h2 = void 0 === y5 ? 5 : y5, k7 = e11.tickRotation, g5 = void 0 === k7 ? 0 : k7, v6 = e11.format, p5 = e11.legend, P5 = e11.legendPosition, T4 = void 0 === P5 ? "end" : P5, A4 = e11.legendOffset, S5 = void 0 === A4 ? 0 : A4, W5 = e11.theme, w6 = b2({ axis: n6, scale: r5, ticksPosition: u3, tickValues: d3, tickSize: m5, tickPadding: h2, tickRotation: g5, engine: "canvas" }), B4 = w6.ticks, X4 = w6.textAlign, Y4 = w6.textBaseline;
  t9.save(), t9.translate(o4, s4), t9.textAlign = X4, t9.textBaseline = Y4;
  var C7 = W5.axis.ticks.text;
  t9.font = (C7.fontWeight ? C7.fontWeight + " " : "") + C7.fontSize + "px " + C7.fontFamily, (null != (i5 = W5.axis.domain.line.strokeWidth) ? i5 : 0) > 0 && (t9.lineWidth = Number(W5.axis.domain.line.strokeWidth), t9.lineCap = "square", W5.axis.domain.line.stroke && (t9.strokeStyle = W5.axis.domain.line.stroke), t9.beginPath(), t9.moveTo(0, 0), t9.lineTo("x" === n6 ? f3 : 0, "x" === n6 ? 0 : f3), t9.stroke());
  var O6 = "function" == typeof v6 ? v6 : function(t10) {
    return "" + t10;
  };
  if (B4.forEach(function(e12) {
    var i6;
    (null != (i6 = W5.axis.ticks.line.strokeWidth) ? i6 : 0) > 0 && (t9.lineWidth = Number(W5.axis.ticks.line.strokeWidth), t9.lineCap = "square", W5.axis.ticks.line.stroke && (t9.strokeStyle = W5.axis.ticks.line.stroke), t9.beginPath(), t9.moveTo(e12.x, e12.y), t9.lineTo(e12.x + e12.lineX, e12.y + e12.lineY), t9.stroke());
    var n7 = O6(e12.value);
    t9.save(), t9.translate(e12.x + e12.textX, e12.y + e12.textY), t9.rotate(Ht(g5)), C7.outlineWidth > 0 && (t9.strokeStyle = C7.outlineColor, t9.lineWidth = 2 * C7.outlineWidth, t9.lineJoin = "round", t9.strokeText("" + n7, 0, 0)), W5.axis.ticks.text.fill && (t9.fillStyle = C7.fill), t9.fillText("" + n7, 0, 0), t9.restore();
  }), void 0 !== p5) {
    var j5 = 0, z5 = 0, V3 = 0, D3 = "center";
    "y" === n6 ? (V3 = -90, j5 = S5, "start" === T4 ? (D3 = "start", z5 = f3) : "middle" === T4 ? (D3 = "center", z5 = f3 / 2) : "end" === T4 && (D3 = "end")) : (z5 = S5, "start" === T4 ? D3 = "start" : "middle" === T4 ? (D3 = "center", j5 = f3 / 2) : "end" === T4 && (D3 = "end", j5 = f3)), t9.translate(j5, z5), t9.rotate(Ht(V3)), t9.font = (W5.axis.legend.text.fontWeight ? W5.axis.legend.text.fontWeight + " " : "") + W5.axis.legend.text.fontSize + "px " + W5.axis.legend.text.fontFamily, W5.axis.legend.text.fill && (t9.fillStyle = W5.axis.legend.text.fill), t9.textAlign = D3, t9.textBaseline = "middle", t9.fillText(p5, 0, 0);
  }
  t9.restore();
};
var j2 = function(t9, e11) {
  var i5 = e11.xScale, n6 = e11.yScale, r5 = e11.width, a5 = e11.height, o4 = e11.top, l3 = e11.right, s4 = e11.bottom, c9 = e11.left, f3 = e11.theme, u3 = { top: o4, right: l3, bottom: s4, left: c9 };
  w2.forEach(function(e12) {
    var o5 = u3[e12];
    if (!o5) return null;
    var l4 = "top" === e12 || "bottom" === e12, s5 = "top" === e12 || "left" === e12 ? "before" : "after", c10 = l4 ? i5 : n6, d3 = P2(o5.format, c10);
    O3(t9, p2({}, o5, { axis: l4 ? "x" : "y", x: "right" === e12 ? r5 : 0, y: "bottom" === e12 ? a5 : 0, scale: c10, format: d3, length: l4 ? r5 : a5, ticksPosition: s5, theme: f3 }));
  });
};
var z3 = function(t9, e11) {
  var i5 = e11.width, n6 = e11.height, r5 = e11.scale, a5 = e11.axis, o4 = e11.values;
  T2({ width: i5, height: n6, scale: r5, axis: a5, values: o4 }).forEach(function(e12) {
    t9.beginPath(), t9.moveTo(e12.x1, e12.y1), t9.lineTo(e12.x2, e12.y2), t9.stroke();
  });
};

// node_modules/@nivo/bar/dist/nivo-bar.es.js
var import_react21 = __toESM(require_react());

// node_modules/@nivo/annotations/dist/nivo-annotations.es.js
var import_react18 = __toESM(require_react());
var import_filter2 = __toESM(require_filter());
var import_isNumber = __toESM(require_isNumber());
var import_omit = __toESM(require_omit());
var import_jsx_runtime4 = __toESM(require_jsx_runtime());
function g3() {
  return g3 = Object.assign ? Object.assign.bind() : function(t9) {
    for (var n6 = 1; n6 < arguments.length; n6++) {
      var i5 = arguments[n6];
      for (var o4 in i5) Object.prototype.hasOwnProperty.call(i5, o4) && (t9[o4] = i5[o4]);
    }
    return t9;
  }, g3.apply(this, arguments);
}
var k4 = { dotSize: 4, noteWidth: 120, noteTextOffset: 8, animate: true };
var W4 = function(n6) {
  var i5 = typeof n6;
  return (0, import_react18.isValidElement)(n6) || "string" === i5 || "function" === i5 || "object" === i5;
};
var v4 = function(t9) {
  var n6 = typeof t9;
  return "string" === n6 || "function" === n6;
};
var b3 = function(t9) {
  return "circle" === t9.type;
};
var w3 = function(t9) {
  return "dot" === t9.type;
};
var z4 = function(t9) {
  return "rect" === t9.type;
};
var P3 = function(t9) {
  var n6 = t9.data, i5 = t9.annotations, e11 = t9.getPosition, r5 = t9.getDimensions;
  return i5.reduce(function(t10, i6) {
    var s4 = i6.offset || 0;
    return [].concat(t10, (0, import_filter2.default)(n6, i6.match).map(function(t11) {
      var n7 = e11(t11), o4 = r5(t11);
      return (b3(i6) || z4(i6)) && (o4.size = o4.size + 2 * s4, o4.width = o4.width + 2 * s4, o4.height = o4.height + 2 * s4), g3({}, (0, import_omit.default)(i6, ["match", "offset"]), n7, o4, { size: i6.size || o4.size, datum: t11 });
    }));
  }, []);
};
var C5 = function(t9, n6, i5, o4) {
  var e11 = Math.atan2(o4 - n6, i5 - t9);
  return Qt(Kt(e11));
};
var O4 = function(t9) {
  var n6, i5, o4 = t9.x, a5 = t9.y, r5 = t9.noteX, s4 = t9.noteY, h2 = t9.noteWidth, d3 = void 0 === h2 ? k4.noteWidth : h2, c9 = t9.noteTextOffset, f3 = void 0 === c9 ? k4.noteTextOffset : c9;
  if ((0, import_isNumber.default)(r5)) n6 = o4 + r5;
  else {
    if (void 0 === r5.abs) throw new Error("noteX should be either a number or an object containing an 'abs' property");
    n6 = r5.abs;
  }
  if ((0, import_isNumber.default)(s4)) i5 = a5 + s4;
  else {
    if (void 0 === s4.abs) throw new Error("noteY should be either a number or an object containing an 'abs' property");
    i5 = s4.abs;
  }
  var y5 = o4, x5 = a5, m5 = C5(o4, a5, n6, i5);
  if (b3(t9)) {
    var p5 = Jt(Ht(m5), t9.size / 2);
    y5 += p5.x, x5 += p5.y;
  }
  if (z4(t9)) {
    var g5 = Math.round((m5 + 90) / 45) % 8;
    0 === g5 && (x5 -= t9.height / 2), 1 === g5 && (y5 += t9.width / 2, x5 -= t9.height / 2), 2 === g5 && (y5 += t9.width / 2), 3 === g5 && (y5 += t9.width / 2, x5 += t9.height / 2), 4 === g5 && (x5 += t9.height / 2), 5 === g5 && (y5 -= t9.width / 2, x5 += t9.height / 2), 6 === g5 && (y5 -= t9.width / 2), 7 === g5 && (y5 -= t9.width / 2, x5 -= t9.height / 2);
  }
  var W5 = n6, v6 = n6;
  return (m5 + 90) % 360 > 180 ? (W5 -= d3, v6 -= d3) : v6 += d3, { points: [[y5, x5], [n6, i5], [v6, i5]], text: [W5, i5 - f3], angle: m5 + 90 };
};
var S2 = function(t9) {
  var i5 = t9.data, o4 = t9.annotations, e11 = t9.getPosition, a5 = t9.getDimensions;
  return (0, import_react18.useMemo)(function() {
    return P3({ data: i5, annotations: o4, getPosition: e11, getDimensions: a5 });
  }, [i5, o4, e11, a5]);
};
var j3 = function(t9) {
  var i5 = t9.annotations;
  return (0, import_react18.useMemo)(function() {
    return i5.map(function(t10) {
      return g3({}, t10, { computed: O4(g3({}, t10)) });
    });
  }, [i5]);
};
var M2 = function(t9) {
  return (0, import_react18.useMemo)(function() {
    return O4(t9);
  }, [t9]);
};
var T3 = function(t9) {
  var n6 = t9.datum, o4 = t9.x, e11 = t9.y, r5 = t9.note, s4 = zt(), l3 = Ur(), u3 = l3.animate, c9 = l3.config, k7 = useSpring({ x: o4, y: e11, config: c9, immediate: !u3 });
  return "function" == typeof r5 ? (0, import_react18.createElement)(r5, { x: o4, y: e11, datum: n6 }) : (0, import_jsx_runtime4.jsxs)(import_jsx_runtime4.Fragment, { children: [s4.annotations.text.outlineWidth > 0 && (0, import_jsx_runtime4.jsx)(animated.text, { x: k7.x, y: k7.y, style: g3({}, s4.annotations.text, { strokeLinejoin: "round", strokeWidth: 2 * s4.annotations.text.outlineWidth, stroke: s4.annotations.text.outlineColor }), children: r5 }), (0, import_jsx_runtime4.jsx)(animated.text, { x: k7.x, y: k7.y, style: (0, import_omit.default)(s4.annotations.text, ["outlineWidth", "outlineColor"]), children: r5 })] });
};
var E3 = function(t9) {
  var i5 = t9.points, o4 = t9.isOutline, e11 = void 0 !== o4 && o4, a5 = zt(), r5 = (0, import_react18.useMemo)(function() {
    var t10 = i5[0];
    return i5.slice(1).reduce(function(t11, n6) {
      return t11 + " L" + n6[0] + "," + n6[1];
    }, "M" + t10[0] + "," + t10[1]);
  }, [i5]), s4 = Fr(r5);
  if (e11 && a5.annotations.link.outlineWidth <= 0) return null;
  var l3 = g3({}, a5.annotations.link);
  return e11 && (l3.strokeLinecap = "square", l3.strokeWidth = a5.annotations.link.strokeWidth + 2 * a5.annotations.link.outlineWidth, l3.stroke = a5.annotations.link.outlineColor, l3.opacity = a5.annotations.link.outlineOpacity), (0, import_jsx_runtime4.jsx)(animated.path, { fill: "none", d: s4, style: l3 });
};
var I = function(t9) {
  var n6 = t9.x, i5 = t9.y, o4 = t9.size, e11 = zt(), a5 = Ur(), r5 = a5.animate, s4 = a5.config, l3 = useSpring({ x: n6, y: i5, radius: o4 / 2, config: s4, immediate: !r5 });
  return (0, import_jsx_runtime4.jsxs)(import_jsx_runtime4.Fragment, { children: [e11.annotations.outline.outlineWidth > 0 && (0, import_jsx_runtime4.jsx)(animated.circle, { cx: l3.x, cy: l3.y, r: l3.radius, style: g3({}, e11.annotations.outline, { fill: "none", strokeWidth: e11.annotations.outline.strokeWidth + 2 * e11.annotations.outline.outlineWidth, stroke: e11.annotations.outline.outlineColor, opacity: e11.annotations.outline.outlineOpacity }) }), (0, import_jsx_runtime4.jsx)(animated.circle, { cx: l3.x, cy: l3.y, r: l3.radius, style: e11.annotations.outline })] });
};
var D2 = function(t9) {
  var n6 = t9.x, i5 = t9.y, o4 = t9.size, e11 = void 0 === o4 ? k4.dotSize : o4, a5 = zt(), r5 = Ur(), s4 = r5.animate, l3 = r5.config, u3 = useSpring({ x: n6, y: i5, radius: e11 / 2, config: l3, immediate: !s4 });
  return (0, import_jsx_runtime4.jsxs)(import_jsx_runtime4.Fragment, { children: [a5.annotations.outline.outlineWidth > 0 && (0, import_jsx_runtime4.jsx)(animated.circle, { cx: u3.x, cy: u3.y, r: u3.radius, style: g3({}, a5.annotations.outline, { fill: "none", strokeWidth: 2 * a5.annotations.outline.outlineWidth, stroke: a5.annotations.outline.outlineColor, opacity: a5.annotations.outline.outlineOpacity }) }), (0, import_jsx_runtime4.jsx)(animated.circle, { cx: u3.x, cy: u3.y, r: u3.radius, style: a5.annotations.symbol })] });
};
var L2 = function(t9) {
  var n6 = t9.x, i5 = t9.y, o4 = t9.width, e11 = t9.height, a5 = t9.borderRadius, r5 = void 0 === a5 ? 6 : a5, s4 = zt(), l3 = Ur(), u3 = l3.animate, c9 = l3.config, k7 = useSpring({ x: n6 - o4 / 2, y: i5 - e11 / 2, width: o4, height: e11, config: c9, immediate: !u3 });
  return (0, import_jsx_runtime4.jsxs)(import_jsx_runtime4.Fragment, { children: [s4.annotations.outline.outlineWidth > 0 && (0, import_jsx_runtime4.jsx)(animated.rect, { x: k7.x, y: k7.y, rx: r5, ry: r5, width: k7.width, height: k7.height, style: g3({}, s4.annotations.outline, { fill: "none", strokeWidth: s4.annotations.outline.strokeWidth + 2 * s4.annotations.outline.outlineWidth, stroke: s4.annotations.outline.outlineColor, opacity: s4.annotations.outline.outlineOpacity }) }), (0, import_jsx_runtime4.jsx)(animated.rect, { x: k7.x, y: k7.y, rx: r5, ry: r5, width: k7.width, height: k7.height, style: s4.annotations.outline })] });
};
var R = function(t9) {
  var n6 = t9.datum, i5 = t9.x, o4 = t9.y, e11 = t9.note, a5 = M2(t9);
  if (!W4(e11)) throw new Error("note should be a valid react element");
  return (0, import_jsx_runtime4.jsxs)(import_jsx_runtime4.Fragment, { children: [(0, import_jsx_runtime4.jsx)(E3, { points: a5.points, isOutline: true }), b3(t9) && (0, import_jsx_runtime4.jsx)(I, { x: i5, y: o4, size: t9.size }), w3(t9) && (0, import_jsx_runtime4.jsx)(D2, { x: i5, y: o4, size: t9.size }), z4(t9) && (0, import_jsx_runtime4.jsx)(L2, { x: i5, y: o4, width: t9.width, height: t9.height, borderRadius: t9.borderRadius }), (0, import_jsx_runtime4.jsx)(E3, { points: a5.points }), (0, import_jsx_runtime4.jsx)(T3, { datum: n6, x: a5.text[0], y: a5.text[1], note: e11 })] });
};
var q2 = function(t9, n6) {
  n6.forEach(function(n7, i5) {
    var o4 = n7[0], e11 = n7[1];
    0 === i5 ? t9.moveTo(o4, e11) : t9.lineTo(o4, e11);
  });
};
var J2 = function(t9, n6) {
  var i5 = n6.annotations, o4 = n6.theme;
  0 !== i5.length && (t9.save(), i5.forEach(function(n7) {
    if (!v4(n7.note)) throw new Error("note is invalid for canvas implementation");
    o4.annotations.link.outlineWidth > 0 && (t9.lineCap = "square", t9.strokeStyle = o4.annotations.link.outlineColor, t9.lineWidth = o4.annotations.link.strokeWidth + 2 * o4.annotations.link.outlineWidth, t9.beginPath(), q2(t9, n7.computed.points), t9.stroke(), t9.lineCap = "butt"), b3(n7) && o4.annotations.outline.outlineWidth > 0 && (t9.strokeStyle = o4.annotations.outline.outlineColor, t9.lineWidth = o4.annotations.outline.strokeWidth + 2 * o4.annotations.outline.outlineWidth, t9.beginPath(), t9.arc(n7.x, n7.y, n7.size / 2, 0, 2 * Math.PI), t9.stroke()), w3(n7) && o4.annotations.symbol.outlineWidth > 0 && (t9.strokeStyle = o4.annotations.symbol.outlineColor, t9.lineWidth = 2 * o4.annotations.symbol.outlineWidth, t9.beginPath(), t9.arc(n7.x, n7.y, n7.size / 2, 0, 2 * Math.PI), t9.stroke()), z4(n7) && o4.annotations.outline.outlineWidth > 0 && (t9.strokeStyle = o4.annotations.outline.outlineColor, t9.lineWidth = o4.annotations.outline.strokeWidth + 2 * o4.annotations.outline.outlineWidth, t9.beginPath(), t9.rect(n7.x - n7.width / 2, n7.y - n7.height / 2, n7.width, n7.height), t9.stroke()), t9.strokeStyle = o4.annotations.link.stroke, t9.lineWidth = o4.annotations.link.strokeWidth, t9.beginPath(), q2(t9, n7.computed.points), t9.stroke(), b3(n7) && (t9.strokeStyle = o4.annotations.outline.stroke, t9.lineWidth = o4.annotations.outline.strokeWidth, t9.beginPath(), t9.arc(n7.x, n7.y, n7.size / 2, 0, 2 * Math.PI), t9.stroke()), w3(n7) && (t9.fillStyle = o4.annotations.symbol.fill, t9.beginPath(), t9.arc(n7.x, n7.y, n7.size / 2, 0, 2 * Math.PI), t9.fill()), z4(n7) && (t9.strokeStyle = o4.annotations.outline.stroke, t9.lineWidth = o4.annotations.outline.strokeWidth, t9.beginPath(), t9.rect(n7.x - n7.width / 2, n7.y - n7.height / 2, n7.width, n7.height), t9.stroke()), "function" == typeof n7.note ? n7.note(t9, { datum: n7.datum, x: n7.computed.text[0], y: n7.computed.text[1], theme: o4 }) : (t9.font = o4.annotations.text.fontSize + "px " + o4.annotations.text.fontFamily, t9.textAlign = "left", t9.textBaseline = "alphabetic", t9.fillStyle = o4.annotations.text.fill, t9.strokeStyle = o4.annotations.text.outlineColor, t9.lineWidth = 2 * o4.annotations.text.outlineWidth, o4.annotations.text.outlineWidth > 0 && (t9.lineJoin = "round", t9.strokeText(n7.note, n7.computed.text[0], n7.computed.text[1]), t9.lineJoin = "miter"), t9.fillText(n7.note, n7.computed.text[0], n7.computed.text[1]));
  }), t9.restore());
};

// node_modules/@nivo/bar/dist/nivo-bar.es.js
var import_jsx_runtime6 = __toESM(require_jsx_runtime());

// node_modules/@nivo/legends/dist/nivo-legends.es.js
var import_jsx_runtime5 = __toESM(require_jsx_runtime());

// node_modules/@nivo/colors/dist/nivo-colors.es.js
var import_react19 = __toESM(require_react());
var import_get2 = __toESM(require_get());
var import_isPlainObject2 = __toESM(require_isPlainObject());
var import_prop_types2 = __toESM(require_prop_types());
function qe() {
  return qe = Object.assign ? Object.assign.bind() : function(e11) {
    for (var r5 = 1; r5 < arguments.length; r5++) {
      var n6 = arguments[r5];
      for (var t9 in n6) Object.prototype.hasOwnProperty.call(n6, t9) && (e11[t9] = n6[t9]);
    }
    return e11;
  }, qe.apply(this, arguments);
}
function Ce(e11, r5) {
  (null == r5 || r5 > e11.length) && (r5 = e11.length);
  for (var n6 = 0, t9 = new Array(r5); n6 < r5; n6++) t9[n6] = e11[n6];
  return t9;
}
function Ge(e11, r5) {
  var n6 = "undefined" != typeof Symbol && e11[Symbol.iterator] || e11["@@iterator"];
  if (n6) return (n6 = n6.call(e11)).next.bind(n6);
  if (Array.isArray(e11) || (n6 = function(e12, r6) {
    if (e12) {
      if ("string" == typeof e12) return Ce(e12, r6);
      var n7 = Object.prototype.toString.call(e12).slice(8, -1);
      return "Object" === n7 && e12.constructor && (n7 = e12.constructor.name), "Map" === n7 || "Set" === n7 ? Array.from(e12) : "Arguments" === n7 || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n7) ? Ce(e12, r6) : void 0;
    }
  }(e11)) || r5 && e11 && "number" == typeof e11.length) {
    n6 && (e11 = n6);
    var t9 = 0;
    return function() {
      return t9 >= e11.length ? { done: true } : { done: false, value: e11[t9++] };
    };
  }
  throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
var Re = { nivo: ["#e8c1a0", "#f47560", "#f1e15b", "#e8a838", "#61cdbb", "#97e3d5"], category10: category10_default, accent: Accent_default, dark2: Dark2_default, paired: Paired_default, pastel1: Pastel1_default, pastel2: Pastel2_default, set1: Set1_default, set2: Set2_default, set3: Set3_default, tableau10: Tableau10_default };
var Ve = Object.keys(Re);
var Pe = { brown_blueGreen: scheme, purpleRed_green: scheme2, pink_yellowGreen: scheme3, purple_orange: scheme4, red_blue: scheme5, red_grey: scheme6, red_yellow_blue: scheme7, red_yellow_green: scheme8, spectral: scheme9 };
var Te = Object.keys(Pe);
var Ue = { brown_blueGreen: BrBG_default, purpleRed_green: PRGn_default, pink_yellowGreen: PiYG_default, purple_orange: PuOr_default, red_blue: RdBu_default, red_grey: RdGy_default, red_yellow_blue: RdYlBu_default, red_yellow_green: RdYlGn_default, spectral: Spectral_default };
var De = { blues: scheme22, greens: scheme23, greys: scheme24, oranges: scheme27, purples: scheme25, reds: scheme26, blue_green: scheme10, blue_purple: scheme11, green_blue: scheme12, orange_red: scheme13, purple_blue_green: scheme14, purple_blue: scheme15, purple_red: scheme16, red_purple: scheme17, yellow_green_blue: scheme18, yellow_green: scheme19, yellow_orange_brown: scheme20, yellow_orange_red: scheme21 };
var Me = Object.keys(De);
var $e = { blues: Blues_default, greens: Greens_default, greys: Greys_default, oranges: Oranges_default, purples: Purples_default, reds: Reds_default, turbo: turbo_default, viridis: viridis_default, inferno, magma, plasma, cividis: cividis_default, warm, cool, cubehelixDefault: cubehelix_default2, blue_green: BuGn_default, blue_purple: BuPu_default, green_blue: GnBu_default, orange_red: OrRd_default, purple_blue_green: PuBuGn_default, purple_blue: PuBu_default, purple_red: PuRd_default, red_purple: RdPu_default, yellow_green_blue: YlGnBu_default, yellow_green: YlGn_default, yellow_orange_brown: YlOrBr_default, yellow_orange_red: YlOrRd_default };
var Be = qe({}, Re, Pe, De);
var Fe = Object.keys(Be);
var He = function(e11) {
  return Ve.includes(e11);
};
var Je = function(e11) {
  return Te.includes(e11);
};
var Ke = function(e11) {
  return Me.includes(e11);
};
var Le = { rainbow: rainbow_default, sinebow: sinebow_default };
var Ne = qe({}, Ue, $e, Le);
var Qe2 = Object.keys(Ne);
var We = function(e11, r5) {
  if ("function" == typeof e11) return e11;
  if ((0, import_isPlainObject2.default)(e11)) {
    if (function(e12) {
      return void 0 !== e12.theme;
    }(e11)) {
      if (void 0 === r5) throw new Error("Unable to use color from theme as no theme was provided");
      var n6 = (0, import_get2.default)(r5, e11.theme);
      if (void 0 === n6) throw new Error("Color from theme is undefined at path: '" + e11.theme + "'");
      return function() {
        return n6;
      };
    }
    if (function(e12) {
      return void 0 !== e12.from;
    }(e11)) {
      var t9 = function(r6) {
        return (0, import_get2.default)(r6, e11.from);
      };
      if (Array.isArray(e11.modifiers)) {
        for (var o4, i5 = [], u3 = function() {
          var e12 = o4.value, r6 = e12[0], n7 = e12[1];
          if ("brighter" === r6) i5.push(function(e13) {
            return e13.brighter(n7);
          });
          else if ("darker" === r6) i5.push(function(e13) {
            return e13.darker(n7);
          });
          else {
            if ("opacity" !== r6) throw new Error("Invalid color modifier: '" + r6 + "', must be one of: 'brighter', 'darker', 'opacity'");
            i5.push(function(e13) {
              return e13.opacity = n7, e13;
            });
          }
        }, a5 = Ge(e11.modifiers); !(o4 = a5()).done; ) u3();
        return 0 === i5.length ? t9 : function(e12) {
          return i5.reduce(function(e13, r6) {
            return r6(e13);
          }, rgb2(t9(e12))).toString();
        };
      }
      return t9;
    }
    throw new Error("Invalid color spec, you should either specify 'theme' or 'from' when using a config object");
  }
  return function() {
    return e11;
  };
};
var Xe = function(e11, r5) {
  return (0, import_react19.useMemo)(function() {
    return We(e11, r5);
  }, [e11, r5]);
};
var Ye = import_prop_types2.default.oneOfType([import_prop_types2.default.string, import_prop_types2.default.func, import_prop_types2.default.shape({ theme: import_prop_types2.default.string.isRequired }), import_prop_types2.default.shape({ from: import_prop_types2.default.string.isRequired, modifiers: import_prop_types2.default.arrayOf(import_prop_types2.default.array) })]);
var fr = function(e11, r5) {
  if ("function" == typeof e11) return e11;
  var n6 = "function" == typeof r5 ? r5 : function(e12) {
    return (0, import_get2.default)(e12, r5);
  };
  if (Array.isArray(e11)) {
    var t9 = ordinal(e11), o4 = function(e12) {
      return t9(n6(e12));
    };
    return o4.scale = t9, o4;
  }
  if ((0, import_isPlainObject2.default)(e11)) {
    if (function(e12) {
      return void 0 !== e12.datum;
    }(e11)) return function(r6) {
      return (0, import_get2.default)(r6, e11.datum);
    };
    if (function(e12) {
      return void 0 !== e12.scheme;
    }(e11)) {
      if (He(e11.scheme)) {
        var i5 = ordinal(Be[e11.scheme]), u3 = function(e12) {
          return i5(n6(e12));
        };
        return u3.scale = i5, u3;
      }
      if (Je(e11.scheme)) {
        if (void 0 !== e11.size && (e11.size < 3 || e11.size > 11)) throw new Error("Invalid size '" + e11.size + "' for diverging color scheme '" + e11.scheme + "', must be between 3~11");
        var a5 = ordinal(Be[e11.scheme][e11.size || 11]), l3 = function(e12) {
          return a5(n6(e12));
        };
        return l3.scale = a5, l3;
      }
      if (Ke(e11.scheme)) {
        if (void 0 !== e11.size && (e11.size < 3 || e11.size > 9)) throw new Error("Invalid size '" + e11.size + "' for sequential color scheme '" + e11.scheme + "', must be between 3~9");
        var c9 = ordinal(Be[e11.scheme][e11.size || 9]), s4 = function(e12) {
          return c9(n6(e12));
        };
        return s4.scale = c9, s4;
      }
    }
    throw new Error("Invalid colors, when using an object, you should either pass a 'datum' or a 'scheme' property");
  }
  return function() {
    return e11;
  };
};
var pr = function(e11, r5) {
  return (0, import_react19.useMemo)(function() {
    return fr(e11, r5);
  }, [e11, r5]);
};

// node_modules/@nivo/legends/dist/nivo-legends.es.js
var c8 = __toESM(require_react());
var import_react20 = __toESM(require_react());
var f2 = function(e11) {
  var i5 = e11.x, n6 = e11.y, o4 = e11.size, r5 = e11.fill, l3 = e11.opacity, a5 = void 0 === l3 ? 1 : l3, c9 = e11.borderWidth, d3 = void 0 === c9 ? 0 : c9, s4 = e11.borderColor;
  return (0, import_jsx_runtime5.jsx)("circle", { r: o4 / 2, cx: i5 + o4 / 2, cy: n6 + o4 / 2, fill: r5, opacity: a5, strokeWidth: d3, stroke: void 0 === s4 ? "transparent" : s4, style: { pointerEvents: "none" } });
};
var m4 = function(e11) {
  var i5 = e11.x, n6 = e11.y, o4 = e11.size, r5 = e11.fill, l3 = e11.opacity, a5 = void 0 === l3 ? 1 : l3, c9 = e11.borderWidth, d3 = void 0 === c9 ? 0 : c9, s4 = e11.borderColor;
  return (0, import_jsx_runtime5.jsx)("g", { transform: "translate(" + i5 + "," + n6 + ")", children: (0, import_jsx_runtime5.jsx)("path", { d: "\n                    M" + o4 / 2 + " 0\n                    L" + 0.8 * o4 + " " + o4 / 2 + "\n                    L" + o4 / 2 + " " + o4 + "\n                    L" + 0.2 * o4 + " " + o4 / 2 + "\n                    L" + o4 / 2 + " 0\n                ", fill: r5, opacity: a5, strokeWidth: d3, stroke: void 0 === s4 ? "transparent" : s4, style: { pointerEvents: "none" } }) });
};
var v5 = function(e11) {
  var i5 = e11.x, n6 = e11.y, o4 = e11.size, r5 = e11.fill, l3 = e11.opacity, a5 = void 0 === l3 ? 1 : l3, c9 = e11.borderWidth, d3 = void 0 === c9 ? 0 : c9, s4 = e11.borderColor;
  return (0, import_jsx_runtime5.jsx)("rect", { x: i5, y: n6, fill: r5, opacity: a5, strokeWidth: d3, stroke: void 0 === s4 ? "transparent" : s4, width: o4, height: o4, style: { pointerEvents: "none" } });
};
var u2 = function(e11) {
  var i5 = e11.x, n6 = e11.y, o4 = e11.size, r5 = e11.fill, l3 = e11.opacity, a5 = void 0 === l3 ? 1 : l3, c9 = e11.borderWidth, d3 = void 0 === c9 ? 0 : c9, s4 = e11.borderColor;
  return (0, import_jsx_runtime5.jsx)("g", { transform: "translate(" + i5 + "," + n6 + ")", children: (0, import_jsx_runtime5.jsx)("path", { d: "\n                M" + o4 / 2 + " 0\n                L" + o4 + " " + o4 + "\n                L0 " + o4 + "\n                L" + o4 / 2 + " 0\n            ", fill: r5, opacity: a5, strokeWidth: d3, stroke: void 0 === s4 ? "transparent" : s4, style: { pointerEvents: "none" } }) });
};
function p4() {
  return p4 = Object.assign ? Object.assign.bind() : function(t9) {
    for (var e11 = 1; e11 < arguments.length; e11++) {
      var i5 = arguments[e11];
      for (var n6 in i5) Object.prototype.hasOwnProperty.call(i5, n6) && (t9[n6] = i5[n6]);
    }
    return t9;
  }, p4.apply(this, arguments);
}
var k5 = { top: 0, right: 0, bottom: 0, left: 0 };
var x4 = function(t9) {
  var e11, i5 = t9.direction, n6 = t9.itemsSpacing, o4 = t9.padding, r5 = t9.itemCount, l3 = t9.itemWidth, a5 = t9.itemHeight;
  if ("number" != typeof o4 && ("object" != typeof (e11 = o4) || Array.isArray(e11) || null === e11)) throw new Error("Invalid property padding, must be one of: number, object");
  var c9 = "number" == typeof o4 ? { top: o4, right: o4, bottom: o4, left: o4 } : p4({}, k5, o4), d3 = c9.left + c9.right, s4 = c9.top + c9.bottom, h2 = l3 + d3, g5 = a5 + s4, f3 = (r5 - 1) * n6;
  return "row" === i5 ? h2 = l3 * r5 + f3 + d3 : "column" === i5 && (g5 = a5 * r5 + f3 + s4), { width: h2, height: g5, padding: c9 };
};
var b4 = function(t9) {
  var e11 = t9.anchor, i5 = t9.translateX, n6 = t9.translateY, o4 = t9.containerWidth, r5 = t9.containerHeight, l3 = t9.width, a5 = t9.height, c9 = i5, d3 = n6;
  switch (e11) {
    case "top":
      c9 += (o4 - l3) / 2;
      break;
    case "top-right":
      c9 += o4 - l3;
      break;
    case "right":
      c9 += o4 - l3, d3 += (r5 - a5) / 2;
      break;
    case "bottom-right":
      c9 += o4 - l3, d3 += r5 - a5;
      break;
    case "bottom":
      c9 += (o4 - l3) / 2, d3 += r5 - a5;
      break;
    case "bottom-left":
      d3 += r5 - a5;
      break;
    case "left":
      d3 += (r5 - a5) / 2;
      break;
    case "center":
      c9 += (o4 - l3) / 2, d3 += (r5 - a5) / 2;
  }
  return { x: c9, y: d3 };
};
var S3 = function(t9) {
  var e11, i5, n6, o4, r5, l3, a5 = t9.direction, c9 = t9.justify, d3 = t9.symbolSize, s4 = t9.symbolSpacing, h2 = t9.width, g5 = t9.height;
  switch (a5) {
    case "left-to-right":
      e11 = 0, i5 = (g5 - d3) / 2, o4 = g5 / 2, l3 = "central", c9 ? (n6 = h2, r5 = "end") : (n6 = d3 + s4, r5 = "start");
      break;
    case "right-to-left":
      e11 = h2 - d3, i5 = (g5 - d3) / 2, o4 = g5 / 2, l3 = "central", c9 ? (n6 = 0, r5 = "start") : (n6 = h2 - d3 - s4, r5 = "end");
      break;
    case "top-to-bottom":
      e11 = (h2 - d3) / 2, i5 = 0, n6 = h2 / 2, r5 = "middle", c9 ? (o4 = g5, l3 = "alphabetic") : (o4 = d3 + s4, l3 = "text-before-edge");
      break;
    case "bottom-to-top":
      e11 = (h2 - d3) / 2, i5 = g5 - d3, n6 = h2 / 2, r5 = "middle", c9 ? (o4 = 0, l3 = "text-before-edge") : (o4 = g5 - d3 - s4, l3 = "alphabetic");
  }
  return { symbolX: e11, symbolY: i5, labelX: n6, labelY: o4, labelAnchor: r5, labelAlignment: l3 };
};
var w4 = { circle: f2, diamond: m4, square: v5, triangle: u2 };
var X3 = function(i5) {
  var n6, l3, a5, d3, g5, f3, m5, v6, u3, y5, k7, x5 = i5.x, b5 = i5.y, A4 = i5.width, W5 = i5.height, z5 = i5.data, C7 = i5.direction, X4 = void 0 === C7 ? "left-to-right" : C7, Y4 = i5.justify, O6 = void 0 !== Y4 && Y4, B4 = i5.textColor, H2 = i5.background, E5 = void 0 === H2 ? "transparent" : H2, j5 = i5.opacity, L4 = void 0 === j5 ? 1 : j5, M4 = i5.symbolShape, F2 = void 0 === M4 ? "square" : M4, T4 = i5.symbolSize, P5 = void 0 === T4 ? 16 : T4, V3 = i5.symbolSpacing, R2 = void 0 === V3 ? 8 : V3, D3 = i5.symbolBorderWidth, q4 = void 0 === D3 ? 0 : D3, G = i5.symbolBorderColor, I3 = void 0 === G ? "transparent" : G, N3 = i5.onClick, _3 = i5.onMouseEnter, J4 = i5.onMouseLeave, K4 = i5.toggleSerie, Q3 = i5.effects, U2 = (0, import_react20.useState)({}), Z2 = U2[0], $2 = U2[1], tt2 = zt(), et2 = (0, import_react20.useCallback)(function(t9) {
    if (Q3) {
      var e11 = Q3.filter(function(t10) {
        return "hover" === t10.on;
      }).reduce(function(t10, e12) {
        return p4({}, t10, e12.style);
      }, {});
      $2(e11);
    }
    null == _3 || _3(z5, t9);
  }, [_3, z5, Q3]), it2 = (0, import_react20.useCallback)(function(t9) {
    if (Q3) {
      var e11 = Q3.filter(function(t10) {
        return "hover" !== t10.on;
      }).reduce(function(t10, e12) {
        return p4({}, t10, e12.style);
      }, {});
      $2(e11);
    }
    null == J4 || J4(z5, t9);
  }, [J4, z5, Q3]), nt2 = S3({ direction: X4, justify: O6, symbolSize: null != (n6 = Z2.symbolSize) ? n6 : P5, symbolSpacing: R2, width: A4, height: W5 }), ot2 = nt2.symbolX, rt2 = nt2.symbolY, lt = nt2.labelX, at2 = nt2.labelY, ct2 = nt2.labelAnchor, dt2 = nt2.labelAlignment, st = [N3, _3, J4, K4].some(function(t9) {
    return void 0 !== t9;
  }), ht2 = "function" == typeof F2 ? F2 : w4[F2];
  return (0, import_jsx_runtime5.jsxs)("g", { transform: "translate(" + x5 + "," + b5 + ")", style: { opacity: null != (l3 = Z2.itemOpacity) ? l3 : L4 }, children: [(0, import_jsx_runtime5.jsx)("rect", { width: A4, height: W5, fill: null != (a5 = Z2.itemBackground) ? a5 : E5, style: { cursor: st ? "pointer" : "auto" }, onClick: function(t9) {
    null == N3 || N3(z5, t9), null == K4 || K4(z5.id);
  }, onMouseEnter: et2, onMouseLeave: it2 }), c8.createElement(ht2, p4({ id: z5.id, x: ot2, y: rt2, size: null != (d3 = Z2.symbolSize) ? d3 : P5, fill: null != (g5 = null != (f3 = z5.fill) ? f3 : z5.color) ? g5 : "black", borderWidth: null != (m5 = Z2.symbolBorderWidth) ? m5 : q4, borderColor: null != (v6 = Z2.symbolBorderColor) ? v6 : I3 }, z5.hidden ? tt2.legends.hidden.symbol : void 0)), (0, import_jsx_runtime5.jsx)("text", { textAnchor: ct2, style: p4({}, Mt(tt2.legends.text), { fill: null != (u3 = null != (y5 = null != (k7 = Z2.itemTextColor) ? k7 : B4) ? y5 : tt2.legends.text.fill) ? u3 : "black", dominantBaseline: dt2, pointerEvents: "none", userSelect: "none" }, z5.hidden ? tt2.legends.hidden.text : void 0), x: lt, y: at2, children: z5.label })] });
};
var Y3 = function(e11) {
  var i5 = e11.data, n6 = e11.x, o4 = e11.y, r5 = e11.direction, l3 = e11.padding, a5 = void 0 === l3 ? 0 : l3, c9 = e11.justify, d3 = e11.effects, s4 = e11.itemWidth, h2 = e11.itemHeight, g5 = e11.itemDirection, f3 = void 0 === g5 ? "left-to-right" : g5, m5 = e11.itemsSpacing, v6 = void 0 === m5 ? 0 : m5, u3 = e11.itemTextColor, p5 = e11.itemBackground, y5 = void 0 === p5 ? "transparent" : p5, k7 = e11.itemOpacity, b5 = void 0 === k7 ? 1 : k7, S5 = e11.symbolShape, A4 = e11.symbolSize, W5 = e11.symbolSpacing, z5 = e11.symbolBorderWidth, C7 = e11.symbolBorderColor, w6 = e11.onClick, Y4 = e11.onMouseEnter, O6 = e11.onMouseLeave, B4 = e11.toggleSerie, H2 = x4({ itemCount: i5.length, itemWidth: s4, itemHeight: h2, itemsSpacing: v6, direction: r5, padding: a5 }).padding, E5 = "row" === r5 ? s4 + v6 : 0, j5 = "column" === r5 ? h2 + v6 : 0;
  return (0, import_jsx_runtime5.jsx)("g", { transform: "translate(" + n6 + "," + o4 + ")", children: i5.map(function(e12, i6) {
    return (0, import_jsx_runtime5.jsx)(X3, { data: e12, x: i6 * E5 + H2.left, y: i6 * j5 + H2.top, width: s4, height: h2, direction: f3, justify: c9, effects: d3, textColor: u3, background: y5, opacity: b5, symbolShape: S5, symbolSize: A4, symbolSpacing: W5, symbolBorderWidth: z5, symbolBorderColor: C7, onClick: w6, onMouseEnter: Y4, onMouseLeave: O6, toggleSerie: B4 }, i6);
  }) });
};
var O5 = function(e11) {
  var i5 = e11.data, n6 = e11.containerWidth, o4 = e11.containerHeight, r5 = e11.translateX, l3 = void 0 === r5 ? 0 : r5, a5 = e11.translateY, c9 = void 0 === a5 ? 0 : a5, d3 = e11.anchor, s4 = e11.direction, h2 = e11.padding, g5 = void 0 === h2 ? 0 : h2, f3 = e11.justify, m5 = e11.itemsSpacing, v6 = void 0 === m5 ? 0 : m5, u3 = e11.itemWidth, p5 = e11.itemHeight, y5 = e11.itemDirection, k7 = e11.itemTextColor, S5 = e11.itemBackground, A4 = e11.itemOpacity, W5 = e11.symbolShape, z5 = e11.symbolSize, C7 = e11.symbolSpacing, w6 = e11.symbolBorderWidth, X4 = e11.symbolBorderColor, O6 = e11.onClick, B4 = e11.onMouseEnter, H2 = e11.onMouseLeave, E5 = e11.toggleSerie, j5 = e11.effects, L4 = x4({ itemCount: i5.length, itemsSpacing: v6, itemWidth: u3, itemHeight: p5, direction: s4, padding: g5 }), M4 = L4.width, F2 = L4.height, T4 = b4({ anchor: d3, translateX: l3, translateY: c9, containerWidth: n6, containerHeight: o4, width: M4, height: F2 }), P5 = T4.x, V3 = T4.y;
  return (0, import_jsx_runtime5.jsx)(Y3, { data: i5, x: P5, y: V3, direction: s4, padding: g5, justify: f3, effects: j5, itemsSpacing: v6, itemWidth: u3, itemHeight: p5, itemDirection: y5, itemTextColor: k7, itemBackground: S5, itemOpacity: A4, symbolShape: W5, symbolSize: z5, symbolSpacing: C7, symbolBorderWidth: w6, symbolBorderColor: X4, onClick: O6, onMouseEnter: B4, onMouseLeave: H2, toggleSerie: "boolean" == typeof E5 ? void 0 : E5 });
};
var B3 = { start: "left", middle: "center", end: "right" };
var H = function(t9, e11) {
  var i5 = e11.data, n6 = e11.containerWidth, o4 = e11.containerHeight, r5 = e11.translateX, l3 = void 0 === r5 ? 0 : r5, a5 = e11.translateY, c9 = void 0 === a5 ? 0 : a5, d3 = e11.anchor, s4 = e11.direction, h2 = e11.padding, g5 = void 0 === h2 ? 0 : h2, f3 = e11.justify, m5 = void 0 !== f3 && f3, v6 = e11.itemsSpacing, u3 = void 0 === v6 ? 0 : v6, p5 = e11.itemWidth, y5 = e11.itemHeight, k7 = e11.itemDirection, A4 = void 0 === k7 ? "left-to-right" : k7, W5 = e11.itemTextColor, z5 = e11.symbolSize, C7 = void 0 === z5 ? 16 : z5, w6 = e11.symbolSpacing, X4 = void 0 === w6 ? 8 : w6, Y4 = e11.theme, O6 = x4({ itemCount: i5.length, itemWidth: p5, itemHeight: y5, itemsSpacing: u3, direction: s4, padding: g5 }), H2 = O6.width, E5 = O6.height, j5 = O6.padding, L4 = b4({ anchor: d3, translateX: l3, translateY: c9, containerWidth: n6, containerHeight: o4, width: H2, height: E5 }), M4 = L4.x, F2 = L4.y, T4 = "row" === s4 ? p5 + u3 : 0, P5 = "column" === s4 ? y5 + u3 : 0;
  t9.save(), t9.translate(M4, F2), t9.font = Y4.legends.text.fontSize + "px " + (Y4.legends.text.fontFamily || "sans-serif"), i5.forEach(function(e12, i6) {
    var n7, o5, r6 = i6 * T4 + j5.left, l4 = i6 * P5 + j5.top, a6 = S3({ direction: A4, justify: m5, symbolSize: C7, symbolSpacing: X4, width: p5, height: y5 }), c10 = a6.symbolX, d4 = a6.symbolY, s5 = a6.labelX, h3 = a6.labelY, g6 = a6.labelAnchor, f4 = a6.labelAlignment;
    t9.fillStyle = null != (n7 = e12.color) ? n7 : "black", t9.fillRect(r6 + c10, l4 + d4, C7, C7), t9.textAlign = B3[g6], "central" === f4 && (t9.textBaseline = "middle"), t9.fillStyle = null != (o5 = null != W5 ? W5 : Y4.legends.text.fill) ? o5 : "black", t9.fillText(String(e12.label), r6 + s5, l4 + h3);
  }), t9.restore();
};

// node_modules/@nivo/bar/dist/nivo-bar.es.js
var import_uniqBy2 = __toESM(require_uniqBy());
function j4() {
  return j4 = Object.assign ? Object.assign.bind() : function(e11) {
    for (var t9 = 1; t9 < arguments.length; t9++) {
      var a5 = arguments[t9];
      for (var i5 in a5) Object.prototype.hasOwnProperty.call(a5, i5) && (e11[i5] = a5[i5]);
    }
    return e11;
  }, j4.apply(this, arguments);
}
function q3(e11, t9) {
  if (null == e11) return {};
  var a5, i5, n6 = {}, l3 = Object.keys(e11);
  for (i5 = 0; i5 < l3.length; i5++) a5 = l3[i5], t9.indexOf(a5) >= 0 || (n6[a5] = e11[a5]);
  return n6;
}
var K3;
var _2 = function(e11) {
  var t9 = e11.bars, a5 = e11.annotations, i5 = S2({ data: t9, annotations: a5, getPosition: function(e12) {
    return { x: e12.x + e12.width / 2, y: e12.y + e12.height / 2 };
  }, getDimensions: function(e12) {
    var t10 = e12.height, a6 = e12.width;
    return { width: a6, height: t10, size: Math.max(a6, t10) };
  } });
  return (0, import_jsx_runtime6.jsx)(import_jsx_runtime6.Fragment, { children: i5.map(function(e12, t10) {
    return (0, import_jsx_runtime6.jsx)(R, j4({}, e12), t10);
  }) });
};
var J3 = function(e11) {
  var t9 = e11.width, a5 = e11.height, i5 = e11.legends, n6 = e11.toggleSerie;
  return (0, import_jsx_runtime6.jsx)(import_jsx_runtime6.Fragment, { children: i5.map(function(e12, i6) {
    var l3, r5 = e12[0], o4 = e12[1];
    return (0, import_jsx_runtime6.jsx)(O5, j4({}, r5, { containerWidth: t9, containerHeight: a5, data: null != (l3 = r5.data) ? l3 : o4, toggleSerie: r5.toggleSerie && "keys" === r5.dataFrom ? n6 : void 0 }), i6);
  }) });
};
var Q2 = ["data"];
var U = function(e11) {
  var t9, a5 = e11.bar, i5 = a5.data, l3 = q3(a5, Q2), r5 = e11.style, o4 = r5.borderColor, d3 = r5.color, u3 = r5.height, c9 = r5.labelColor, s4 = r5.labelOpacity, h2 = r5.labelX, f3 = r5.labelY, b5 = r5.transform, v6 = r5.width, g5 = r5.textAnchor, x5 = e11.borderRadius, w6 = e11.borderWidth, L4 = e11.label, C7 = e11.shouldRenderLabel, V3 = e11.isInteractive, M4 = e11.onClick, W5 = e11.onMouseEnter, T4 = e11.onMouseLeave, B4 = e11.tooltip, O6 = e11.isFocusable, P5 = e11.ariaLabel, H2 = e11.ariaLabelledBy, R2 = e11.ariaDescribedBy, F2 = e11.ariaDisabled, D3 = e11.ariaHidden, X4 = zt(), Y4 = k(), G = Y4.showTooltipFromEvent, z5 = Y4.showTooltipAt, N3 = Y4.hideTooltip, K4 = (0, import_react21.useMemo)(function() {
    return function() {
      return (0, import_react21.createElement)(B4, j4({}, l3, i5));
    };
  }, [B4, l3, i5]), _3 = (0, import_react21.useCallback)(function(e12) {
    null == M4 || M4(j4({ color: l3.color }, i5), e12);
  }, [l3, i5, M4]), J4 = (0, import_react21.useCallback)(function(e12) {
    return G(K4(), e12);
  }, [G, K4]), U2 = (0, import_react21.useCallback)(function(e12) {
    null == W5 || W5(i5, e12), G(K4(), e12);
  }, [i5, W5, G, K4]), Z2 = (0, import_react21.useCallback)(function(e12) {
    null == T4 || T4(i5, e12), N3();
  }, [i5, N3, T4]), $2 = (0, import_react21.useCallback)(function() {
    z5(K4(), [l3.absX + l3.width / 2, l3.absY]);
  }, [z5, K4, l3]), ee2 = (0, import_react21.useCallback)(function() {
    N3();
  }, [N3]);
  return (0, import_jsx_runtime6.jsxs)(animated.g, { transform: b5, children: [(0, import_jsx_runtime6.jsx)(animated.rect, { width: to2(v6, function(e12) {
    return Math.max(e12, 0);
  }), height: to2(u3, function(e12) {
    return Math.max(e12, 0);
  }), rx: x5, ry: x5, fill: null != (t9 = i5.fill) ? t9 : d3, strokeWidth: w6, stroke: o4, focusable: O6, tabIndex: O6 ? 0 : void 0, "aria-label": P5 ? P5(i5) : void 0, "aria-labelledby": H2 ? H2(i5) : void 0, "aria-describedby": R2 ? R2(i5) : void 0, "aria-disabled": F2 ? F2(i5) : void 0, "aria-hidden": D3 ? D3(i5) : void 0, onMouseEnter: V3 ? U2 : void 0, onMouseMove: V3 ? J4 : void 0, onMouseLeave: V3 ? Z2 : void 0, onClick: V3 ? _3 : void 0, onFocus: V3 && O6 ? $2 : void 0, onBlur: V3 && O6 ? ee2 : void 0 }), C7 && (0, import_jsx_runtime6.jsx)(animated.text, { x: h2, y: f3, textAnchor: g5, dominantBaseline: "central", fillOpacity: s4, style: j4({}, X4.labels.text, { pointerEvents: "none", fill: c9 }), children: L4 })] });
};
var Z = ["color", "label"];
var $ = function(e11) {
  var t9 = e11.color, a5 = e11.label, i5 = q3(e11, Z);
  return (0, import_jsx_runtime6.jsx)(w, { id: a5, value: i5.formattedValue, enableChip: true, color: t9 });
};
var ee = { indexBy: "id", keys: ["value"], groupMode: "stacked", layout: "vertical", reverse: false, minValue: "auto", maxValue: "auto", valueScale: { type: "linear" }, indexScale: { type: "band", round: true }, padding: 0.1, innerPadding: 0, axisBottom: {}, axisLeft: {}, enableGridX: false, enableGridY: true, enableLabel: true, label: "formattedValue", labelPosition: "middle", labelOffset: 0, labelSkipWidth: 0, labelSkipHeight: 0, labelTextColor: { from: "theme", theme: "labels.text.fill" }, colorBy: "id", colors: { scheme: "nivo" }, borderRadius: 0, borderWidth: 0, borderColor: { from: "color" }, isInteractive: true, tooltip: $, tooltipLabel: function(e11) {
  return e11.id + " - " + e11.indexValue;
}, legends: [], initialHiddenIds: [], annotations: [], markers: [], enableTotals: false, totalsOffset: 10 };
var te = j4({}, ee, { layers: ["grid", "axes", "bars", "totals", "markers", "legends", "annotations"], barComponent: U, defs: [], fill: [], animate: true, motionConfig: "default", role: "img", isFocusable: false });
var ae = j4({}, ee, { layers: ["grid", "axes", "bars", "totals", "legends", "annotations"], pixelRatio: "undefined" != typeof window && null != (K3 = window.devicePixelRatio) ? K3 : 1 });
var ie = function(e11, t9, a5, i5, n6, l3) {
  return cn2(i5, { all: e11.map(t9), min: 0, max: 0 }, n6, l3).padding(a5);
};
var ne = function(e11, t9) {
  return e11.map(function(e12) {
    return j4({}, t9.reduce(function(e13, t10) {
      return e13[t10] = null, e13;
    }, {}), e12);
  });
};
var le = function(e11) {
  return Object.keys(e11).reduce(function(t9, a5) {
    return e11[a5] && (t9[a5] = e11[a5]), t9;
  }, {});
};
var re2 = function(e11) {
  return [e11, Number(e11)];
};
function oe(e11, t9, a5, i5) {
  return void 0 === e11 && (e11 = ee.layout), void 0 === t9 && (t9 = ee.reverse), void 0 === a5 && (a5 = ee.labelPosition), void 0 === i5 && (i5 = ee.labelOffset), function(n6, l3) {
    var r5 = i5 * (t9 ? -1 : 1);
    if ("horizontal" === e11) {
      var o4 = n6 / 2;
      return "start" === a5 ? o4 = t9 ? n6 : 0 : "end" === a5 && (o4 = t9 ? 0 : n6), { labelX: o4 + r5, labelY: l3 / 2, textAnchor: "middle" === a5 ? "middle" : t9 ? "end" : "start" };
    }
    var d3 = l3 / 2;
    return "start" === a5 ? d3 = t9 ? 0 : l3 : "end" === a5 && (d3 = t9 ? l3 : 0), { labelX: n6 / 2, labelY: d3 - r5, textAnchor: "middle" };
  };
}
var de = ["layout", "minValue", "maxValue", "reverse", "width", "height", "padding", "innerPadding", "valueScale", "indexScale", "hiddenIds"];
var ue = function(e11, t9) {
  return e11 > t9;
};
var ce = function(e11, t9) {
  return e11 < t9;
};
var se = function(e11, t9) {
  return Array.from(" ".repeat(t9 - e11), function(t10, a5) {
    return e11 + a5;
  });
};
var he = function(e11) {
  return ue(e11, 0) ? 0 : e11;
};
var fe = function(e11, t9, a5, i5) {
  var n6 = e11.data, l3 = e11.formatValue, r5 = e11.getColor, o4 = e11.getIndex, d3 = e11.getTooltipLabel, u3 = e11.innerPadding, c9 = void 0 === u3 ? 0 : u3, s4 = e11.keys, h2 = e11.xScale, f3 = e11.yScale, b5 = e11.margin, v6 = a5 ? ce : ue, g5 = n6.map(le), m5 = [];
  return s4.forEach(function(e12, a6) {
    return se(0, h2.domain().length).forEach(function(u4) {
      var s5, p5, x5, y5 = re2(n6[u4][e12]), S5 = y5[0], k7 = y5[1], w6 = o4(n6[u4]), L4 = (null != (s5 = h2(w6)) ? s5 : 0) + t9 * a6 + c9 * a6, C7 = v6(p5 = k7, 0) ? null != (x5 = f3(p5)) ? x5 : 0 : i5, V3 = function(e13, t10) {
        var a7;
        return v6(e13, 0) ? i5 - t10 : (null != (a7 = f3(e13)) ? a7 : 0) - i5;
      }(k7, C7), M4 = { id: e12, value: null === S5 ? S5 : k7, formattedValue: l3(k7), hidden: false, index: u4, indexValue: w6, data: g5[u4] };
      m5.push({ key: e12 + "." + M4.indexValue, index: m5.length, data: M4, x: L4, y: C7, absX: b5.left + L4, absY: b5.top + C7, width: t9, height: V3, color: r5(M4), label: d3(M4) });
    });
  }), m5;
};
var be = function(e11, t9, a5, i5) {
  var n6 = e11.data, l3 = e11.formatValue, r5 = e11.getIndex, o4 = e11.getColor, d3 = e11.getTooltipLabel, u3 = e11.keys, c9 = e11.innerPadding, s4 = void 0 === c9 ? 0 : c9, h2 = e11.xScale, f3 = e11.yScale, b5 = e11.margin, v6 = a5 ? ce : ue, g5 = n6.map(le), m5 = [];
  return u3.forEach(function(e12, a6) {
    return se(0, f3.domain().length).forEach(function(u4) {
      var c10, p5, x5, y5 = re2(n6[u4][e12]), S5 = y5[0], k7 = y5[1], w6 = r5(n6[u4]), L4 = v6(p5 = k7, 0) ? i5 : null != (x5 = h2(p5)) ? x5 : 0, C7 = (null != (c10 = f3(w6)) ? c10 : 0) + t9 * a6 + s4 * a6, V3 = function(e13, t10) {
        var a7;
        return v6(e13, 0) ? (null != (a7 = h2(e13)) ? a7 : 0) - i5 : i5 - t10;
      }(k7, L4), M4 = { id: e12, value: null === S5 ? S5 : k7, formattedValue: l3(k7), hidden: false, index: u4, indexValue: w6, data: g5[u4] };
      m5.push({ key: e12 + "." + M4.indexValue, index: m5.length, data: M4, x: L4, y: C7, absX: b5.left + L4, absY: b5.top + C7, width: V3, height: t9, color: o4(M4), label: d3(M4) });
    });
  }), m5;
};
var ve = function(e11) {
  var t9, a5, i5 = e11.layout, n6 = e11.minValue, l3 = e11.maxValue, r5 = e11.reverse, o4 = e11.width, d3 = e11.height, u3 = e11.padding, c9 = void 0 === u3 ? 0 : u3, s4 = e11.innerPadding, h2 = void 0 === s4 ? 0 : s4, f3 = e11.valueScale, b5 = e11.indexScale, v6 = e11.hiddenIds, g5 = void 0 === v6 ? [] : v6, m5 = q3(e11, de), p5 = m5.keys.filter(function(e12) {
    return !g5.includes(e12);
  }), x5 = ne(m5.data, p5), y5 = "vertical" === i5 ? ["y", "x", o4] : ["x", "y", d3], S5 = y5[0], k7 = y5[1], w6 = y5[2], L4 = ie(x5, m5.getIndex, c9, b5, w6, k7), C7 = j4({ max: l3, min: n6, reverse: r5 }, f3), V3 = "auto" === C7.min ? he : function(e12) {
    return e12;
  }, M4 = x5.reduce(function(e12, t10) {
    return [].concat(e12, p5.map(function(e13) {
      return t10[e13];
    }));
  }, []).filter(Boolean), W5 = V3(Math.min.apply(Math, M4)), T4 = (a5 = Math.max.apply(Math, M4), isFinite(a5) ? a5 : 0), B4 = cn2(C7, { all: M4, min: W5, max: T4 }, "x" === S5 ? o4 : d3, S5), O6 = "vertical" === i5 ? [L4, B4] : [B4, L4], I3 = O6[0], P5 = O6[1], E5 = (L4.bandwidth() - h2 * (p5.length - 1)) / p5.length, H2 = [j4({}, m5, { data: x5, keys: p5, innerPadding: h2, xScale: I3, yScale: P5 }), E5, C7.reverse, null != (t9 = B4(0)) ? t9 : 0];
  return { xScale: I3, yScale: P5, bars: E5 > 0 ? "vertical" === i5 ? fe.apply(void 0, H2) : be.apply(void 0, H2) : [] };
};
var ge = ["data", "layout", "minValue", "maxValue", "reverse", "width", "height", "padding", "valueScale", "indexScale", "hiddenIds"];
var me = function e9(t9) {
  var a5;
  return t9.some(Array.isArray) ? e9((a5 = []).concat.apply(a5, t9)) : t9;
};
var pe = function(e11, t9, a5) {
  var i5 = e11.formatValue, n6 = e11.getColor, l3 = e11.getIndex, r5 = e11.getTooltipLabel, o4 = e11.innerPadding, d3 = e11.stackedData, u3 = e11.xScale, c9 = e11.yScale, s4 = e11.margin, h2 = [];
  return d3.forEach(function(e12) {
    return u3.domain().forEach(function(d4, f3) {
      var b5, v6, g5 = e12[f3], m5 = null != (b5 = u3(l3(g5.data))) ? b5 : 0, p5 = (null != (v6 = function(e13) {
        return c9(e13[a5 ? 0 : 1]);
      }(g5)) ? v6 : 0) + 0.5 * o4, x5 = function(e13, t10) {
        var i6;
        return (null != (i6 = c9(e13[a5 ? 1 : 0])) ? i6 : 0) - t10;
      }(g5, p5) - o4, y5 = re2(g5.data[e12.key]), S5 = y5[0], k7 = y5[1], w6 = { id: e12.key, value: null === S5 ? S5 : k7, formattedValue: i5(k7), hidden: false, index: f3, indexValue: d4, data: le(g5.data) };
      h2.push({ key: e12.key + "." + d4, index: h2.length, data: w6, x: m5, y: p5, absX: s4.left + m5, absY: s4.top + p5, width: t9, height: x5, color: n6(w6), label: r5(w6) });
    });
  }), h2;
};
var xe = function(e11, t9, a5) {
  var i5 = e11.formatValue, n6 = e11.getColor, l3 = e11.getIndex, r5 = e11.getTooltipLabel, o4 = e11.innerPadding, d3 = e11.stackedData, u3 = e11.xScale, c9 = e11.yScale, s4 = e11.margin, h2 = [];
  return d3.forEach(function(e12) {
    return c9.domain().forEach(function(d4, f3) {
      var b5, v6, g5 = e12[f3], m5 = null != (b5 = c9(l3(g5.data))) ? b5 : 0, p5 = (null != (v6 = function(e13) {
        return u3(e13[a5 ? 1 : 0]);
      }(g5)) ? v6 : 0) + 0.5 * o4, x5 = function(e13, t10) {
        var i6;
        return (null != (i6 = u3(e13[a5 ? 0 : 1])) ? i6 : 0) - t10;
      }(g5, p5) - o4, y5 = re2(g5.data[e12.key]), S5 = y5[0], k7 = y5[1], w6 = { id: e12.key, value: null === S5 ? S5 : k7, formattedValue: i5(k7), hidden: false, index: f3, indexValue: d4, data: le(g5.data) };
      h2.push({ key: e12.key + "." + d4, index: h2.length, data: w6, x: p5, y: m5, absX: s4.left + p5, absY: s4.top + m5, width: x5, height: t9, color: n6(w6), label: r5(w6) });
    });
  }), h2;
};
var ye = function(e11) {
  var t9, a5 = e11.data, i5 = e11.layout, n6 = e11.minValue, l3 = e11.maxValue, r5 = e11.reverse, o4 = e11.width, d3 = e11.height, u3 = e11.padding, c9 = void 0 === u3 ? 0 : u3, s4 = e11.valueScale, h2 = e11.indexScale, f3 = e11.hiddenIds, b5 = void 0 === f3 ? [] : f3, v6 = q3(e11, ge), g5 = v6.keys.filter(function(e12) {
    return !b5.includes(e12);
  }), m5 = stack_default().keys(g5).offset(diverging_default)(ne(a5, g5)), p5 = "vertical" === i5 ? ["y", "x", o4] : ["x", "y", d3], x5 = p5[0], y5 = p5[1], S5 = p5[2], k7 = ie(a5, v6.getIndex, c9, h2, S5, y5), w6 = j4({ max: l3, min: n6, reverse: r5 }, s4), L4 = (t9 = me(m5), "log" === s4.type ? t9.filter(function(e12) {
    return 0 !== e12;
  }) : t9), C7 = Math.min.apply(Math, L4), V3 = Math.max.apply(Math, L4), M4 = cn2(w6, { all: L4, min: C7, max: V3 }, "x" === x5 ? o4 : d3, x5), W5 = "vertical" === i5 ? [k7, M4] : [M4, k7], T4 = W5[0], B4 = W5[1], O6 = v6.innerPadding > 0 ? v6.innerPadding : 0, I3 = k7.bandwidth(), P5 = [j4({}, v6, { innerPadding: O6, stackedData: m5, xScale: T4, yScale: B4 }), I3, w6.reverse];
  return { xScale: T4, yScale: B4, bars: I3 > 0 ? "vertical" === i5 ? pe.apply(void 0, P5) : xe.apply(void 0, P5) : [] };
};
var Se = function(e11) {
  var t9 = e11.bars, a5 = e11.direction, i5 = e11.from, n6 = e11.groupMode, r5 = e11.layout, o4 = e11.legendLabel, d3 = e11.reverse, u3 = Cn(null != o4 ? o4 : "indexes" === i5 ? "indexValue" : "id");
  return "indexes" === i5 ? function(e12, t10, a6) {
    var i6 = (0, import_uniqBy2.default)(e12.map(function(e13) {
      var t11, i7;
      return { id: null != (t11 = e13.data.indexValue) ? t11 : "", label: a6(e13.data), hidden: e13.data.hidden, color: null != (i7 = e13.color) ? i7 : "#000" };
    }), function(e13) {
      return e13.id;
    });
    return "horizontal" === t10 && i6.reverse(), i6;
  }(t9, r5, u3) : function(e12, t10, a6, i6, n7, l3) {
    var r6 = (0, import_uniqBy2.default)(e12.map(function(e13) {
      var t11;
      return { id: e13.data.id, label: l3(e13.data), hidden: e13.data.hidden, color: null != (t11 = e13.color) ? t11 : "#000" };
    }), function(e13) {
      return e13.id;
    });
    return ("vertical" === t10 && "stacked" === i6 && "column" === a6 && true !== n7 || "horizontal" === t10 && "stacked" === i6 && true === n7) && r6.reverse(), r6;
  }(t9, r5, a5, n6, d3, u3);
};
var ke2 = function(e11, t9, a5) {
  var i5 = e11.get(t9) || 0;
  e11.set(t9, i5 + a5);
};
var we2 = function(e11, t9, a5) {
  var i5 = e11.get(t9) || 0;
  e11.set(t9, i5 + (a5 > 0 ? a5 : 0));
};
var Le2 = function(e11, t9, a5) {
  var i5 = e11.get(t9) || 0;
  e11.set(t9, Math.max(i5, Number(a5)));
};
var Ce2 = function(e11, t9) {
  var a5 = e11.get(t9) || 0;
  e11.set(t9, a5 + 1);
};
var Ve2 = function(e11) {
  var t9 = e11.indexBy, a5 = void 0 === t9 ? ee.indexBy : t9, i5 = e11.keys, l3 = void 0 === i5 ? ee.keys : i5, d3 = e11.label, u3 = void 0 === d3 ? ee.label : d3, c9 = e11.tooltipLabel, s4 = void 0 === c9 ? ee.tooltipLabel : c9, h2 = e11.valueFormat, f3 = e11.colors, b5 = void 0 === f3 ? ee.colors : f3, v6 = e11.colorBy, g5 = void 0 === v6 ? ee.colorBy : v6, m5 = e11.borderColor, p5 = void 0 === m5 ? ee.borderColor : m5, x5 = e11.labelTextColor, S5 = void 0 === x5 ? ee.labelTextColor : x5, L4 = e11.groupMode, C7 = void 0 === L4 ? ee.groupMode : L4, V3 = e11.layout, M4 = void 0 === V3 ? ee.layout : V3, W5 = e11.reverse, T4 = void 0 === W5 ? ee.reverse : W5, B4 = e11.data, O6 = e11.minValue, I3 = void 0 === O6 ? ee.minValue : O6, P5 = e11.maxValue, E5 = void 0 === P5 ? ee.maxValue : P5, H2 = e11.margin, R2 = e11.width, A4 = e11.height, X4 = e11.padding, Y4 = void 0 === X4 ? ee.padding : X4, G = e11.innerPadding, z5 = void 0 === G ? ee.innerPadding : G, N3 = e11.valueScale, q4 = void 0 === N3 ? ee.valueScale : N3, K4 = e11.indexScale, _3 = void 0 === K4 ? ee.indexScale : K4, J4 = e11.initialHiddenIds, Q3 = void 0 === J4 ? ee.initialHiddenIds : J4, U2 = e11.enableLabel, Z2 = void 0 === U2 ? ee.enableLabel : U2, $2 = e11.labelSkipWidth, te2 = void 0 === $2 ? ee.labelSkipWidth : $2, ae2 = e11.labelSkipHeight, ie2 = void 0 === ae2 ? ee.labelSkipHeight : ae2, ne2 = e11.legends, le2 = void 0 === ne2 ? ee.legends : ne2, re3 = e11.legendLabel, oe2 = e11.totalsOffset, de2 = void 0 === oe2 ? ee.totalsOffset : oe2, ue2 = (0, import_react21.useState)(null != Q3 ? Q3 : []), ce2 = ue2[0], se2 = ue2[1], he2 = (0, import_react21.useCallback)(function(e12) {
    se2(function(t10) {
      return t10.indexOf(e12) > -1 ? t10.filter(function(t11) {
        return t11 !== e12;
      }) : [].concat(t10, [e12]);
    });
  }, []), fe2 = Wn(a5), be2 = Wn(u3), ge2 = Wn(s4), me2 = Ot(h2), pe2 = zt(), xe2 = pr(b5, g5), Ve3 = Xe(p5, pe2), Me3 = Xe(S5, pe2), We3 = ("grouped" === C7 ? ve : ye)({ layout: M4, reverse: T4, data: B4, getIndex: fe2, keys: l3, minValue: I3, maxValue: E5, width: R2, height: A4, getColor: xe2, padding: Y4, innerPadding: z5, valueScale: q4, indexScale: _3, hiddenIds: ce2, formatValue: me2, getTooltipLabel: ge2, margin: H2 }), Te3 = We3.bars, Be3 = We3.xScale, Oe3 = We3.yScale, Ie2 = (0, import_react21.useMemo)(function() {
    return Te3.filter(function(e12) {
      return null !== e12.data.value;
    }).map(function(e12, t10) {
      return j4({}, e12, { index: t10 });
    });
  }, [Te3]), Pe3 = (0, import_react21.useCallback)(function(e12) {
    var t10 = e12.width, a6 = e12.height;
    return !!Z2 && (!(te2 > 0 && t10 < te2) && !(ie2 > 0 && a6 < ie2));
  }, [Z2, te2, ie2]), Ee2 = (0, import_react21.useMemo)(function() {
    return l3.map(function(e12) {
      var t10 = Te3.find(function(t11) {
        return t11.data.id === e12;
      });
      return j4({}, t10, { data: j4({ id: e12 }, null == t10 ? void 0 : t10.data, { hidden: ce2.includes(e12) }) });
    });
  }, [ce2, l3, Te3]), He3 = (0, import_react21.useMemo)(function() {
    return le2.map(function(e12) {
      return [e12, Se({ bars: "keys" === e12.dataFrom ? Ee2 : Te3, direction: e12.direction, from: e12.dataFrom, groupMode: C7, layout: M4, legendLabel: re3, reverse: T4 })];
    });
  }, [le2, Ee2, Te3, C7, M4, re3, T4]), Re3 = (0, import_react21.useMemo)(function() {
    return function(e12, t10, a6, i6, n6, l4, r5) {
      void 0 === i6 && (i6 = ee.layout), void 0 === n6 && (n6 = ee.groupMode);
      var o4 = [];
      if (0 === e12.length) return o4;
      var d4 = /* @__PURE__ */ new Map(), u4 = e12[0].width, c10 = e12[0].height;
      if ("stacked" === n6) {
        var s5 = /* @__PURE__ */ new Map();
        e12.forEach(function(e13) {
          var t11 = e13.data, a7 = t11.indexValue, i7 = t11.value;
          ke2(d4, a7, Number(i7)), we2(s5, a7, Number(i7));
        }), s5.forEach(function(e13, n7) {
          var s6, h4, f5, b6 = d4.get(n7) || 0;
          "vertical" === i6 ? (s6 = t10(n7), h4 = a6(e13), f5 = a6(e13 / 2)) : (s6 = t10(e13), h4 = a6(n7), f5 = t10(e13 / 2)), s6 += "vertical" === i6 ? u4 / 2 : l4, h4 += "vertical" === i6 ? -l4 : c10 / 2, o4.push({ key: "total_" + n7, x: s6, y: h4, value: b6, formattedValue: r5(b6), animationOffset: f5 });
        });
      } else if ("grouped" === n6) {
        var h3 = /* @__PURE__ */ new Map(), f4 = /* @__PURE__ */ new Map();
        e12.forEach(function(e13) {
          var t11 = e13.data, a7 = t11.indexValue, i7 = t11.value;
          ke2(d4, a7, Number(i7)), Le2(h3, a7, Number(i7)), Ce2(f4, a7);
        }), h3.forEach(function(e13, n7) {
          var s6, h4, b6, v7 = d4.get(n7) || 0, g6 = f4.get(n7);
          "vertical" === i6 ? (s6 = t10(n7), h4 = a6(e13), b6 = a6(e13 / 2)) : (s6 = t10(e13), h4 = a6(n7), b6 = t10(e13 / 2)), s6 += "vertical" === i6 ? g6 * u4 / 2 : l4, h4 += "vertical" === i6 ? -l4 : g6 * c10 / 2, o4.push({ key: "total_" + n7, x: s6, y: h4, value: v7, formattedValue: r5(v7), animationOffset: b6 });
        });
      }
      return o4;
    }(Te3, Be3, Oe3, M4, C7, de2, me2);
  }, [Te3, Be3, Oe3, M4, C7, de2, me2]);
  return { bars: Te3, barsWithValue: Ie2, xScale: Be3, yScale: Oe3, getIndex: fe2, getLabel: be2, getTooltipLabel: ge2, formatValue: me2, getColor: xe2, getBorderColor: Ve3, getLabelColor: Me3, shouldRenderBarLabel: Pe3, hiddenIds: ce2, toggleSerie: he2, legendsWithData: He3, barTotals: Re3 };
};
var Me2 = function(e11) {
  var t9 = e11.data, a5 = e11.springConfig, i5 = e11.animate, l3 = e11.layout, r5 = void 0 === l3 ? te.layout : l3, o4 = zt();
  return useTransition(t9, { keys: function(e12) {
    return e12.key;
  }, from: function(e12) {
    return { x: "vertical" === r5 ? e12.x : e12.animationOffset, y: "vertical" === r5 ? e12.animationOffset : e12.y, labelOpacity: 0 };
  }, enter: function(e12) {
    return { x: e12.x, y: e12.y, labelOpacity: 1 };
  }, update: function(e12) {
    return { x: e12.x, y: e12.y, labelOpacity: 1 };
  }, leave: function(e12) {
    return { x: "vertical" === r5 ? e12.x : e12.animationOffset, y: "vertical" === r5 ? e12.animationOffset : e12.y, labelOpacity: 0 };
  }, config: a5, immediate: !i5, initial: i5 ? void 0 : null })(function(e12, t10) {
    return (0, import_jsx_runtime6.jsx)(animated.text, { x: e12.x, y: e12.y, fillOpacity: e12.labelOpacity, style: j4({}, o4.labels.text, { pointerEvents: "none", fill: o4.text.fill }), fontWeight: "bold", fontSize: o4.labels.text.fontSize, fontFamily: o4.labels.text.fontFamily, textAnchor: "vertical" === r5 ? "middle" : "start", alignmentBaseline: "vertical" === r5 ? "alphabetic" : "middle", children: t10.formattedValue }, t10.key);
  });
};
var We2 = ["isInteractive", "animate", "motionConfig", "theme", "renderWrapper"];
var Te2 = function(a5) {
  var i5 = a5.data, n6 = a5.indexBy, l3 = a5.keys, r5 = a5.margin, o4 = a5.width, d3 = a5.height, b5 = a5.groupMode, v6 = a5.layout, g5 = a5.reverse, m5 = a5.minValue, p5 = a5.maxValue, k7 = a5.valueScale, w6 = a5.indexScale, C7 = a5.padding, V3 = a5.innerPadding, M4 = a5.axisTop, W5 = a5.axisRight, T4 = a5.axisBottom, B4 = void 0 === T4 ? te.axisBottom : T4, O6 = a5.axisLeft, P5 = void 0 === O6 ? te.axisLeft : O6, E5 = a5.enableGridX, H2 = void 0 === E5 ? te.enableGridX : E5, R2 = a5.enableGridY, F2 = void 0 === R2 ? te.enableGridY : R2, D3 = a5.gridXValues, A4 = a5.gridYValues, X4 = a5.layers, Y4 = void 0 === X4 ? te.layers : X4, G = a5.barComponent, z5 = void 0 === G ? te.barComponent : G, N3 = a5.enableLabel, q4 = void 0 === N3 ? te.enableLabel : N3, K4 = a5.label, Q3 = a5.labelSkipWidth, U2 = void 0 === Q3 ? te.labelSkipWidth : Q3, Z2 = a5.labelSkipHeight, $2 = void 0 === Z2 ? te.labelSkipHeight : Z2, ee2 = a5.labelTextColor, ae2 = a5.labelPosition, ie2 = void 0 === ae2 ? te.labelPosition : ae2, ne2 = a5.labelOffset, le2 = void 0 === ne2 ? te.labelOffset : ne2, re3 = a5.markers, de2 = void 0 === re3 ? te.markers : re3, ue2 = a5.colorBy, ce2 = a5.colors, se2 = a5.defs, he2 = void 0 === se2 ? te.defs : se2, fe2 = a5.fill, be2 = void 0 === fe2 ? te.fill : fe2, ve2 = a5.borderRadius, ge2 = void 0 === ve2 ? te.borderRadius : ve2, me2 = a5.borderWidth, pe2 = void 0 === me2 ? te.borderWidth : me2, xe2 = a5.borderColor, ye2 = a5.annotations, Se2 = void 0 === ye2 ? te.annotations : ye2, ke3 = a5.legendLabel, we3 = a5.tooltipLabel, Le3 = a5.valueFormat, Ce3 = a5.isInteractive, We3 = void 0 === Ce3 ? te.isInteractive : Ce3, Te3 = a5.tooltip, Be3 = void 0 === Te3 ? te.tooltip : Te3, Oe3 = a5.onClick, Ie2 = a5.onMouseEnter, Pe3 = a5.onMouseLeave, Ee2 = a5.legends, He3 = a5.role, Re3 = void 0 === He3 ? te.role : He3, Fe2 = a5.ariaLabel, De2 = a5.ariaLabelledBy, Ae = a5.ariaDescribedBy, Xe2 = a5.isFocusable, Ye2 = void 0 === Xe2 ? te.isFocusable : Xe2, Ge2 = a5.barAriaLabel, ze = a5.barAriaLabelledBy, Ne2 = a5.barAriaDescribedBy, je2 = a5.barAriaHidden, qe2 = a5.barAriaDisabled, Ke2 = a5.initialHiddenIds, _e = a5.enableTotals, Je2 = void 0 === _e ? te.enableTotals : _e, Qe3 = a5.totalsOffset, Ue2 = void 0 === Qe3 ? te.totalsOffset : Qe3, Ze2 = Ur(), $e2 = Ze2.animate, et2 = Ze2.config, tt2 = wt(o4, d3, r5), at2 = tt2.outerWidth, it2 = tt2.outerHeight, nt2 = tt2.margin, lt = tt2.innerWidth, rt2 = tt2.innerHeight, ot2 = Ve2({ indexBy: n6, label: K4, tooltipLabel: we3, valueFormat: Le3, colors: ce2, colorBy: ue2, borderColor: xe2, labelTextColor: ee2, groupMode: b5, layout: v6, reverse: g5, data: i5, keys: l3, minValue: m5, maxValue: p5, margin: nt2, width: lt, height: rt2, padding: C7, innerPadding: V3, valueScale: k7, indexScale: w6, enableLabel: q4, labelSkipWidth: U2, labelSkipHeight: $2, legends: Ee2, legendLabel: ke3, initialHiddenIds: Ke2, totalsOffset: Ue2 }), dt2 = ot2.bars, ut2 = ot2.barsWithValue, ct2 = ot2.xScale, st = ot2.yScale, ht2 = ot2.getLabel, ft = ot2.getTooltipLabel, bt = ot2.getBorderColor, vt2 = ot2.getLabelColor, gt2 = ot2.shouldRenderBarLabel, mt = ot2.toggleSerie, pt2 = ot2.legendsWithData, xt2 = ot2.barTotals, yt = ot2.getColor, St2 = oe(v6, g5, ie2, le2), kt2 = useTransition(ut2, { keys: function(e11) {
    return e11.key;
  }, from: function(e11) {
    return j4({ borderColor: bt(e11), color: e11.color, height: 0, labelColor: vt2(e11), labelOpacity: 0 }, St2(e11.width, e11.height), { transform: "translate(" + e11.x + ", " + (e11.y + e11.height) + ")", width: e11.width }, "vertical" === v6 ? {} : { height: e11.height, transform: "translate(" + e11.x + ", " + e11.y + ")", width: 0 });
  }, enter: function(e11) {
    return j4({ borderColor: bt(e11), color: e11.color, height: e11.height, labelColor: vt2(e11), labelOpacity: 1 }, St2(e11.width, e11.height), { transform: "translate(" + e11.x + ", " + e11.y + ")", width: e11.width });
  }, update: function(e11) {
    return j4({ borderColor: bt(e11), color: e11.color, height: e11.height, labelColor: vt2(e11), labelOpacity: 1 }, St2(e11.width, e11.height), { transform: "translate(" + e11.x + ", " + e11.y + ")", width: e11.width });
  }, leave: function(e11) {
    return j4({ borderColor: bt(e11), color: e11.color, height: 0, labelColor: vt2(e11), labelOpacity: 0 }, St2(e11.width, e11.height), { labelY: 0, transform: "translate(" + e11.x + ", " + (e11.y + e11.height) + ")", width: e11.width }, "vertical" === v6 ? {} : j4({}, St2(e11.width, e11.height), { labelX: 0, height: e11.height, transform: "translate(" + e11.x + ", " + e11.y + ")", width: 0 }));
  }, config: et2, immediate: !$e2, initial: $e2 ? void 0 : null }), wt2 = (0, import_react21.useMemo)(function() {
    return { borderRadius: ge2, borderWidth: pe2, enableLabel: q4, isInteractive: We3, labelSkipWidth: U2, labelSkipHeight: $2, onClick: Oe3, onMouseEnter: Ie2, onMouseLeave: Pe3, getTooltipLabel: ft, tooltip: Be3, isFocusable: Ye2, ariaLabel: Ge2, ariaLabelledBy: ze, ariaDescribedBy: Ne2, ariaHidden: je2, ariaDisabled: qe2 };
  }, [ge2, pe2, q4, ft, We3, $2, U2, Oe3, Ie2, Pe3, Be3, Ye2, Ge2, ze, Ne2, je2, qe2]), Lt2 = In(he2, dt2, be2, { dataKey: "data", targetKey: "data.fill" }), Ct2 = { annotations: null, axes: null, bars: null, grid: null, legends: null, markers: null, totals: null };
  Y4.includes("annotations") && (Ct2.annotations = (0, import_jsx_runtime6.jsx)(_2, { bars: dt2, annotations: Se2 }, "annotations")), Y4.includes("axes") && (Ct2.axes = (0, import_jsx_runtime6.jsx)(B2, { xScale: ct2, yScale: st, width: lt, height: rt2, top: M4, right: W5, bottom: B4, left: P5 }, "axes")), Y4.includes("bars") && (Ct2.bars = (0, import_jsx_runtime6.jsx)(import_react21.Fragment, { children: kt2(function(e11, t9) {
    return (0, import_react21.createElement)(z5, j4({}, wt2, { bar: t9, style: e11, shouldRenderLabel: gt2(t9), label: ht2(t9.data) }));
  }) }, "bars")), Y4.includes("grid") && (Ct2.grid = (0, import_jsx_runtime6.jsx)(C4, { width: lt, height: rt2, xScale: H2 ? ct2 : null, yScale: F2 ? st : null, xValues: D3, yValues: A4 }, "grid")), Y4.includes("legends") && (Ct2.legends = (0, import_jsx_runtime6.jsx)(J3, { width: lt, height: rt2, legends: pt2, toggleSerie: mt }, "legends")), Y4.includes("markers") && (Ct2.markers = (0, import_jsx_runtime6.jsx)(Rn, { markers: de2, width: lt, height: rt2, xScale: ct2, yScale: st }, "markers")), Y4.includes("totals") && Je2 && (Ct2.totals = (0, import_jsx_runtime6.jsx)(Me2, { data: xt2, springConfig: et2, animate: $e2, layout: v6 }, "totals"));
  var Vt = (0, import_react21.useMemo)(function() {
    return j4({}, wt2, { margin: nt2, width: o4, height: d3, innerWidth: lt, innerHeight: rt2, bars: dt2, legendData: pt2, enableLabel: q4, xScale: ct2, yScale: st, tooltip: Be3, getTooltipLabel: ft, onClick: Oe3, onMouseEnter: Ie2, onMouseLeave: Pe3, getColor: yt });
  }, [wt2, nt2, o4, d3, lt, rt2, dt2, pt2, q4, ct2, st, Be3, ft, Oe3, Ie2, Pe3, yt]);
  return (0, import_jsx_runtime6.jsx)(gn, { width: at2, height: it2, margin: nt2, defs: Lt2, role: Re3, ariaLabel: Fe2, ariaLabelledBy: De2, ariaDescribedBy: Ae, isFocusable: Ye2, children: Y4.map(function(e11, t9) {
    var a6;
    return "function" == typeof e11 ? (0, import_jsx_runtime6.jsx)(import_react21.Fragment, { children: (0, import_react21.createElement)(e11, Vt) }, t9) : null != (a6 = null == Ct2 ? void 0 : Ct2[e11]) ? a6 : null;
  }) });
};
var Be2 = function(e11) {
  var t9 = e11.isInteractive, a5 = void 0 === t9 ? te.isInteractive : t9, i5 = e11.animate, n6 = void 0 === i5 ? te.animate : i5, l3 = e11.motionConfig, r5 = void 0 === l3 ? te.motionConfig : l3, o4 = e11.theme, u3 = e11.renderWrapper, c9 = q3(e11, We2);
  return (0, import_jsx_runtime6.jsx)(St, { animate: n6, isInteractive: a5, motionConfig: r5, renderWrapper: u3, theme: o4, children: (0, import_jsx_runtime6.jsx)(Te2, j4({ isInteractive: a5 }, c9)) });
};
var Oe2 = ["isInteractive", "renderWrapper", "theme"];
var Ie = function(e11, t9, a5, i5) {
  return e11.find(function(e12) {
    return jn(e12.x + t9.left, e12.y + t9.top, e12.width, e12.height, a5, i5);
  });
};
var Pe2 = function(e11) {
  var t9 = e11.data, l3 = e11.indexBy, r5 = e11.keys, d3 = e11.margin, u3 = e11.width, s4 = e11.height, h2 = e11.groupMode, f3 = e11.layout, v6 = e11.reverse, g5 = e11.minValue, m5 = e11.maxValue, p5 = e11.valueScale, x5 = e11.indexScale, w6 = e11.padding, L4 = e11.innerPadding, C7 = e11.axisTop, T4 = e11.axisRight, P5 = e11.axisBottom, E5 = void 0 === P5 ? ae.axisBottom : P5, H2 = e11.axisLeft, F2 = void 0 === H2 ? ae.axisLeft : H2, D3 = e11.enableGridX, X4 = void 0 === D3 ? ae.enableGridX : D3, Y4 = e11.enableGridY, G = void 0 === Y4 ? ae.enableGridY : Y4, z5 = e11.gridXValues, N3 = e11.gridYValues, q4 = e11.labelPosition, K4 = void 0 === q4 ? ae.labelPosition : q4, _3 = e11.labelOffset, J4 = void 0 === _3 ? ae.labelOffset : _3, Q3 = e11.layers, U2 = void 0 === Q3 ? ae.layers : Q3, Z2 = e11.renderBar, $2 = void 0 === Z2 ? function(e12, t10) {
    var a5 = t10.bar, i5 = a5.color, n6 = a5.height, l4 = a5.width, r6 = a5.x, o4 = a5.y, d4 = t10.borderColor, u4 = t10.borderRadius, c9 = t10.borderWidth, s5 = t10.label, h3 = t10.labelColor, f4 = t10.shouldRenderLabel, b5 = t10.labelX, v7 = t10.labelY, g6 = t10.textAnchor;
    if (e12.fillStyle = i5, c9 > 0 && (e12.strokeStyle = d4, e12.lineWidth = c9), e12.beginPath(), u4 > 0) {
      var m6 = Math.min(u4, n6);
      e12.moveTo(r6 + m6, o4), e12.lineTo(r6 + l4 - m6, o4), e12.quadraticCurveTo(r6 + l4, o4, r6 + l4, o4 + m6), e12.lineTo(r6 + l4, o4 + n6 - m6), e12.quadraticCurveTo(r6 + l4, o4 + n6, r6 + l4 - m6, o4 + n6), e12.lineTo(r6 + m6, o4 + n6), e12.quadraticCurveTo(r6, o4 + n6, r6, o4 + n6 - m6), e12.lineTo(r6, o4 + m6), e12.quadraticCurveTo(r6, o4, r6 + m6, o4), e12.closePath();
    } else e12.rect(r6, o4, l4, n6);
    e12.fill(), c9 > 0 && e12.stroke(), f4 && (e12.textBaseline = "middle", e12.textAlign = "middle" === g6 ? "center" : g6, e12.fillStyle = h3, e12.fillText(s5, r6 + b5, o4 + v7));
  } : Z2, ee2 = e11.enableLabel, te2 = void 0 === ee2 ? ae.enableLabel : ee2, ie2 = e11.label, ne2 = e11.labelSkipWidth, le2 = void 0 === ne2 ? ae.labelSkipWidth : ne2, re3 = e11.labelSkipHeight, de2 = void 0 === re3 ? ae.labelSkipHeight : re3, ue2 = e11.labelTextColor, ce2 = e11.colorBy, se2 = e11.colors, he2 = e11.borderRadius, fe2 = void 0 === he2 ? ae.borderRadius : he2, be2 = e11.borderWidth, ve2 = void 0 === be2 ? ae.borderWidth : be2, ge2 = e11.borderColor, me2 = e11.annotations, pe2 = void 0 === me2 ? ae.annotations : me2, xe2 = e11.legendLabel, ye2 = e11.tooltipLabel, Se2 = e11.valueFormat, ke3 = e11.isInteractive, we3 = void 0 === ke3 ? ae.isInteractive : ke3, Le3 = e11.tooltip, Ce3 = void 0 === Le3 ? ae.tooltip : Le3, Me3 = e11.onClick, We3 = e11.onMouseEnter, Te3 = e11.onMouseLeave, Be3 = e11.legends, Oe3 = e11.pixelRatio, Pe3 = void 0 === Oe3 ? ae.pixelRatio : Oe3, Ee2 = e11.canvasRef, He3 = e11.enableTotals, Re3 = void 0 === He3 ? ae.enableTotals : He3, Fe2 = e11.totalsOffset, De2 = void 0 === Fe2 ? ae.totalsOffset : Fe2, Ae = (0, import_react21.useRef)(null), Xe2 = zt(), Ye2 = wt(u3, s4, d3), Ge2 = Ye2.margin, ze = Ye2.innerWidth, Ne2 = Ye2.innerHeight, je2 = Ye2.outerWidth, qe2 = Ye2.outerHeight, Ke2 = Ve2({ indexBy: l3, label: ie2, tooltipLabel: ye2, valueFormat: Se2, colors: se2, colorBy: ce2, borderColor: ge2, labelTextColor: ue2, groupMode: h2, layout: f3, reverse: v6, data: t9, keys: r5, minValue: g5, maxValue: m5, margin: Ge2, width: ze, height: Ne2, padding: w6, innerPadding: L4, valueScale: p5, indexScale: x5, enableLabel: te2, labelSkipWidth: le2, labelSkipHeight: de2, legends: Be3, legendLabel: xe2, totalsOffset: De2 }), _e = Ke2.bars, Je2 = Ke2.barsWithValue, Qe3 = Ke2.xScale, Ue2 = Ke2.yScale, Ze2 = Ke2.getLabel, $e2 = Ke2.getTooltipLabel, et2 = Ke2.getBorderColor, tt2 = Ke2.getLabelColor, at2 = Ke2.shouldRenderBarLabel, it2 = Ke2.legendsWithData, nt2 = Ke2.barTotals, lt = Ke2.getColor, rt2 = k(), ot2 = rt2.showTooltipFromEvent, dt2 = rt2.hideTooltip, ut2 = j3({ annotations: S2({ data: _e, annotations: pe2, getPosition: function(e12) {
    return { x: e12.x, y: e12.y };
  }, getDimensions: function(e12) {
    var t10 = e12.width, a5 = e12.height;
    return { width: t10, height: a5, size: Math.max(t10, a5) };
  } }) }), ct2 = (0, import_react21.useMemo)(function() {
    return { borderRadius: fe2, borderWidth: ve2, isInteractive: we3, isFocusable: false, labelSkipWidth: le2, labelSkipHeight: de2, margin: Ge2, width: u3, height: s4, innerWidth: ze, innerHeight: Ne2, bars: _e, legendData: it2, enableLabel: te2, xScale: Qe3, yScale: Ue2, tooltip: Ce3, getTooltipLabel: $e2, onClick: Me3, onMouseEnter: We3, onMouseLeave: Te3, getColor: lt };
  }, [fe2, ve2, we3, le2, de2, Ge2, u3, s4, ze, Ne2, _e, it2, te2, Qe3, Ue2, Ce3, $e2, Me3, We3, Te3, lt]), st = Ot(Se2), ht2 = oe(f3, v6, K4, J4);
  (0, import_react21.useEffect)(function() {
    var e12, t10 = null == (e12 = Ae.current) ? void 0 : e12.getContext("2d");
    Ae.current && t10 && (Ae.current.width = je2 * Pe3, Ae.current.height = qe2 * Pe3, t10.scale(Pe3, Pe3), t10.fillStyle = Xe2.background, t10.fillRect(0, 0, je2, qe2), t10.translate(Ge2.left, Ge2.top), U2.forEach(function(e13) {
      "grid" === e13 ? "number" == typeof Xe2.grid.line.strokeWidth && Xe2.grid.line.strokeWidth > 0 && (t10.lineWidth = Xe2.grid.line.strokeWidth, t10.strokeStyle = Xe2.grid.line.stroke, X4 && z3(t10, { width: ze, height: Ne2, scale: Qe3, axis: "x", values: z5 }), G && z3(t10, { width: ze, height: Ne2, scale: Ue2, axis: "y", values: N3 })) : "axes" === e13 ? j2(t10, { xScale: Qe3, yScale: Ue2, width: ze, height: Ne2, top: C7, right: T4, bottom: E5, left: F2, theme: Xe2 }) : "bars" === e13 ? Je2.forEach(function(e14) {
        $2(t10, j4({ bar: e14, borderColor: et2(e14), borderRadius: fe2, borderWidth: ve2, label: Ze2(e14.data), labelColor: tt2(e14), shouldRenderLabel: at2(e14) }, ht2(e14.width, e14.height)));
      }) : "legends" === e13 ? it2.forEach(function(e14) {
        var a5 = e14[0], i5 = e14[1];
        H(t10, j4({}, a5, { data: i5, containerWidth: ze, containerHeight: Ne2, theme: Xe2 }));
      }) : "annotations" === e13 ? J2(t10, { annotations: ut2, theme: Xe2 }) : "totals" === e13 && Re3 ? function(e14, t11, a5, i5) {
        void 0 === i5 && (i5 = ae.layout), e14.fillStyle = a5.text.fill, e14.font = "bold " + a5.labels.text.fontSize + "px " + a5.labels.text.fontFamily, e14.textBaseline = "vertical" === i5 ? "alphabetic" : "middle", e14.textAlign = "vertical" === i5 ? "center" : "start", t11.forEach(function(t14) {
          e14.fillText(t14.formattedValue, t14.x, t14.y);
        });
      }(t10, nt2, Xe2, f3) : "function" == typeof e13 && e13(t10, ct2);
    }), t10.save());
  }, [E5, F2, T4, C7, Je2, fe2, ve2, ut2, X4, G, et2, Ze2, tt2, z5, N3, h2, s4, Ne2, ze, ct2, U2, f3, it2, Ge2.left, Ge2.top, qe2, je2, Pe3, $2, Qe3, Ue2, v6, at2, Xe2, u3, nt2, Re3, st, ht2]);
  var ft = (0, import_react21.useCallback)(function(e12) {
    if (_e && Ae.current) {
      var t10 = Sn(Ae.current, e12), a5 = t10[0], i5 = t10[1], n6 = Ie(_e, Ge2, a5, i5);
      void 0 !== n6 ? (ot2((0, import_react21.createElement)(Ce3, j4({}, n6.data, { color: n6.color, label: n6.label, value: Number(n6.data.value) })), e12), "mouseenter" === e12.type && (null == We3 || We3(n6.data, e12))) : dt2();
    }
  }, [dt2, Ge2, We3, _e, ot2, Ce3]), bt = (0, import_react21.useCallback)(function(e12) {
    if (_e && Ae.current) {
      dt2();
      var t10 = Sn(Ae.current, e12), a5 = t10[0], i5 = t10[1], n6 = Ie(_e, Ge2, a5, i5);
      n6 && (null == Te3 || Te3(n6.data, e12));
    }
  }, [dt2, Ge2, Te3, _e]), vt2 = (0, import_react21.useCallback)(function(e12) {
    if (_e && Ae.current) {
      var t10 = Sn(Ae.current, e12), a5 = t10[0], i5 = t10[1], n6 = Ie(_e, Ge2, a5, i5);
      void 0 !== n6 && (null == Me3 || Me3(j4({}, n6.data, { color: n6.color }), e12));
    }
  }, [Ge2, Me3, _e]);
  return (0, import_jsx_runtime6.jsx)("canvas", { ref: function(e12) {
    Ae.current = e12, Ee2 && "current" in Ee2 && (Ee2.current = e12);
  }, width: je2 * Pe3, height: qe2 * Pe3, style: { width: je2, height: qe2, cursor: we3 ? "auto" : "normal" }, onMouseEnter: we3 ? ft : void 0, onMouseMove: we3 ? ft : void 0, onMouseLeave: we3 ? bt : void 0, onClick: we3 ? vt2 : void 0 });
};
var Ee = (0, import_react21.forwardRef)(function(e11, t9) {
  var a5 = e11.isInteractive, i5 = e11.renderWrapper, n6 = e11.theme, l3 = q3(e11, Oe2);
  return (0, import_jsx_runtime6.jsx)(St, { isInteractive: a5, renderWrapper: i5, theme: n6, animate: false, children: (0, import_jsx_runtime6.jsx)(Pe2, j4({}, l3, { canvasRef: t9 })) });
});
var He2 = function(e11) {
  return (0, import_jsx_runtime6.jsx)(It, { children: function(t9) {
    var a5 = t9.width, i5 = t9.height;
    return (0, import_jsx_runtime6.jsx)(Be2, j4({ width: a5, height: i5 }, e11));
  } });
};
var Re2 = (0, import_react21.forwardRef)(function(e11, t9) {
  return (0, import_jsx_runtime6.jsx)(It, { children: function(a5) {
    var i5 = a5.width, n6 = a5.height;
    return (0, import_jsx_runtime6.jsx)(Ee, j4({ width: i5, height: n6 }, e11, { ref: t9 }));
  } });
});
export {
  Be2 as Bar,
  Ee as BarCanvas,
  U as BarItem,
  $ as BarTooltip,
  Me2 as BarTotals,
  He2 as ResponsiveBar,
  Re2 as ResponsiveBarCanvas,
  ae as canvasDefaultProps,
  ee as defaultProps,
  te as svgDefaultProps
};
//# sourceMappingURL=@nivo_bar.js.map
