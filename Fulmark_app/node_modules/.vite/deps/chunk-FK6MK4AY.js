import {
  RouterProvider2
} from "./chunk-FIB7OQ2W.js";
import {
  DataRouterContext,
  DataRouterStateContext,
  Navigate,
  Route,
  Routes,
  createHashRouter,
  createMemoryRouter,
  useBlocker,
  useInRouterContext,
  useLocation,
  useNavigate,
  useParams
} from "./chunk-AROJ2QES.js";
import {
  Form<PERSON>rov<PERSON>,
  useController,
  useForm,
  useFormContext,
  useFormState,
  useWatch
} from "./chunk-JHPPEXU2.js";
import {
  require_baseClone,
  require_baseFlatten,
  require_baseRest,
  require_baseUniq,
  require_baseUnset,
  require_get,
  require_isArrayLikeObject,
  require_isIterateeCall,
  require_isSymbol,
  require_keysIn,
  require_memoize,
  require_merge,
  require_pick,
  require_set
} from "./chunk-H4XJCW4X.js";
import {
  QueryClient,
  QueryClientProvider,
  QueryObserver,
  notifyManager,
  useInfiniteQuery,
  useMutation,
  useQuery,
  useQueryClient
} from "./chunk-HU6PDPOE.js";
import {
  isMatch,
  isValid,
  parseISO
} from "./chunk-K5SMX77J.js";
import {
  require_dist
} from "./chunk-CZVV7UDY.js";
import {
  require_eq,
  require_isEqual,
  require_isObject,
  require_root
} from "./chunk-5NM3XKGP.js";
import {
  require_react_is
} from "./chunk-TUVFVZDQ.js";
import {
  require_jsx_runtime
} from "./chunk-CRNJR6QK.js";
import {
  require_react
} from "./chunk-ZMLY2J2T.js";
import {
  __commonJS,
  __export,
  __publicField,
  __toESM
} from "./chunk-4B2QHNJT.js";

// node_modules/lodash/unset.js
var require_unset = __commonJS({
  "node_modules/lodash/unset.js"(exports, module) {
    var baseUnset = require_baseUnset();
    function unset2(object, path) {
      return object == null ? true : baseUnset(object, path);
    }
    module.exports = unset2;
  }
});

// node_modules/lodash/cloneDeep.js
var require_cloneDeep = __commonJS({
  "node_modules/lodash/cloneDeep.js"(exports, module) {
    var baseClone = require_baseClone();
    var CLONE_DEEP_FLAG = 1;
    var CLONE_SYMBOLS_FLAG = 4;
    function cloneDeep3(value) {
      return baseClone(value, CLONE_DEEP_FLAG | CLONE_SYMBOLS_FLAG);
    }
    module.exports = cloneDeep3;
  }
});

// node_modules/lodash/now.js
var require_now = __commonJS({
  "node_modules/lodash/now.js"(exports, module) {
    var root = require_root();
    var now = function() {
      return root.Date.now();
    };
    module.exports = now;
  }
});

// node_modules/lodash/_trimmedEndIndex.js
var require_trimmedEndIndex = __commonJS({
  "node_modules/lodash/_trimmedEndIndex.js"(exports, module) {
    var reWhitespace = /\s/;
    function trimmedEndIndex(string) {
      var index = string.length;
      while (index-- && reWhitespace.test(string.charAt(index))) {
      }
      return index;
    }
    module.exports = trimmedEndIndex;
  }
});

// node_modules/lodash/_baseTrim.js
var require_baseTrim = __commonJS({
  "node_modules/lodash/_baseTrim.js"(exports, module) {
    var trimmedEndIndex = require_trimmedEndIndex();
    var reTrimStart = /^\s+/;
    function baseTrim(string) {
      return string ? string.slice(0, trimmedEndIndex(string) + 1).replace(reTrimStart, "") : string;
    }
    module.exports = baseTrim;
  }
});

// node_modules/lodash/toNumber.js
var require_toNumber = __commonJS({
  "node_modules/lodash/toNumber.js"(exports, module) {
    var baseTrim = require_baseTrim();
    var isObject3 = require_isObject();
    var isSymbol = require_isSymbol();
    var NAN = 0 / 0;
    var reIsBadHex = /^[-+]0x[0-9a-f]+$/i;
    var reIsBinary = /^0b[01]+$/i;
    var reIsOctal = /^0o[0-7]+$/i;
    var freeParseInt = parseInt;
    function toNumber(value) {
      if (typeof value == "number") {
        return value;
      }
      if (isSymbol(value)) {
        return NAN;
      }
      if (isObject3(value)) {
        var other = typeof value.valueOf == "function" ? value.valueOf() : value;
        value = isObject3(other) ? other + "" : other;
      }
      if (typeof value != "string") {
        return value === 0 ? value : +value;
      }
      value = baseTrim(value);
      var isBinary = reIsBinary.test(value);
      return isBinary || reIsOctal.test(value) ? freeParseInt(value.slice(2), isBinary ? 2 : 8) : reIsBadHex.test(value) ? NAN : +value;
    }
    module.exports = toNumber;
  }
});

// node_modules/lodash/debounce.js
var require_debounce = __commonJS({
  "node_modules/lodash/debounce.js"(exports, module) {
    var isObject3 = require_isObject();
    var now = require_now();
    var toNumber = require_toNumber();
    var FUNC_ERROR_TEXT = "Expected a function";
    var nativeMax = Math.max;
    var nativeMin = Math.min;
    function debounce5(func, wait, options) {
      var lastArgs, lastThis, maxWait, result, timerId, lastCallTime, lastInvokeTime = 0, leading = false, maxing = false, trailing = true;
      if (typeof func != "function") {
        throw new TypeError(FUNC_ERROR_TEXT);
      }
      wait = toNumber(wait) || 0;
      if (isObject3(options)) {
        leading = !!options.leading;
        maxing = "maxWait" in options;
        maxWait = maxing ? nativeMax(toNumber(options.maxWait) || 0, wait) : maxWait;
        trailing = "trailing" in options ? !!options.trailing : trailing;
      }
      function invokeFunc(time) {
        var args = lastArgs, thisArg = lastThis;
        lastArgs = lastThis = void 0;
        lastInvokeTime = time;
        result = func.apply(thisArg, args);
        return result;
      }
      function leadingEdge(time) {
        lastInvokeTime = time;
        timerId = setTimeout(timerExpired, wait);
        return leading ? invokeFunc(time) : result;
      }
      function remainingWait(time) {
        var timeSinceLastCall = time - lastCallTime, timeSinceLastInvoke = time - lastInvokeTime, timeWaiting = wait - timeSinceLastCall;
        return maxing ? nativeMin(timeWaiting, maxWait - timeSinceLastInvoke) : timeWaiting;
      }
      function shouldInvoke(time) {
        var timeSinceLastCall = time - lastCallTime, timeSinceLastInvoke = time - lastInvokeTime;
        return lastCallTime === void 0 || timeSinceLastCall >= wait || timeSinceLastCall < 0 || maxing && timeSinceLastInvoke >= maxWait;
      }
      function timerExpired() {
        var time = now();
        if (shouldInvoke(time)) {
          return trailingEdge(time);
        }
        timerId = setTimeout(timerExpired, remainingWait(time));
      }
      function trailingEdge(time) {
        timerId = void 0;
        if (trailing && lastArgs) {
          return invokeFunc(time);
        }
        lastArgs = lastThis = void 0;
        return result;
      }
      function cancel() {
        if (timerId !== void 0) {
          clearTimeout(timerId);
        }
        lastInvokeTime = 0;
        lastArgs = lastCallTime = lastThis = timerId = void 0;
      }
      function flush() {
        return timerId === void 0 ? result : trailingEdge(now());
      }
      function debounced() {
        var time = now(), isInvoking = shouldInvoke(time);
        lastArgs = arguments;
        lastThis = this;
        lastCallTime = time;
        if (isInvoking) {
          if (timerId === void 0) {
            return leadingEdge(lastCallTime);
          }
          if (maxing) {
            clearTimeout(timerId);
            timerId = setTimeout(timerExpired, wait);
            return invokeFunc(lastCallTime);
          }
        }
        if (timerId === void 0) {
          timerId = setTimeout(timerExpired, wait);
        }
        return result;
      }
      debounced.cancel = cancel;
      debounced.flush = flush;
      return debounced;
    }
    module.exports = debounce5;
  }
});

// node_modules/strict-uri-encode/index.js
var require_strict_uri_encode = __commonJS({
  "node_modules/strict-uri-encode/index.js"(exports, module) {
    "use strict";
    module.exports = (str) => encodeURIComponent(str).replace(/[!'()*]/g, (x) => `%${x.charCodeAt(0).toString(16).toUpperCase()}`);
  }
});

// node_modules/decode-uri-component/index.js
var require_decode_uri_component = __commonJS({
  "node_modules/decode-uri-component/index.js"(exports, module) {
    "use strict";
    var token = "%[a-f0-9]{2}";
    var singleMatcher = new RegExp("(" + token + ")|([^%]+?)", "gi");
    var multiMatcher = new RegExp("(" + token + ")+", "gi");
    function decodeComponents(components, split) {
      try {
        return [decodeURIComponent(components.join(""))];
      } catch (err) {
      }
      if (components.length === 1) {
        return components;
      }
      split = split || 1;
      var left = components.slice(0, split);
      var right = components.slice(split);
      return Array.prototype.concat.call([], decodeComponents(left), decodeComponents(right));
    }
    function decode(input) {
      try {
        return decodeURIComponent(input);
      } catch (err) {
        var tokens = input.match(singleMatcher) || [];
        for (var i = 1; i < tokens.length; i++) {
          input = decodeComponents(tokens, i).join("");
          tokens = input.match(singleMatcher) || [];
        }
        return input;
      }
    }
    function customDecodeURIComponent(input) {
      var replaceMap = {
        "%FE%FF": "��",
        "%FF%FE": "��"
      };
      var match = multiMatcher.exec(input);
      while (match) {
        try {
          replaceMap[match[0]] = decodeURIComponent(match[0]);
        } catch (err) {
          var result = decode(match[0]);
          if (result !== match[0]) {
            replaceMap[match[0]] = result;
          }
        }
        match = multiMatcher.exec(input);
      }
      replaceMap["%C2"] = "�";
      var entries = Object.keys(replaceMap);
      for (var i = 0; i < entries.length; i++) {
        var key = entries[i];
        input = input.replace(new RegExp(key, "g"), replaceMap[key]);
      }
      return input;
    }
    module.exports = function(encodedURI) {
      if (typeof encodedURI !== "string") {
        throw new TypeError("Expected `encodedURI` to be of type `string`, got `" + typeof encodedURI + "`");
      }
      try {
        encodedURI = encodedURI.replace(/\+/g, " ");
        return decodeURIComponent(encodedURI);
      } catch (err) {
        return customDecodeURIComponent(encodedURI);
      }
    };
  }
});

// node_modules/split-on-first/index.js
var require_split_on_first = __commonJS({
  "node_modules/split-on-first/index.js"(exports, module) {
    "use strict";
    module.exports = (string, separator) => {
      if (!(typeof string === "string" && typeof separator === "string")) {
        throw new TypeError("Expected the arguments to be of type `string`");
      }
      if (separator === "") {
        return [string];
      }
      const separatorIndex = string.indexOf(separator);
      if (separatorIndex === -1) {
        return [string];
      }
      return [
        string.slice(0, separatorIndex),
        string.slice(separatorIndex + separator.length)
      ];
    };
  }
});

// node_modules/filter-obj/index.js
var require_filter_obj = __commonJS({
  "node_modules/filter-obj/index.js"(exports, module) {
    "use strict";
    module.exports = function(obj, predicate) {
      var ret = {};
      var keys = Object.keys(obj);
      var isArr = Array.isArray(predicate);
      for (var i = 0; i < keys.length; i++) {
        var key = keys[i];
        var val = obj[key];
        if (isArr ? predicate.indexOf(key) !== -1 : predicate(key, val, obj)) {
          ret[key] = val;
        }
      }
      return ret;
    };
  }
});

// node_modules/query-string/index.js
var require_query_string = __commonJS({
  "node_modules/query-string/index.js"(exports) {
    "use strict";
    var strictUriEncode = require_strict_uri_encode();
    var decodeComponent = require_decode_uri_component();
    var splitOnFirst = require_split_on_first();
    var filterObject = require_filter_obj();
    var isNullOrUndefined = (value) => value === null || value === void 0;
    var encodeFragmentIdentifier = Symbol("encodeFragmentIdentifier");
    function encoderForArrayFormat(options) {
      switch (options.arrayFormat) {
        case "index":
          return (key) => (result, value) => {
            const index = result.length;
            if (value === void 0 || options.skipNull && value === null || options.skipEmptyString && value === "") {
              return result;
            }
            if (value === null) {
              return [...result, [encode(key, options), "[", index, "]"].join("")];
            }
            return [
              ...result,
              [encode(key, options), "[", encode(index, options), "]=", encode(value, options)].join("")
            ];
          };
        case "bracket":
          return (key) => (result, value) => {
            if (value === void 0 || options.skipNull && value === null || options.skipEmptyString && value === "") {
              return result;
            }
            if (value === null) {
              return [...result, [encode(key, options), "[]"].join("")];
            }
            return [...result, [encode(key, options), "[]=", encode(value, options)].join("")];
          };
        case "colon-list-separator":
          return (key) => (result, value) => {
            if (value === void 0 || options.skipNull && value === null || options.skipEmptyString && value === "") {
              return result;
            }
            if (value === null) {
              return [...result, [encode(key, options), ":list="].join("")];
            }
            return [...result, [encode(key, options), ":list=", encode(value, options)].join("")];
          };
        case "comma":
        case "separator":
        case "bracket-separator": {
          const keyValueSep = options.arrayFormat === "bracket-separator" ? "[]=" : "=";
          return (key) => (result, value) => {
            if (value === void 0 || options.skipNull && value === null || options.skipEmptyString && value === "") {
              return result;
            }
            value = value === null ? "" : value;
            if (result.length === 0) {
              return [[encode(key, options), keyValueSep, encode(value, options)].join("")];
            }
            return [[result, encode(value, options)].join(options.arrayFormatSeparator)];
          };
        }
        default:
          return (key) => (result, value) => {
            if (value === void 0 || options.skipNull && value === null || options.skipEmptyString && value === "") {
              return result;
            }
            if (value === null) {
              return [...result, encode(key, options)];
            }
            return [...result, [encode(key, options), "=", encode(value, options)].join("")];
          };
      }
    }
    function parserForArrayFormat(options) {
      let result;
      switch (options.arrayFormat) {
        case "index":
          return (key, value, accumulator) => {
            result = /\[(\d*)\]$/.exec(key);
            key = key.replace(/\[\d*\]$/, "");
            if (!result) {
              accumulator[key] = value;
              return;
            }
            if (accumulator[key] === void 0) {
              accumulator[key] = {};
            }
            accumulator[key][result[1]] = value;
          };
        case "bracket":
          return (key, value, accumulator) => {
            result = /(\[\])$/.exec(key);
            key = key.replace(/\[\]$/, "");
            if (!result) {
              accumulator[key] = value;
              return;
            }
            if (accumulator[key] === void 0) {
              accumulator[key] = [value];
              return;
            }
            accumulator[key] = [].concat(accumulator[key], value);
          };
        case "colon-list-separator":
          return (key, value, accumulator) => {
            result = /(:list)$/.exec(key);
            key = key.replace(/:list$/, "");
            if (!result) {
              accumulator[key] = value;
              return;
            }
            if (accumulator[key] === void 0) {
              accumulator[key] = [value];
              return;
            }
            accumulator[key] = [].concat(accumulator[key], value);
          };
        case "comma":
        case "separator":
          return (key, value, accumulator) => {
            const isArray2 = typeof value === "string" && value.includes(options.arrayFormatSeparator);
            const isEncodedArray = typeof value === "string" && !isArray2 && decode(value, options).includes(options.arrayFormatSeparator);
            value = isEncodedArray ? decode(value, options) : value;
            const newValue = isArray2 || isEncodedArray ? value.split(options.arrayFormatSeparator).map((item) => decode(item, options)) : value === null ? value : decode(value, options);
            accumulator[key] = newValue;
          };
        case "bracket-separator":
          return (key, value, accumulator) => {
            const isArray2 = /(\[\])$/.test(key);
            key = key.replace(/\[\]$/, "");
            if (!isArray2) {
              accumulator[key] = value ? decode(value, options) : value;
              return;
            }
            const arrayValue = value === null ? [] : value.split(options.arrayFormatSeparator).map((item) => decode(item, options));
            if (accumulator[key] === void 0) {
              accumulator[key] = arrayValue;
              return;
            }
            accumulator[key] = [].concat(accumulator[key], arrayValue);
          };
        default:
          return (key, value, accumulator) => {
            if (accumulator[key] === void 0) {
              accumulator[key] = value;
              return;
            }
            accumulator[key] = [].concat(accumulator[key], value);
          };
      }
    }
    function validateArrayFormatSeparator(value) {
      if (typeof value !== "string" || value.length !== 1) {
        throw new TypeError("arrayFormatSeparator must be single character string");
      }
    }
    function encode(value, options) {
      if (options.encode) {
        return options.strict ? strictUriEncode(value) : encodeURIComponent(value);
      }
      return value;
    }
    function decode(value, options) {
      if (options.decode) {
        return decodeComponent(value);
      }
      return value;
    }
    function keysSorter(input) {
      if (Array.isArray(input)) {
        return input.sort();
      }
      if (typeof input === "object") {
        return keysSorter(Object.keys(input)).sort((a, b) => Number(a) - Number(b)).map((key) => input[key]);
      }
      return input;
    }
    function removeHash(input) {
      const hashStart = input.indexOf("#");
      if (hashStart !== -1) {
        input = input.slice(0, hashStart);
      }
      return input;
    }
    function getHash(url) {
      let hash2 = "";
      const hashStart = url.indexOf("#");
      if (hashStart !== -1) {
        hash2 = url.slice(hashStart);
      }
      return hash2;
    }
    function extract(input) {
      input = removeHash(input);
      const queryStart = input.indexOf("?");
      if (queryStart === -1) {
        return "";
      }
      return input.slice(queryStart + 1);
    }
    function parseValue(value, options) {
      if (options.parseNumbers && !Number.isNaN(Number(value)) && (typeof value === "string" && value.trim() !== "")) {
        value = Number(value);
      } else if (options.parseBooleans && value !== null && (value.toLowerCase() === "true" || value.toLowerCase() === "false")) {
        value = value.toLowerCase() === "true";
      }
      return value;
    }
    function parse3(query, options) {
      options = Object.assign({
        decode: true,
        sort: true,
        arrayFormat: "none",
        arrayFormatSeparator: ",",
        parseNumbers: false,
        parseBooleans: false
      }, options);
      validateArrayFormatSeparator(options.arrayFormatSeparator);
      const formatter = parserForArrayFormat(options);
      const ret = /* @__PURE__ */ Object.create(null);
      if (typeof query !== "string") {
        return ret;
      }
      query = query.trim().replace(/^[?#&]/, "");
      if (!query) {
        return ret;
      }
      for (const param of query.split("&")) {
        if (param === "") {
          continue;
        }
        let [key, value] = splitOnFirst(options.decode ? param.replace(/\+/g, " ") : param, "=");
        value = value === void 0 ? null : ["comma", "separator", "bracket-separator"].includes(options.arrayFormat) ? value : decode(value, options);
        formatter(decode(key, options), value, ret);
      }
      for (const key of Object.keys(ret)) {
        const value = ret[key];
        if (typeof value === "object" && value !== null) {
          for (const k of Object.keys(value)) {
            value[k] = parseValue(value[k], options);
          }
        } else {
          ret[key] = parseValue(value, options);
        }
      }
      if (options.sort === false) {
        return ret;
      }
      return (options.sort === true ? Object.keys(ret).sort() : Object.keys(ret).sort(options.sort)).reduce((result, key) => {
        const value = ret[key];
        if (Boolean(value) && typeof value === "object" && !Array.isArray(value)) {
          result[key] = keysSorter(value);
        } else {
          result[key] = value;
        }
        return result;
      }, /* @__PURE__ */ Object.create(null));
    }
    exports.extract = extract;
    exports.parse = parse3;
    exports.stringify = (object, options) => {
      if (!object) {
        return "";
      }
      options = Object.assign({
        encode: true,
        strict: true,
        arrayFormat: "none",
        arrayFormatSeparator: ","
      }, options);
      validateArrayFormatSeparator(options.arrayFormatSeparator);
      const shouldFilter = (key) => options.skipNull && isNullOrUndefined(object[key]) || options.skipEmptyString && object[key] === "";
      const formatter = encoderForArrayFormat(options);
      const objectCopy = {};
      for (const key of Object.keys(object)) {
        if (!shouldFilter(key)) {
          objectCopy[key] = object[key];
        }
      }
      const keys = Object.keys(objectCopy);
      if (options.sort !== false) {
        keys.sort(options.sort);
      }
      return keys.map((key) => {
        const value = object[key];
        if (value === void 0) {
          return "";
        }
        if (value === null) {
          return encode(key, options);
        }
        if (Array.isArray(value)) {
          if (value.length === 0 && options.arrayFormat === "bracket-separator") {
            return encode(key, options) + "[]";
          }
          return value.reduce(formatter(key), []).join("&");
        }
        return encode(key, options) + "=" + encode(value, options);
      }).filter((x) => x.length > 0).join("&");
    };
    exports.parseUrl = (url, options) => {
      options = Object.assign({
        decode: true
      }, options);
      const [url_, hash2] = splitOnFirst(url, "#");
      return Object.assign(
        {
          url: url_.split("?")[0] || "",
          query: parse3(extract(url), options)
        },
        options && options.parseFragmentIdentifier && hash2 ? { fragmentIdentifier: decode(hash2, options) } : {}
      );
    };
    exports.stringifyUrl = (object, options) => {
      options = Object.assign({
        encode: true,
        strict: true,
        [encodeFragmentIdentifier]: true
      }, options);
      const url = removeHash(object.url).split("?")[0] || "";
      const queryFromUrl = exports.extract(object.url);
      const parsedQueryFromUrl = exports.parse(queryFromUrl, { sort: false });
      const query = Object.assign(parsedQueryFromUrl, object.query);
      let queryString = exports.stringify(query, options);
      if (queryString) {
        queryString = `?${queryString}`;
      }
      let hash2 = getHash(object.url);
      if (object.fragmentIdentifier) {
        hash2 = `#${options[encodeFragmentIdentifier] ? encode(object.fragmentIdentifier, options) : object.fragmentIdentifier}`;
      }
      return `${url}${queryString}${hash2}`;
    };
    exports.pick = (input, filter, options) => {
      options = Object.assign({
        parseFragmentIdentifier: true,
        [encodeFragmentIdentifier]: false
      }, options);
      const { url, query, fragmentIdentifier } = exports.parseUrl(input, options);
      return exports.stringifyUrl({
        url,
        query: filterObject(query, filter),
        fragmentIdentifier
      }, options);
    };
    exports.exclude = (input, filter, options) => {
      const exclusionFilter = Array.isArray(filter) ? (key) => !filter.includes(key) : (key, value) => !filter(key, value);
      return exports.pick(input, exclusionFilter, options);
    };
  }
});

// node_modules/eventemitter3/index.js
var require_eventemitter3 = __commonJS({
  "node_modules/eventemitter3/index.js"(exports, module) {
    "use strict";
    var has = Object.prototype.hasOwnProperty;
    var prefix = "~";
    function Events() {
    }
    if (Object.create) {
      Events.prototype = /* @__PURE__ */ Object.create(null);
      if (!new Events().__proto__) prefix = false;
    }
    function EE(fn, context, once) {
      this.fn = fn;
      this.context = context;
      this.once = once || false;
    }
    function addListener(emitter, event, fn, context, once) {
      if (typeof fn !== "function") {
        throw new TypeError("The listener must be a function");
      }
      var listener = new EE(fn, context || emitter, once), evt = prefix ? prefix + event : event;
      if (!emitter._events[evt]) emitter._events[evt] = listener, emitter._eventsCount++;
      else if (!emitter._events[evt].fn) emitter._events[evt].push(listener);
      else emitter._events[evt] = [emitter._events[evt], listener];
      return emitter;
    }
    function clearEvent(emitter, evt) {
      if (--emitter._eventsCount === 0) emitter._events = new Events();
      else delete emitter._events[evt];
    }
    function EventEmitter2() {
      this._events = new Events();
      this._eventsCount = 0;
    }
    EventEmitter2.prototype.eventNames = function eventNames() {
      var names = [], events, name;
      if (this._eventsCount === 0) return names;
      for (name in events = this._events) {
        if (has.call(events, name)) names.push(prefix ? name.slice(1) : name);
      }
      if (Object.getOwnPropertySymbols) {
        return names.concat(Object.getOwnPropertySymbols(events));
      }
      return names;
    };
    EventEmitter2.prototype.listeners = function listeners(event) {
      var evt = prefix ? prefix + event : event, handlers = this._events[evt];
      if (!handlers) return [];
      if (handlers.fn) return [handlers.fn];
      for (var i = 0, l = handlers.length, ee = new Array(l); i < l; i++) {
        ee[i] = handlers[i].fn;
      }
      return ee;
    };
    EventEmitter2.prototype.listenerCount = function listenerCount(event) {
      var evt = prefix ? prefix + event : event, listeners = this._events[evt];
      if (!listeners) return 0;
      if (listeners.fn) return 1;
      return listeners.length;
    };
    EventEmitter2.prototype.emit = function emit(event, a1, a2, a3, a4, a5) {
      var evt = prefix ? prefix + event : event;
      if (!this._events[evt]) return false;
      var listeners = this._events[evt], len = arguments.length, args, i;
      if (listeners.fn) {
        if (listeners.once) this.removeListener(event, listeners.fn, void 0, true);
        switch (len) {
          case 1:
            return listeners.fn.call(listeners.context), true;
          case 2:
            return listeners.fn.call(listeners.context, a1), true;
          case 3:
            return listeners.fn.call(listeners.context, a1, a2), true;
          case 4:
            return listeners.fn.call(listeners.context, a1, a2, a3), true;
          case 5:
            return listeners.fn.call(listeners.context, a1, a2, a3, a4), true;
          case 6:
            return listeners.fn.call(listeners.context, a1, a2, a3, a4, a5), true;
        }
        for (i = 1, args = new Array(len - 1); i < len; i++) {
          args[i - 1] = arguments[i];
        }
        listeners.fn.apply(listeners.context, args);
      } else {
        var length = listeners.length, j;
        for (i = 0; i < length; i++) {
          if (listeners[i].once) this.removeListener(event, listeners[i].fn, void 0, true);
          switch (len) {
            case 1:
              listeners[i].fn.call(listeners[i].context);
              break;
            case 2:
              listeners[i].fn.call(listeners[i].context, a1);
              break;
            case 3:
              listeners[i].fn.call(listeners[i].context, a1, a2);
              break;
            case 4:
              listeners[i].fn.call(listeners[i].context, a1, a2, a3);
              break;
            default:
              if (!args) for (j = 1, args = new Array(len - 1); j < len; j++) {
                args[j - 1] = arguments[j];
              }
              listeners[i].fn.apply(listeners[i].context, args);
          }
        }
      }
      return true;
    };
    EventEmitter2.prototype.on = function on(event, fn, context) {
      return addListener(this, event, fn, context, false);
    };
    EventEmitter2.prototype.once = function once(event, fn, context) {
      return addListener(this, event, fn, context, true);
    };
    EventEmitter2.prototype.removeListener = function removeListener(event, fn, context, once) {
      var evt = prefix ? prefix + event : event;
      if (!this._events[evt]) return this;
      if (!fn) {
        clearEvent(this, evt);
        return this;
      }
      var listeners = this._events[evt];
      if (listeners.fn) {
        if (listeners.fn === fn && (!once || listeners.once) && (!context || listeners.context === context)) {
          clearEvent(this, evt);
        }
      } else {
        for (var i = 0, events = [], length = listeners.length; i < length; i++) {
          if (listeners[i].fn !== fn || once && !listeners[i].once || context && listeners[i].context !== context) {
            events.push(listeners[i]);
          }
        }
        if (events.length) this._events[evt] = events.length === 1 ? events[0] : events;
        else clearEvent(this, evt);
      }
      return this;
    };
    EventEmitter2.prototype.removeAllListeners = function removeAllListeners(event) {
      var evt;
      if (event) {
        evt = prefix ? prefix + event : event;
        if (this._events[evt]) clearEvent(this, evt);
      } else {
        this._events = new Events();
        this._eventsCount = 0;
      }
      return this;
    };
    EventEmitter2.prototype.off = EventEmitter2.prototype.removeListener;
    EventEmitter2.prototype.addListener = EventEmitter2.prototype.on;
    EventEmitter2.prefixed = prefix;
    EventEmitter2.EventEmitter = EventEmitter2;
    if ("undefined" !== typeof module) {
      module.exports = EventEmitter2;
    }
  }
});

// node_modules/lodash/union.js
var require_union = __commonJS({
  "node_modules/lodash/union.js"(exports, module) {
    var baseFlatten = require_baseFlatten();
    var baseRest = require_baseRest();
    var baseUniq = require_baseUniq();
    var isArrayLikeObject = require_isArrayLikeObject();
    var union2 = baseRest(function(arrays) {
      return baseUniq(baseFlatten(arrays, 1, isArrayLikeObject, true));
    });
    module.exports = union2;
  }
});

// node_modules/lodash/defaults.js
var require_defaults = __commonJS({
  "node_modules/lodash/defaults.js"(exports, module) {
    var baseRest = require_baseRest();
    var eq = require_eq();
    var isIterateeCall = require_isIterateeCall();
    var keysIn = require_keysIn();
    var objectProto = Object.prototype;
    var hasOwnProperty = objectProto.hasOwnProperty;
    var defaults3 = baseRest(function(object, sources) {
      object = Object(object);
      var index = -1;
      var length = sources.length;
      var guard = length > 2 ? sources[2] : void 0;
      if (guard && isIterateeCall(sources[0], sources[1], guard)) {
        length = 1;
      }
      while (++index < length) {
        var source = sources[index];
        var props = keysIn(source);
        var propsIndex = -1;
        var propsLength = props.length;
        while (++propsIndex < propsLength) {
          var key = props[propsIndex];
          var value = object[key];
          if (value === void 0 || eq(value, objectProto[key]) && !hasOwnProperty.call(object, key)) {
            object[key] = source[key];
          }
        }
      }
      return object;
    });
    module.exports = defaults3;
  }
});

// node_modules/inflection/lib/inflection.js
var require_inflection = __commonJS({
  "node_modules/inflection/lib/inflection.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.pluralize = pluralize3;
    exports.singularize = singularize;
    exports.inflect = inflect2;
    exports.camelize = camelize;
    exports.underscore = underscore;
    exports.humanize = humanize2;
    exports.capitalize = capitalize;
    exports.dasherize = dasherize;
    exports.titleize = titleize;
    exports.demodulize = demodulize;
    exports.tableize = tableize;
    exports.classify = classify;
    exports.foreignKey = foreignKey;
    exports.ordinalize = ordinalize;
    exports.transform = transform2;
    var uncountableWords = [
      // 'access',
      "accommodation",
      "adulthood",
      "advertising",
      "advice",
      "aggression",
      "aid",
      "air",
      "aircraft",
      "alcohol",
      "anger",
      "applause",
      "arithmetic",
      // 'art',
      "assistance",
      "athletics",
      // 'attention',
      "bacon",
      "baggage",
      // 'ballet',
      // 'beauty',
      "beef",
      // 'beer',
      // 'behavior',
      "biology",
      // 'billiards',
      "blood",
      "botany",
      // 'bowels',
      "bread",
      // 'business',
      "butter",
      "carbon",
      "cardboard",
      "cash",
      "chalk",
      "chaos",
      "chess",
      "crossroads",
      "countryside",
      // 'damage',
      "dancing",
      // 'danger',
      "deer",
      // 'delight',
      // 'dessert',
      "dignity",
      "dirt",
      // 'distribution',
      "dust",
      "economics",
      "education",
      "electricity",
      // 'employment',
      // 'energy',
      "engineering",
      "enjoyment",
      // 'entertainment',
      "envy",
      "equipment",
      "ethics",
      "evidence",
      "evolution",
      // 'failure',
      // 'faith',
      "fame",
      "fiction",
      // 'fish',
      "flour",
      "flu",
      "food",
      // 'freedom',
      // 'fruit',
      "fuel",
      "fun",
      // 'funeral',
      "furniture",
      "gallows",
      "garbage",
      "garlic",
      // 'gas',
      "genetics",
      // 'glass',
      "gold",
      "golf",
      "gossip",
      // 'grass',
      "gratitude",
      "grief",
      // 'ground',
      "guilt",
      "gymnastics",
      // 'hair',
      "happiness",
      "hardware",
      "harm",
      "hate",
      "hatred",
      "health",
      "heat",
      // 'height',
      "help",
      "homework",
      "honesty",
      "honey",
      "hospitality",
      "housework",
      "humour",
      "hunger",
      "hydrogen",
      "ice",
      "importance",
      "inflation",
      "information",
      // 'injustice',
      "innocence",
      // 'intelligence',
      "iron",
      "irony",
      "jam",
      // 'jealousy',
      // 'jelly',
      "jewelry",
      // 'joy',
      "judo",
      // 'juice',
      // 'justice',
      "karate",
      // 'kindness',
      "knowledge",
      // 'labour',
      "lack",
      // 'land',
      "laughter",
      "lava",
      "leather",
      "leisure",
      "lightning",
      "linguine",
      "linguini",
      "linguistics",
      "literature",
      "litter",
      "livestock",
      "logic",
      "loneliness",
      // 'love',
      "luck",
      "luggage",
      "macaroni",
      "machinery",
      "magic",
      // 'mail',
      "management",
      "mankind",
      "marble",
      "mathematics",
      "mayonnaise",
      "measles",
      // 'meat',
      // 'metal',
      "methane",
      "milk",
      "minus",
      "money",
      // 'moose',
      "mud",
      "music",
      "mumps",
      "nature",
      "news",
      "nitrogen",
      "nonsense",
      "nurture",
      "nutrition",
      "obedience",
      "obesity",
      // 'oil',
      "oxygen",
      // 'paper',
      // 'passion',
      "pasta",
      "patience",
      // 'permission',
      "physics",
      "poetry",
      "pollution",
      "poverty",
      // 'power',
      "pride",
      // 'production',
      // 'progress',
      // 'pronunciation',
      "psychology",
      "publicity",
      "punctuation",
      // 'quality',
      // 'quantity',
      "quartz",
      "racism",
      // 'rain',
      // 'recreation',
      "relaxation",
      "reliability",
      "research",
      "respect",
      "revenge",
      "rice",
      "rubbish",
      "rum",
      "safety",
      // 'salad',
      // 'salt',
      // 'sand',
      // 'satire',
      "scenery",
      "seafood",
      "seaside",
      "series",
      "shame",
      "sheep",
      "shopping",
      // 'silence',
      "sleep",
      // 'slang'
      "smoke",
      "smoking",
      "snow",
      "soap",
      "software",
      "soil",
      // 'sorrow',
      // 'soup',
      "spaghetti",
      // 'speed',
      "species",
      // 'spelling',
      // 'sport',
      "steam",
      // 'strength',
      "stuff",
      "stupidity",
      // 'success',
      // 'sugar',
      "sunshine",
      "symmetry",
      // 'tea',
      "tennis",
      "thirst",
      "thunder",
      "timber",
      // 'time',
      // 'toast',
      // 'tolerance',
      // 'trade',
      "traffic",
      "transportation",
      // 'travel',
      "trust",
      // 'understanding',
      "underwear",
      "unemployment",
      "unity",
      // 'usage',
      "validity",
      "veal",
      "vegetation",
      "vegetarianism",
      "vengeance",
      "violence",
      // 'vision',
      "vitality",
      "warmth",
      // 'water',
      "wealth",
      "weather",
      // 'weight',
      "welfare",
      "wheat",
      // 'whiskey',
      // 'width',
      "wildlife",
      // 'wine',
      "wisdom",
      // 'wood',
      // 'wool',
      // 'work',
      // 'yeast',
      "yoga",
      "zinc",
      "zoology"
    ];
    var regex2 = {
      plural: {
        men: new RegExp("^(m|wom)en$", "gi"),
        people: new RegExp("(pe)ople$", "gi"),
        children: new RegExp("(child)ren$", "gi"),
        tia: new RegExp("([ti])a$", "gi"),
        analyses: new RegExp("((a)naly|(b)a|(d)iagno|(p)arenthe|(p)rogno|(s)ynop|(t)he)ses$", "gi"),
        databases: new RegExp("(database)s$", "gi"),
        drives: new RegExp("(drive)s$", "gi"),
        hives: new RegExp("(hi|ti)ves$", "gi"),
        curves: new RegExp("(curve)s$", "gi"),
        lrves: new RegExp("([lr])ves$", "gi"),
        aves: new RegExp("([a])ves$", "gi"),
        foves: new RegExp("([^fo])ves$", "gi"),
        movies: new RegExp("(m)ovies$", "gi"),
        aeiouyies: new RegExp("([^aeiouy]|qu)ies$", "gi"),
        series: new RegExp("(s)eries$", "gi"),
        xes: new RegExp("(x|ch|ss|sh)es$", "gi"),
        mice: new RegExp("([m|l])ice$", "gi"),
        buses: new RegExp("(bus)es$", "gi"),
        oes: new RegExp("(o)es$", "gi"),
        shoes: new RegExp("(shoe)s$", "gi"),
        crises: new RegExp("(cris|ax|test)es$", "gi"),
        octopuses: new RegExp("(octop|vir)uses$", "gi"),
        aliases: new RegExp("(alias|canvas|status|campus)es$", "gi"),
        summonses: new RegExp("^(summons|bonus)es$", "gi"),
        oxen: new RegExp("^(ox)en", "gi"),
        matrices: new RegExp("(matr)ices$", "gi"),
        vertices: new RegExp("(vert|ind)ices$", "gi"),
        feet: new RegExp("^feet$", "gi"),
        teeth: new RegExp("^teeth$", "gi"),
        geese: new RegExp("^geese$", "gi"),
        quizzes: new RegExp("(quiz)zes$", "gi"),
        whereases: new RegExp("^(whereas)es$", "gi"),
        criteria: new RegExp("^(criteri)a$", "gi"),
        genera: new RegExp("^genera$", "gi"),
        ss: new RegExp("ss$", "gi"),
        s: new RegExp("s$", "gi")
      },
      singular: {
        man: new RegExp("^(m|wom)an$", "gi"),
        person: new RegExp("(pe)rson$", "gi"),
        child: new RegExp("(child)$", "gi"),
        drive: new RegExp("(drive)$", "gi"),
        ox: new RegExp("^(ox)$", "gi"),
        axis: new RegExp("(ax|test)is$", "gi"),
        octopus: new RegExp("(octop|vir)us$", "gi"),
        alias: new RegExp("(alias|status|canvas|campus)$", "gi"),
        summons: new RegExp("^(summons|bonus)$", "gi"),
        bus: new RegExp("(bu)s$", "gi"),
        buffalo: new RegExp("(buffal|tomat|potat)o$", "gi"),
        tium: new RegExp("([ti])um$", "gi"),
        sis: new RegExp("sis$", "gi"),
        ffe: new RegExp("(?:([^f])fe|([lr])f)$", "gi"),
        focus: new RegExp("^(focus)$", "gi"),
        hive: new RegExp("(hi|ti)ve$", "gi"),
        aeiouyy: new RegExp("([^aeiouy]|qu)y$", "gi"),
        x: new RegExp("(x|ch|ss|sh)$", "gi"),
        matrix: new RegExp("(matr)ix$", "gi"),
        vertex: new RegExp("(vert|ind)ex$", "gi"),
        mouse: new RegExp("([m|l])ouse$", "gi"),
        foot: new RegExp("^foot$", "gi"),
        tooth: new RegExp("^tooth$", "gi"),
        goose: new RegExp("^goose$", "gi"),
        quiz: new RegExp("(quiz)$", "gi"),
        whereas: new RegExp("^(whereas)$", "gi"),
        criterion: new RegExp("^(criteri)on$", "gi"),
        genus: new RegExp("^genus$", "gi"),
        s: new RegExp("s$", "gi"),
        common: new RegExp("$", "gi")
      }
    };
    var pluralRules = [
      // do not replace if its already a plural word
      [regex2.plural.men],
      [regex2.plural.people],
      [regex2.plural.children],
      [regex2.plural.tia],
      [regex2.plural.analyses],
      [regex2.plural.databases],
      [regex2.plural.drives],
      [regex2.plural.hives],
      [regex2.plural.curves],
      [regex2.plural.lrves],
      [regex2.plural.foves],
      [regex2.plural.aeiouyies],
      [regex2.plural.series],
      [regex2.plural.movies],
      [regex2.plural.xes],
      [regex2.plural.mice],
      [regex2.plural.buses],
      [regex2.plural.oes],
      [regex2.plural.shoes],
      [regex2.plural.crises],
      [regex2.plural.octopuses],
      [regex2.plural.aliases],
      [regex2.plural.summonses],
      [regex2.plural.oxen],
      [regex2.plural.matrices],
      [regex2.plural.feet],
      [regex2.plural.teeth],
      [regex2.plural.geese],
      [regex2.plural.quizzes],
      [regex2.plural.whereases],
      [regex2.plural.criteria],
      [regex2.plural.genera],
      // original rule
      [regex2.singular.man, "$1en"],
      [regex2.singular.person, "$1ople"],
      [regex2.singular.child, "$1ren"],
      [regex2.singular.drive, "$1s"],
      [regex2.singular.ox, "$1en"],
      [regex2.singular.axis, "$1es"],
      [regex2.singular.octopus, "$1uses"],
      [regex2.singular.alias, "$1es"],
      [regex2.singular.summons, "$1es"],
      [regex2.singular.bus, "$1ses"],
      [regex2.singular.buffalo, "$1oes"],
      [regex2.singular.tium, "$1a"],
      [regex2.singular.sis, "ses"],
      [regex2.singular.ffe, "$1$2ves"],
      [regex2.singular.focus, "$1es"],
      [regex2.singular.hive, "$1ves"],
      [regex2.singular.aeiouyy, "$1ies"],
      [regex2.singular.matrix, "$1ices"],
      [regex2.singular.vertex, "$1ices"],
      [regex2.singular.x, "$1es"],
      [regex2.singular.mouse, "$1ice"],
      [regex2.singular.foot, "feet"],
      [regex2.singular.tooth, "teeth"],
      [regex2.singular.goose, "geese"],
      [regex2.singular.quiz, "$1zes"],
      [regex2.singular.whereas, "$1es"],
      [regex2.singular.criterion, "$1a"],
      [regex2.singular.genus, "genera"],
      [regex2.singular.s, "s"],
      [regex2.singular.common, "s"]
    ];
    var singularRules = [
      // do not replace if its already a singular word
      [regex2.singular.man],
      [regex2.singular.person],
      [regex2.singular.child],
      [regex2.singular.drive],
      [regex2.singular.ox],
      [regex2.singular.axis],
      [regex2.singular.octopus],
      [regex2.singular.alias],
      [regex2.singular.summons],
      [regex2.singular.bus],
      [regex2.singular.buffalo],
      [regex2.singular.tium],
      [regex2.singular.sis],
      [regex2.singular.ffe],
      [regex2.singular.focus],
      [regex2.singular.hive],
      [regex2.singular.aeiouyy],
      [regex2.singular.x],
      [regex2.singular.matrix],
      [regex2.singular.mouse],
      [regex2.singular.foot],
      [regex2.singular.tooth],
      [regex2.singular.goose],
      [regex2.singular.quiz],
      [regex2.singular.whereas],
      [regex2.singular.criterion],
      [regex2.singular.genus],
      // original rule
      [regex2.plural.men, "$1an"],
      [regex2.plural.people, "$1rson"],
      [regex2.plural.children, "$1"],
      [regex2.plural.databases, "$1"],
      [regex2.plural.drives, "$1"],
      [regex2.plural.genera, "genus"],
      [regex2.plural.criteria, "$1on"],
      [regex2.plural.tia, "$1um"],
      [regex2.plural.analyses, "$1$2sis"],
      [regex2.plural.hives, "$1ve"],
      [regex2.plural.curves, "$1"],
      [regex2.plural.lrves, "$1f"],
      [regex2.plural.aves, "$1ve"],
      [regex2.plural.foves, "$1fe"],
      [regex2.plural.movies, "$1ovie"],
      [regex2.plural.aeiouyies, "$1y"],
      [regex2.plural.series, "$1eries"],
      [regex2.plural.xes, "$1"],
      [regex2.plural.mice, "$1ouse"],
      [regex2.plural.buses, "$1"],
      [regex2.plural.oes, "$1"],
      [regex2.plural.shoes, "$1"],
      [regex2.plural.crises, "$1is"],
      [regex2.plural.octopuses, "$1us"],
      [regex2.plural.aliases, "$1"],
      [regex2.plural.summonses, "$1"],
      [regex2.plural.oxen, "$1"],
      [regex2.plural.matrices, "$1ix"],
      [regex2.plural.vertices, "$1ex"],
      [regex2.plural.feet, "foot"],
      [regex2.plural.teeth, "tooth"],
      [regex2.plural.geese, "goose"],
      [regex2.plural.quizzes, "$1"],
      [regex2.plural.whereases, "$1"],
      [regex2.plural.ss, "ss"],
      [regex2.plural.s, ""]
    ];
    var nonTitlecasedWords = [
      "and",
      "or",
      "nor",
      "a",
      "an",
      "the",
      "so",
      "but",
      "to",
      "of",
      "at",
      "by",
      "from",
      "into",
      "on",
      "onto",
      "off",
      "out",
      "in",
      "over",
      "with",
      "for"
    ];
    var idSuffix = new RegExp("(_ids|_id)$", "g");
    var underbar = new RegExp("_", "g");
    var spaceOrUnderbar = new RegExp("[ _]", "g");
    var uppercase = new RegExp("([A-Z])", "g");
    var underbarPrefix = new RegExp("^_");
    function applyRules(str, rules, skip, override) {
      if (override) {
        return override;
      } else {
        if (skip.includes(str.toLocaleLowerCase())) {
          return str;
        }
        for (const rule of rules) {
          if (str.match(rule[0])) {
            if (rule[1] !== void 0) {
              return str.replace(rule[0], rule[1]);
            }
            return str;
          }
        }
      }
      return str;
    }
    function pluralize3(str, plural) {
      return applyRules(str, pluralRules, uncountableWords, plural);
    }
    function singularize(str, singular) {
      return applyRules(str, singularRules, uncountableWords, singular);
    }
    function inflect2(str, count, singular, plural) {
      if (isNaN(count))
        return str;
      if (count === 1) {
        return applyRules(str, singularRules, uncountableWords, singular);
      } else {
        return applyRules(str, pluralRules, uncountableWords, plural);
      }
    }
    function camelize(str, lowFirstLetter) {
      const strPath = str.split("/");
      const j = strPath.length;
      let strArr, k, l, first;
      for (let i = 0; i < j; i++) {
        strArr = strPath[i].split("_");
        k = 0;
        l = strArr.length;
        for (; k < l; k++) {
          if (k !== 0) {
            strArr[k] = strArr[k].toLowerCase();
          }
          first = strArr[k].charAt(0);
          first = lowFirstLetter && i === 0 && k === 0 ? first.toLowerCase() : first.toUpperCase();
          strArr[k] = first + strArr[k].substring(1);
        }
        strPath[i] = strArr.join("");
      }
      return strPath.join("::");
    }
    function underscore(str, allUpperCase) {
      if (allUpperCase && str === str.toUpperCase())
        return str;
      const strPath = str.split("::");
      const j = strPath.length;
      for (let i = 0; i < j; i++) {
        strPath[i] = strPath[i].replace(uppercase, "_$1");
        strPath[i] = strPath[i].replace(underbarPrefix, "");
      }
      return strPath.join("/").toLowerCase();
    }
    function humanize2(str, lowFirstLetter) {
      str = str.toLowerCase();
      str = str.replace(idSuffix, "");
      str = str.replace(underbar, " ");
      if (!lowFirstLetter) {
        str = capitalize(str);
      }
      return str;
    }
    function capitalize(str) {
      str = str.toLowerCase();
      return str.substring(0, 1).toUpperCase() + str.substring(1);
    }
    function dasherize(str) {
      return str.replace(spaceOrUnderbar, "-");
    }
    function titleize(str) {
      str = str.toLowerCase().replace(underbar, " ");
      const strArr = str.split(" ");
      const j = strArr.length;
      let d, l;
      for (let i = 0; i < j; i++) {
        d = strArr[i].split("-");
        l = d.length;
        for (let k = 0; k < l; k++) {
          if (nonTitlecasedWords.indexOf(d[k].toLowerCase()) < 0) {
            d[k] = capitalize(d[k]);
          }
        }
        strArr[i] = d.join("-");
      }
      str = strArr.join(" ");
      str = str.substring(0, 1).toUpperCase() + str.substring(1);
      return str;
    }
    function demodulize(str) {
      const strArr = str.split("::");
      return strArr[strArr.length - 1];
    }
    function tableize(str) {
      str = underscore(str);
      str = pluralize3(str);
      return str;
    }
    function classify(str) {
      str = camelize(str);
      str = singularize(str);
      return str;
    }
    function foreignKey(str, dropIdUbar) {
      str = demodulize(str);
      str = underscore(str) + (dropIdUbar ? "" : "_") + "id";
      return str;
    }
    function ordinalize(str) {
      const strArr = str.split(" ");
      const j = strArr.length;
      for (let i = 0; i < j; i++) {
        const k = parseInt(strArr[i], 10);
        if (!isNaN(k)) {
          const ltd = strArr[i].substring(strArr[i].length - 2);
          const ld = strArr[i].substring(strArr[i].length - 1);
          let suf = "th";
          if (ltd != "11" && ltd != "12" && ltd != "13") {
            if (ld === "1") {
              suf = "st";
            } else if (ld === "2") {
              suf = "nd";
            } else if (ld === "3") {
              suf = "rd";
            }
          }
          strArr[i] += suf;
        }
      }
      return strArr.join(" ");
    }
    var transformFunctions = {
      pluralize: pluralize3,
      singularize,
      camelize,
      underscore,
      humanize: humanize2,
      capitalize,
      dasherize,
      titleize,
      demodulize,
      tableize,
      classify,
      foreignKey,
      ordinalize
    };
    function transform2(str, arr) {
      const j = arr.length;
      for (let i = 0; i < j; i++) {
        const method = arr[i];
        const methodFn = transformFunctions[method];
        if (methodFn) {
          str = methodFn(str);
        }
      }
      return str;
    }
  }
});

// node_modules/ra-core/src/auth/useAuthProvider.ts
var import_react2 = __toESM(require_react());

// node_modules/ra-core/src/auth/AuthContext.tsx
var import_react = __toESM(require_react());
var AuthContext = (0, import_react.createContext)(void 0);
AuthContext.displayName = "AuthContext";

// node_modules/ra-core/src/auth/useAuthProvider.ts
var defaultAuthParams = {
  loginUrl: "/login",
  afterLoginUrl: "/"
};
var useAuthProvider = () => (0, import_react2.useContext)(AuthContext);
var useAuthProvider_default = useAuthProvider;

// node_modules/ra-core/src/auth/useAuthState.ts
var import_react150 = __toESM(require_react());

// node_modules/ra-core/src/auth/useLogout.ts
var import_react149 = __toESM(require_react());

// node_modules/ra-core/src/store/localStorageStore.ts
var RA_STORE = "RaStore";
var testLocalStorage = () => {
  if (typeof window === "undefined" || window.localStorage == void 0) {
    return false;
  }
  try {
    window.localStorage.setItem("test", "test");
    window.localStorage.removeItem("test");
    return true;
  } catch (e) {
    return false;
  }
};
var localStorageAvailable = testLocalStorage();
var localStorageStore = (version = "1", appKey = "") => {
  const prefix = `${RA_STORE}${appKey}`;
  const prefixLength = prefix.length;
  const subscriptions = {};
  const publish = (key, value) => {
    Object.keys(subscriptions).forEach((id) => {
      if (!subscriptions[id]) return;
      if (subscriptions[id].key === key) {
        subscriptions[id].callback(value);
      }
    });
  };
  const onLocalStorageChange = (event) => {
    var _a;
    if (((_a = event.key) == null ? void 0 : _a.substring(0, prefixLength)) !== prefix) {
      return;
    }
    const key = event.key.substring(prefixLength + 1);
    const value = event.newValue ? tryParse(event.newValue) : void 0;
    Object.keys(subscriptions).forEach((id) => {
      if (!subscriptions[id]) return;
      if (subscriptions[id].key === key) {
        if (value === null) {
          subscriptions[id].callback(void 0);
        } else {
          subscriptions[id].callback(
            value == null ? void 0 : value
          );
        }
      }
    });
  };
  return {
    setup: () => {
      if (localStorageAvailable) {
        const storedVersion = getStorage().getItem(`${prefix}.version`);
        if (storedVersion && storedVersion !== version) {
          const storage = getStorage();
          Object.keys(storage).forEach((key) => {
            if (key.startsWith(prefix)) {
              storage.removeItem(key);
            }
          });
        }
        getStorage().setItem(`${prefix}.version`, version);
        window.addEventListener("storage", onLocalStorageChange);
      }
    },
    teardown: () => {
      if (localStorageAvailable) {
        window.removeEventListener("storage", onLocalStorageChange);
      }
    },
    getItem(key, defaultValue) {
      const valueFromStorage = getStorage().getItem(`${prefix}.${key}`);
      return valueFromStorage == null ? defaultValue : tryParse(valueFromStorage);
    },
    setItem(key, value) {
      if (value === void 0) {
        getStorage().removeItem(`${prefix}.${key}`);
      } else {
        getStorage().setItem(`${prefix}.${key}`, JSON.stringify(value));
      }
      publish(key, value);
    },
    removeItem(key) {
      getStorage().removeItem(`${prefix}.${key}`);
      publish(key, void 0);
    },
    removeItems(keyPrefix) {
      const storage = getStorage();
      Object.keys(storage).forEach((key) => {
        if (key.startsWith(`${prefix}.${keyPrefix}`)) {
          storage.removeItem(key);
          const publishKey = key.substring(prefixLength + 1);
          publish(publishKey, void 0);
        }
      });
    },
    reset() {
      const storage = getStorage();
      Object.keys(storage).forEach((key) => {
        if (key.startsWith(prefix)) {
          storage.removeItem(key);
          const publishKey = key.substring(prefixLength + 1);
          publish(publishKey, void 0);
        }
      });
    },
    subscribe: (key, callback) => {
      const id = Math.random().toString();
      subscriptions[id] = {
        key,
        callback
      };
      return () => {
        delete subscriptions[id];
      };
    }
  };
};
var tryParse = (value) => {
  try {
    return JSON.parse(value);
  } catch (e) {
    return value;
  }
};
var LocalStorageShim = class {
  constructor() {
    __publicField(this, "valuesMap", /* @__PURE__ */ new Map());
  }
  getItem(key) {
    if (this.valuesMap.has(key)) {
      return String(this.valuesMap.get(key));
    }
    return null;
  }
  setItem(key, value) {
    this.valuesMap.set(key, value);
  }
  removeItem(key) {
    this.valuesMap.delete(key);
  }
  removeItems(keyPrefix) {
    this.valuesMap.forEach((value, key) => {
      if (key.startsWith(keyPrefix)) {
        this.valuesMap.delete(key);
      }
    });
  }
  clear() {
    this.valuesMap.clear();
  }
  key(i) {
    if (arguments.length === 0) {
      throw new TypeError(
        "Failed to execute 'key' on 'Storage': 1 argument required, but only 0 present."
      );
    }
    const arr = Array.from(this.valuesMap.keys());
    return arr[i];
  }
  get length() {
    return this.valuesMap.size;
  }
};
var memoryStorage = new LocalStorageShim();
var getStorage = () => {
  return localStorageAvailable ? window.localStorage : memoryStorage;
};

// node_modules/ra-core/src/store/memoryStore.tsx
var import_set = __toESM(require_set());
var import_unset = __toESM(require_unset());
var import_get = __toESM(require_get());
var memoryStore = (storage = {}) => {
  const subscriptions = {};
  const publish = (key, value) => {
    Object.keys(subscriptions).forEach((id) => {
      if (!subscriptions[id]) return;
      if (subscriptions[id].key === key) {
        subscriptions[id].callback(value);
      }
    });
  };
  return {
    setup: () => {
    },
    teardown: () => {
      Object.keys(storage).forEach((key) => delete storage[key]);
    },
    getItem(key, defaultValue) {
      return (0, import_get.default)(storage, key, defaultValue);
    },
    setItem(key, value) {
      (0, import_set.default)(storage, key, value);
      publish(key, value);
    },
    removeItem(key) {
      (0, import_unset.default)(storage, key);
      publish(key, void 0);
    },
    removeItems(keyPrefix) {
      const flatStorage = flatten(storage);
      Object.keys(flatStorage).forEach((key) => {
        if (!key.startsWith(keyPrefix)) {
          return;
        }
        (0, import_unset.default)(storage, key);
        publish(key, void 0);
      });
    },
    reset() {
      const flatStorage = flatten(storage);
      Object.keys(flatStorage).forEach((key) => {
        (0, import_unset.default)(storage, key);
        publish(key, void 0);
      });
    },
    subscribe: (key, callback) => {
      const id = Math.random().toString();
      subscriptions[id] = {
        key,
        callback
      };
      return () => {
        delete subscriptions[id];
      };
    }
  };
};
var flatten = (data) => {
  var result = {};
  function doFlatten(current, prop) {
    if (Object(current) !== current) {
      result[prop] = current;
    } else if (Array.isArray(current)) {
      result[prop] = current;
    } else {
      var isEmpty3 = true;
      for (var p in current) {
        isEmpty3 = false;
        doFlatten(current[p], prop ? prop + "." + p : p);
      }
      if (isEmpty3 && prop) result[prop] = {};
    }
  }
  doFlatten(data, "");
  return result;
};

// node_modules/ra-core/src/store/StoreContext.tsx
var import_react3 = __toESM(require_react());
var defaultStore = memoryStore();
var StoreContext = (0, import_react3.createContext)(defaultStore);

// node_modules/ra-core/src/store/StoreContextProvider.tsx
var import_react4 = __toESM(require_react());
var import_jsx_runtime = __toESM(require_jsx_runtime());
var StoreContextProvider = ({
  value: Store2,
  children
}) => {
  (0, import_react4.useEffect)(() => {
    Store2.setup();
    return () => {
      Store2.teardown();
    };
  }, [Store2]);
  return (0, import_jsx_runtime.jsx)(StoreContext.Provider, { value: Store2, children });
};

// node_modules/ra-core/src/store/StoreSetter.tsx
var import_react6 = __toESM(require_react());

// node_modules/ra-core/src/store/useStoreContext.ts
var import_react5 = __toESM(require_react());
var useStoreContext = () => (0, import_react5.useContext)(StoreContext);

// node_modules/ra-core/src/store/StoreSetter.tsx
var import_jsx_runtime2 = __toESM(require_jsx_runtime());
var StoreSetter = ({ value, name, children }) => {
  const { setItem } = useStoreContext();
  (0, import_react6.useEffect)(() => {
    setItem(name, value);
  }, [name, setItem, value]);
  return (0, import_jsx_runtime2.jsx)(import_jsx_runtime2.Fragment, { children });
};

// node_modules/ra-core/src/store/useStore.ts
var import_react146 = __toESM(require_react());
var import_isEqual9 = __toESM(require_isEqual());

// node_modules/ra-core/src/util/escapePath.ts
var escapePath_default = (url) => url.replace(/(\(|\))/g, "\\$1");

// node_modules/ra-core/src/util/FieldTitle.tsx
var import_react140 = __toESM(require_react());

// node_modules/ra-core/src/i18n/substituteTokens.ts
var substituteTokens = (template, data) => template && data ? String.prototype.replace.call(
  template,
  defaultTokenRegex,
  function(expression, argument) {
    if (!data.hasOwnProperty(argument) || data[argument] == null) {
      return expression;
    }
    return data[argument];
  }
) : template;
var defaultTokenRegex = /%\{(.*?)\}/g;

// node_modules/ra-core/src/i18n/TestTranslationProvider.tsx
var import_get2 = __toESM(require_get());

// node_modules/ra-core/src/i18n/I18nContextProvider.tsx
var import_react14 = __toESM(require_react());

// node_modules/ra-core/src/i18n/I18nContext.ts
var import_react7 = __toESM(require_react());
var defaultI18nProvider = {
  translate: (key, options) => (options == null ? void 0 : options._) ? substituteTokens(options._, options) : substituteTokens(key, options),
  changeLocale: () => Promise.resolve(),
  getLocale: () => "en"
};
var I18nContext = (0, import_react7.createContext)(defaultI18nProvider);
I18nContext.displayName = "I18nContext";

// node_modules/ra-core/src/notification/AddNotificationContext.tsx
var import_react8 = __toESM(require_react());
var AddNotificationContext = (0, import_react8.createContext)(() => {
});

// node_modules/ra-core/src/notification/NotificationContext.ts
var import_react9 = __toESM(require_react());
var NotificationContext = (0, import_react9.createContext)({
  notifications: [],
  addNotification: () => {
  },
  takeNotification: () => {
  },
  resetNotifications: () => {
  },
  setNotifications: () => {
  }
});

// node_modules/ra-core/src/notification/NotificationContextProvider.tsx
var import_react10 = __toESM(require_react());
var import_jsx_runtime3 = __toESM(require_jsx_runtime());
var NotificationContextProvider = ({ children }) => {
  const [notifications, setNotifications] = (0, import_react10.useState)(
    []
  );
  const addNotification = (0, import_react10.useCallback)((notification) => {
    setNotifications((notifications2) => [...notifications2, notification]);
  }, []);
  const takeNotification = (0, import_react10.useCallback)(() => {
    if (notifications.length === 0) return;
    const [notification, ...rest] = notifications;
    setNotifications(rest);
    return notification;
  }, [notifications]);
  const resetNotifications = (0, import_react10.useCallback)(() => {
    setNotifications([]);
  }, []);
  const contextValue = (0, import_react10.useMemo)(
    () => ({
      notifications,
      addNotification,
      takeNotification,
      resetNotifications,
      setNotifications
    }),
    [notifications]
    // eslint-disable-line react-hooks/exhaustive-deps
  );
  return (0, import_jsx_runtime3.jsx)(NotificationContext.Provider, { value: contextValue, children: (0, import_jsx_runtime3.jsx)(AddNotificationContext.Provider, { value: addNotification, children }) });
};

// node_modules/ra-core/src/notification/useAddNotificationContext.tsx
var import_react11 = __toESM(require_react());
var useAddNotificationContext = () => (0, import_react11.useContext)(AddNotificationContext);

// node_modules/ra-core/src/notification/useNotificationContext.ts
var import_react12 = __toESM(require_react());
var useNotificationContext = () => (0, import_react12.useContext)(NotificationContext);

// node_modules/ra-core/src/notification/useNotify.ts
var import_react13 = __toESM(require_react());
var useNotify = () => {
  const addNotification = useAddNotificationContext();
  return (0, import_react13.useCallback)(
    (message, options = {}) => {
      const { type: messageType = "info", ...notificationOptions } = options;
      addNotification({
        message,
        type: messageType,
        notificationOptions
      });
    },
    [addNotification]
  );
};

// node_modules/ra-core/src/i18n/I18nContextProvider.tsx
var import_jsx_runtime4 = __toESM(require_jsx_runtime());
var I18nContextProvider = ({
  value = defaulti18nContext,
  children
}) => {
  const [locale] = useStore("locale");
  const notify = useNotify();
  const [key, setKey] = (0, import_react14.useState)(0);
  const [isInitialized, setInitialized] = (0, import_react14.useState)(
    locale === value.getLocale()
  );
  (0, import_react14.useEffect)(() => {
    if (locale && value.getLocale() !== locale) {
      new Promise((resolve) => {
        resolve(value.changeLocale(locale));
      }).then(() => {
        setKey((key2) => key2 + 1);
        setInitialized(true);
      }).catch((error) => {
        setInitialized(true);
        notify("ra.notification.i18n_error", { type: "error" });
        console.error(error);
      });
    } else {
      setInitialized(true);
    }
  }, [value, locale, notify]);
  return isInitialized ? (0, import_jsx_runtime4.jsx)(I18nContext.Provider, { value, children }, key) : null;
};
var defaulti18nContext = {
  translate: (x) => x,
  changeLocale: () => Promise.resolve(),
  getLocale: () => "en"
};

// node_modules/ra-core/src/i18n/TestTranslationProvider.tsx
var import_jsx_runtime5 = __toESM(require_jsx_runtime());
var TestTranslationProvider = ({
  translate,
  messages,
  children
}) => (0, import_jsx_runtime5.jsx)(I18nContextProvider, { value: testI18nProvider({ translate, messages }), children });
var testI18nProvider = ({
  translate,
  messages
} = {}) => {
  return {
    translate: messages ? (key, options) => {
      const message = (0, import_get2.default)(messages, key);
      return message ? typeof message === "function" ? message(options) : message : (options == null ? void 0 : options._) || key;
    } : translate || ((key) => key),
    changeLocale: () => Promise.resolve(),
    getLocale: () => "en"
  };
};

// node_modules/ra-core/src/i18n/TranslatableContext.ts
var import_react15 = __toESM(require_react());
var TranslatableContext = (0, import_react15.createContext)(void 0);

// node_modules/ra-core/src/i18n/TranslatableContextProvider.tsx
var import_jsx_runtime6 = __toESM(require_jsx_runtime());
var TranslatableContextProvider = ({
  children,
  value
}) => {
  return (0, import_jsx_runtime6.jsx)(TranslatableContext.Provider, { value, children });
};

// node_modules/ra-core/src/i18n/TranslationUtils.ts
var import_merge = __toESM(require_merge());
var resolveBrowserLocale = (defaultLocale, options) => {
  const { language, browserLanguage, userLanguage } = window.navigator;
  const locale = language || browserLanguage || userLanguage || defaultLocale || DEFAULT_LOCALE;
  return (options == null ? void 0 : options.fullLocale) ? locale : locale.split("-")[0];
};
var mergeTranslations = (...translationsModules) => (0, import_merge.default)({}, ...translationsModules);

// node_modules/ra-core/src/i18n/useLocaleState.tsx
var import_react17 = __toESM(require_react());

// node_modules/ra-core/src/i18n/useI18nProvider.ts
var import_react16 = __toESM(require_react());
var useI18nProvider = () => (0, import_react16.useContext)(I18nContext);

// node_modules/ra-core/src/i18n/useLocaleState.tsx
var useLocaleState = () => {
  const i18nProvider = useI18nProvider();
  const defaultLocale = (0, import_react17.useMemo)(
    () => i18nProvider.getLocale(),
    [i18nProvider]
  );
  return useStore("locale", defaultLocale);
};

// node_modules/ra-core/src/i18n/useLocale.tsx
var useLocale = () => {
  const [locale] = useLocaleState();
  return locale;
};

// node_modules/ra-core/src/i18n/useLocales.ts
var import_react18 = __toESM(require_react());
var useLocales = (options) => {
  const i18nProvider = useI18nProvider();
  const locales = (0, import_react18.useMemo)(
    () => (i18nProvider == null ? void 0 : i18nProvider.getLocales) ? i18nProvider == null ? void 0 : i18nProvider.getLocales() : [],
    [i18nProvider]
  );
  return (options == null ? void 0 : options.locales) ?? locales;
};

// node_modules/ra-core/src/i18n/useSetLocale.tsx
var useSetLocale = () => {
  const [, setLocale] = useLocaleState();
  return setLocale;
};

// node_modules/ra-core/src/i18n/useTranslatable.ts
var import_react19 = __toESM(require_react());
var import_set2 = __toESM(require_set());
var import_get3 = __toESM(require_get());
var import_cloneDeep = __toESM(require_cloneDeep());
var useTranslatable = (options) => {
  const [localeFromUI] = useLocaleState();
  const { defaultLocale = localeFromUI, locales } = options;
  const [selectedLocale, setSelectedLocale] = (0, import_react19.useState)(defaultLocale);
  const context = (0, import_react19.useMemo)(
    () => ({
      locales,
      selectedLocale,
      selectLocale: setSelectedLocale,
      getRecordForLocale
    }),
    [locales, selectedLocale]
  );
  return context;
};
var getRecordForLocale = (record, locale) => {
  if (!record) {
    return record;
  }
  const paths = getRecordPaths(record);
  const recordForLocale = paths.reduce((acc, path) => {
    if (path.includes(locale)) {
      const pathWithoutLocale = path.slice(0, -1);
      const value = (0, import_get3.default)(record, path);
      return (0, import_set2.default)(acc, pathWithoutLocale, value);
    }
    return acc;
  }, (0, import_cloneDeep.default)(record));
  return recordForLocale;
};
var getRecordPaths = (record = {}, path = []) => {
  return Object.entries(record).reduce((acc, [key, value]) => {
    if (value !== null && typeof value === "object") {
      return [
        ...acc,
        [...path, key],
        ...getRecordPaths(value, [...path, key])
      ];
    }
    if (Array.isArray(value)) {
      return value.reduce(
        (acc2, item, index) => [
          ...acc2,
          ...getRecordPaths(item, [...path, key, `${index}`])
        ],
        acc
      );
    }
    return [...acc, [...path, key]];
  }, []);
};

// node_modules/ra-core/src/i18n/useTranslatableContext.ts
var import_react20 = __toESM(require_react());
var useTranslatableContext = () => {
  const context = (0, import_react20.useContext)(TranslatableContext);
  if (!context) {
    throw new Error(
      "useTranslatableContext must be used inside a TranslatableContextProvider"
    );
  }
  return context;
};

// node_modules/ra-core/src/i18n/useTranslate.ts
var import_react21 = __toESM(require_react());
var useTranslate = () => {
  const i18nProvider = useI18nProvider();
  const translate = (0, import_react21.useCallback)(
    (key, options) => i18nProvider.translate(key, options),
    // update the hook each time the locale changes
    [i18nProvider]
    // eslint-disable-line react-hooks/exhaustive-deps
  );
  return i18nProvider ? translate : identity;
};
var identity = (key) => key;

// node_modules/ra-core/src/i18n/useTranslateLabel.ts
var import_react139 = __toESM(require_react());

// node_modules/ra-core/src/core/CoreAdminContext.tsx
var import_react126 = __toESM(require_react());

// node_modules/ra-core/src/routing/BasenameContext.ts
var import_react22 = __toESM(require_react());
var BasenameContext = (0, import_react22.createContext)("");

// node_modules/ra-core/src/routing/BasenameContextProvider.tsx
var import_jsx_runtime7 = __toESM(require_jsx_runtime());
var BasenameContextProvider = ({ children, basename }) => (0, import_jsx_runtime7.jsx)(BasenameContext.Provider, { value: basename, children });

// node_modules/ra-core/src/routing/AdminRouter.tsx
var import_jsx_runtime8 = __toESM(require_jsx_runtime());
var AdminRouter = ({ basename = "", children }) => {
  const isInRouter = useInRouterContext();
  const Router = isInRouter ? DummyRouter : InternalRouter;
  return (0, import_jsx_runtime8.jsx)(BasenameContextProvider, { basename: isInRouter ? basename : "", children: (0, import_jsx_runtime8.jsx)(Router, { basename, children }) });
};
var DummyRouter = ({
  children
}) => (0, import_jsx_runtime8.jsx)(import_jsx_runtime8.Fragment, { children });
var routerProviderFuture = { v7_startTransition: false, v7_relativeSplatPath: false };
var InternalRouter = ({
  children,
  basename
}) => {
  const router = createHashRouter([{ path: "*", element: (0, import_jsx_runtime8.jsx)(import_jsx_runtime8.Fragment, { children }) }], {
    basename,
    future: {
      v7_fetcherPersist: false,
      v7_normalizeFormMethod: false,
      v7_partialHydration: false,
      v7_relativeSplatPath: false,
      v7_skipActionErrorRevalidation: false
    }
  });
  return (0, import_jsx_runtime8.jsx)(RouterProvider2, { router, future: routerProviderFuture });
};

// node_modules/ra-core/src/routing/useRestoreScrollPosition.ts
var import_react23 = __toESM(require_react());
var import_debounce = __toESM(require_debounce());
var useRestoreScrollPosition = (storeKey, debounceMs = 250) => {
  const [position, setPosition] = useTrackScrollPosition(
    storeKey,
    debounceMs
  );
  const location = useLocation();
  (0, import_react23.useEffect)(() => {
    var _a;
    if (position != null && ((_a = location.state) == null ? void 0 : _a._scrollToTop) !== true) {
      setPosition(void 0);
      window.scrollTo(0, position);
    }
  }, []);
};
var useTrackScrollPosition = (storeKey, debounceMs = 250) => {
  const [position, setPosition] = useStore(storeKey);
  (0, import_react23.useEffect)(() => {
    if (typeof window === "undefined") {
      return;
    }
    const handleScroll = (0, import_debounce.default)(() => {
      setPosition(window.scrollY);
    }, debounceMs);
    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [debounceMs, setPosition]);
  return [position, setPosition];
};

// node_modules/ra-core/src/routing/RestoreScrollPosition.tsx
var RestoreScrollPosition = ({
  children,
  storeKey,
  debounce: debounce5 = 250
}) => {
  useRestoreScrollPosition(storeKey, debounce5);
  return children;
};

// node_modules/ra-core/src/routing/useBasename.ts
var import_react24 = __toESM(require_react());
var useBasename = () => (0, import_react24.useContext)(BasenameContext);

// node_modules/ra-core/src/routing/useCreatePath.ts
var import_react25 = __toESM(require_react());
var useCreatePath = () => {
  const basename = useBasename();
  return (0, import_react25.useCallback)(
    ({ resource, id, type }) => {
      if (["list", "create", "edit", "show"].includes(type) && !resource) {
        throw new Error(
          "Cannot create a link without a resource. You must provide the resource name."
        );
      }
      switch (type) {
        case "list":
          return removeDoubleSlashes(`${basename}/${resource}`);
        case "create":
          return removeDoubleSlashes(
            `${basename}/${resource}/create`
          );
        case "edit": {
          if (id == null) {
            return removeDoubleSlashes(`${basename}/${resource}`);
          }
          return removeDoubleSlashes(
            `${basename}/${resource}/${encodeURIComponent(id)}`
          );
        }
        case "show": {
          if (id == null) {
            return removeDoubleSlashes(`${basename}/${resource}`);
          }
          return removeDoubleSlashes(
            `${basename}/${resource}/${encodeURIComponent(id)}/show`
          );
        }
        default:
          return type;
      }
    },
    [basename]
  );
};
var removeDoubleSlashes = (path) => path.replace("//", "/");

// node_modules/ra-core/src/routing/useGetPathForRecord.ts
var import_react30 = __toESM(require_react());

// node_modules/ra-core/src/core/useResourceContext.ts
var import_react27 = __toESM(require_react());

// node_modules/ra-core/src/core/ResourceContext.ts
var import_react26 = __toESM(require_react());
var ResourceContext = (0, import_react26.createContext)(void 0);

// node_modules/ra-core/src/core/useResourceContext.ts
var useResourceContext = (props) => {
  const context = (0, import_react27.useContext)(ResourceContext);
  return props && props.resource || context;
};

// node_modules/ra-core/src/controller/record/useRecordContext.ts
var import_react29 = __toESM(require_react());

// node_modules/ra-core/src/controller/record/RecordContext.tsx
var import_react28 = __toESM(require_react());
var import_jsx_runtime9 = __toESM(require_jsx_runtime());
var RecordContext = (0, import_react28.createContext)(void 0);
RecordContext.displayName = "RecordContext";
var RecordContextProvider = ({
  children,
  value
}) => (0, import_jsx_runtime9.jsx)(RecordContext.Provider, { value, children });

// node_modules/ra-core/src/controller/record/useRecordContext.ts
var useRecordContext = (props) => {
  const context = (0, import_react29.useContext)(RecordContext);
  return props && props.record || context;
};

// node_modules/ra-core/src/routing/useGetPathForRecord.ts
var useGetPathForRecord = (options = {}) => {
  const { link } = options || {};
  const record = useRecordContext(options);
  const resource = useResourceContext(options);
  if (!resource) {
    throw new Error(
      "Cannot generate a link for a record without a resource. You must use useGetPathForRecord within a ResourceContextProvider, or pass a resource prop."
    );
  }
  const resourceDefinition = useResourceDefinition(options);
  const createPath = useCreatePath();
  const [path, setPath] = (0, import_react30.useState)(
    link && typeof link !== "function" && record != null ? createPath({
      resource,
      id: record.id,
      type: link
    }) : false
  );
  const { canAccess: canAccessShow } = useCanAccess({
    action: "show",
    resource,
    record,
    enabled: link == null && resourceDefinition.hasShow
  });
  const { canAccess: canAccessEdit } = useCanAccess({
    action: "edit",
    resource,
    record,
    enabled: link == null && resourceDefinition.hasEdit
  });
  (0, import_react30.useEffect)(() => {
    if (!record) return;
    if (link === false) {
      setPath(false);
      return;
    }
    if (link == null) {
      if (resourceDefinition.hasShow && canAccessShow) {
        setPath(
          createPath({
            resource,
            id: record.id,
            type: "show"
          })
        );
        return;
      }
      if (resourceDefinition.hasEdit && canAccessEdit) {
        setPath(
          createPath({
            resource,
            id: record.id,
            type: "edit"
          })
        );
        return;
      }
    }
    if (typeof link === "function") {
      const linkResult = link(record, resource);
      if (linkResult instanceof Promise) {
        linkResult.then((resolvedPath) => setPath(resolvedPath));
        return;
      }
      setPath(
        linkResult ? createPath({
          resource,
          id: record.id,
          type: linkResult
        }) : false
      );
      return;
    }
    if (link) {
      setPath(
        createPath({
          resource,
          id: record.id,
          type: link
        })
      );
    }
  }, [
    createPath,
    canAccessShow,
    canAccessEdit,
    link,
    record,
    resource,
    resourceDefinition.hasEdit,
    resourceDefinition.hasShow
  ]);
  return path;
};

// node_modules/ra-core/src/routing/useGetPathForRecordCallback.ts
var import_react33 = __toESM(require_react());

// node_modules/ra-core/src/core/useResourceDefinitionContext.ts
var import_react32 = __toESM(require_react());

// node_modules/ra-core/src/core/ResourceDefinitionContext.tsx
var import_react31 = __toESM(require_react());
var import_isEqual = __toESM(require_isEqual());
var import_jsx_runtime10 = __toESM(require_jsx_runtime());
var ResourceDefinitionContext = (0, import_react31.createContext)({
  definitions: {},
  register: () => {
  },
  unregister: () => {
  }
});
var ResourceDefinitionContextProvider = ({
  definitions: defaultDefinitions = {},
  children
}) => {
  const [definitions, setState] = (0, import_react31.useState)(defaultDefinitions);
  const register = (0, import_react31.useCallback)((config) => {
    setState(
      (prev) => (0, import_isEqual.default)(prev[config.name], config) ? prev : {
        ...prev,
        [config.name]: config
      }
    );
  }, []);
  const unregister = (0, import_react31.useCallback)((config) => {
    setState((prev) => {
      const { [config.name]: _, ...rest } = prev;
      return rest;
    });
  }, []);
  const contextValue = (0, import_react31.useMemo)(
    () => ({ definitions, register, unregister }),
    [definitions]
    // eslint-disable-line react-hooks/exhaustive-deps
  );
  return (0, import_jsx_runtime10.jsx)(ResourceDefinitionContext.Provider, { value: contextValue, children });
};

// node_modules/ra-core/src/core/useResourceDefinitionContext.ts
var useResourceDefinitionContext = () => (0, import_react32.useContext)(ResourceDefinitionContext);

// node_modules/ra-core/src/core/useResourceDefinitions.ts
var useResourceDefinitions = () => useResourceDefinitionContext().definitions;

// node_modules/ra-core/src/auth/useCanAccessCallback.ts
var useCanAccessCallback = (options = {}) => {
  const authProvider = useAuthProvider_default();
  const { mutateAsync } = useMutation({
    mutationFn: async (params) => {
      if (!authProvider || !authProvider.canAccess) {
        return true;
      }
      return authProvider.canAccess(params);
    },
    retry: false,
    ...options
  });
  return mutateAsync;
};

// node_modules/ra-core/src/routing/useGetPathForRecordCallback.ts
var useGetPathForRecordCallback = (options = {}) => {
  const resource = useResourceContext(options);
  const resourceDefinitions = useResourceDefinitions();
  const createPath = useCreatePath();
  const canAccess = useCanAccessCallback();
  return (0, import_react33.useCallback)(
    async (params) => {
      const { link, record } = params || {};
      const finalResource = params.resource ?? resource;
      if (!finalResource) {
        throw new Error(
          "Cannot generate a link for a record without a resource. You must use useGetPathForRecordCallback within a ResourceContextProvider, or pass a resource parameter."
        );
      }
      const resourceDefinition = resourceDefinitions[finalResource] ?? {};
      if (record == null || link === false) {
        return false;
      }
      if (link == null) {
        const [canAccessShow, canAccessEdit] = await Promise.all([
          resourceDefinition.hasShow ? canAccess({
            action: "show",
            resource: finalResource,
            record
          }) : Promise.resolve(false),
          resourceDefinition.hasEdit ? canAccess({
            action: "edit",
            resource: finalResource,
            record
          }) : Promise.resolve(false)
        ]);
        if (canAccessShow) {
          return createPath({
            resource: finalResource,
            id: record.id,
            type: "show"
          });
        }
        if (canAccessEdit) {
          return createPath({
            resource: finalResource,
            id: record.id,
            type: "edit"
          });
        }
        return false;
      }
      const linkFunc = typeof link === "function" ? link : () => link;
      const linkResult = linkFunc(record, finalResource);
      if (linkResult === false) {
        return false;
      }
      const linkResultIsPromise = isPromise(linkResult);
      if (linkResultIsPromise) {
        const resolvedLink = await linkResult;
        if (resolvedLink === false) {
          return;
        }
        return createPath({
          resource: finalResource,
          id: record.id,
          type: resolvedLink
        });
      }
      return createPath({
        resource: finalResource,
        id: record.id,
        type: linkResult
      });
    },
    [canAccess, createPath, resourceDefinitions, resource]
  );
};
var isPromise = (value) => value && typeof value.then === "function";

// node_modules/ra-core/src/routing/useRedirect.ts
var import_react34 = __toESM(require_react());
var useRedirect = () => {
  const navigate = useNavigate();
  const basename = useBasename();
  const createPath = useCreatePath();
  return (0, import_react34.useCallback)(
    (redirectTo, resource = "", id, data, state = {}) => {
      var _a;
      if (!redirectTo) {
        return;
      } else if (typeof redirectTo === "function") {
        const target = redirectTo(resource, id, data);
        const absoluteTarget = typeof target === "string" ? `${basename}${target.startsWith("/") ? "" : "/"}${target}` : {
          pathname: `${basename}${((_a = target.pathname) == null ? void 0 : _a.startsWith("/")) ? "" : "/"}${target.pathname}`,
          ...target
        };
        navigate(absoluteTarget, {
          state: { _scrollToTop: true, ...state }
        });
        return;
      } else if (typeof redirectTo === "string" && redirectTo.startsWith("http") && window) {
        window.location.href = redirectTo;
        return;
      } else {
        navigate(createPath({ resource, id, type: redirectTo }), {
          state: (
            // We force the scrollToTop except when navigating to a list
            // where this is already done by <RestoreScrollPosition> in <Resource>
            redirectTo === "list" ? state : { _scrollToTop: true, ...state }
          )
        });
        return;
      }
    },
    [navigate, basename, createPath]
  );
};

// node_modules/ra-core/src/routing/useResetErrorBoundaryOnLocationChange.ts
var import_react35 = __toESM(require_react());
var useResetErrorBoundaryOnLocationChange = (resetErrorBoundary) => {
  const { pathname } = useLocation();
  const originalPathname = (0, import_react35.useRef)(pathname);
  (0, import_react35.useEffect)(() => {
    if (pathname !== originalPathname.current) {
      resetErrorBoundary();
    }
  }, [pathname, resetErrorBoundary]);
};

// node_modules/ra-core/src/routing/useScrollToTop.tsx
var import_react36 = __toESM(require_react());
var useScrollToTop = () => {
  const location = useLocation();
  (0, import_react36.useEffect)(() => {
    var _a;
    if (((_a = location.state) == null ? void 0 : _a._scrollToTop) && typeof window != "undefined" && typeof window.scrollTo === "function") {
      window.scrollTo(0, 0);
    }
  }, [location]);
};

// node_modules/ra-core/src/routing/TestMemoryRouter.tsx
var import_jsx_runtime11 = __toESM(require_jsx_runtime());
var UseLocation = ({
  locationCallback
}) => {
  const location = useLocation();
  locationCallback(location);
  return null;
};
var UseNavigate = ({
  navigateCallback
}) => {
  const navigate = useNavigate();
  navigateCallback(navigate);
  return null;
};
var TestMemoryRouter = ({
  children,
  locationCallback,
  navigateCallback,
  ...rest
}) => {
  const router = createMemoryRouter(
    [
      {
        path: "*",
        element: (0, import_jsx_runtime11.jsxs)(import_jsx_runtime11.Fragment, { children: [
          children,
          locationCallback && (0, import_jsx_runtime11.jsx)(UseLocation, { locationCallback }),
          navigateCallback && (0, import_jsx_runtime11.jsx)(UseNavigate, { navigateCallback })
        ] })
      }
    ],
    {
      future: {
        v7_fetcherPersist: false,
        v7_normalizeFormMethod: false,
        v7_partialHydration: false,
        v7_relativeSplatPath: false,
        v7_skipActionErrorRevalidation: false
      },
      ...rest
    }
  );
  return (0, import_jsx_runtime11.jsx)(
    RouterProvider2,
    {
      router,
      future: { v7_startTransition: false, v7_relativeSplatPath: false }
    }
  );
};

// node_modules/ra-core/src/routing/useSplatPathBase.ts
var useSplatPathBase = () => {
  const location = useLocation();
  const params = useParams();
  const splatPathRelativePart = params["*"];
  const splatPathBase = location.pathname.replace(
    new RegExp(`/${splatPathRelativePart}$`),
    ""
  );
  return splatPathBase;
};

// node_modules/ra-core/src/dataProvider/dataFetchActions.ts
var GET_LIST = "GET_LIST";
var GET_ONE = "GET_ONE";
var GET_MANY = "GET_MANY";
var GET_MANY_REFERENCE = "GET_MANY_REFERENCE";
var CREATE = "CREATE";
var UPDATE = "UPDATE";
var UPDATE_MANY = "UPDATE_MANY";
var DELETE = "DELETE";
var DELETE_MANY = "DELETE_MANY";
var fetchActionsWithRecordResponse = ["getOne", "create", "update"];
var fetchActionsWithArrayOfIdentifiedRecordsResponse = [
  "getList",
  "getMany",
  "getManyReference"
];
var fetchActionsWithArrayOfRecordsResponse = [
  ...fetchActionsWithArrayOfIdentifiedRecordsResponse,
  "updateMany",
  "deleteMany"
];
var fetchActionsWithTotalResponse = ["getList", "getManyReference"];
var reactAdminFetchActions = [
  ...fetchActionsWithRecordResponse,
  ...fetchActionsWithArrayOfRecordsResponse
];
var sanitizeFetchType = (fetchType) => {
  switch (fetchType) {
    case GET_LIST:
      return "getList";
    case GET_ONE:
      return "getOne";
    case GET_MANY:
      return "getMany";
    case GET_MANY_REFERENCE:
      return "getManyReference";
    case CREATE:
      return "create";
    case UPDATE:
      return "update";
    case UPDATE_MANY:
      return "updateMany";
    case DELETE:
      return "delete";
    case DELETE_MANY:
      return "deleteMany";
    default:
      return fetchType;
  }
};

// node_modules/ra-core/src/dataProvider/defaultDataProvider.ts
var defaultDataProvider = {
  create: () => Promise.resolve({ data: null }),
  delete: () => Promise.resolve({ data: null }),
  deleteMany: () => Promise.resolve({ data: [] }),
  getList: () => Promise.resolve({ data: [], total: 0 }),
  getMany: () => Promise.resolve({ data: [] }),
  getManyReference: () => Promise.resolve({ data: [], total: 0 }),
  getOne: () => Promise.resolve({ data: null }),
  update: () => Promise.resolve({ data: null }),
  updateMany: () => Promise.resolve({ data: [] })
};

// node_modules/ra-core/src/dataProvider/convertLegacyDataProvider.ts
var fetchMap = {
  create: CREATE,
  delete: DELETE,
  deleteMany: DELETE_MANY,
  getList: GET_LIST,
  getMany: GET_MANY,
  getManyReference: GET_MANY_REFERENCE,
  getOne: GET_ONE,
  update: UPDATE,
  updateMany: UPDATE_MANY
};
var convertLegacyDataProvider = (legacyDataProvider) => {
  const proxy = new Proxy(defaultDataProvider, {
    get(_, name) {
      return (resource, params) => {
        if (Object.keys(fetchMap).includes(name.toString())) {
          const fetchType = fetchMap[name.toString()];
          return legacyDataProvider(fetchType, resource, params);
        }
        return legacyDataProvider(name.toString(), resource, params);
      };
    },
    apply(_, __, args) {
      return legacyDataProvider.apply(legacyDataProvider, args);
    }
  });
  return proxy;
};
var convertLegacyDataProvider_default = convertLegacyDataProvider;

// node_modules/ra-core/src/dataProvider/DataProviderContext.ts
var import_react37 = __toESM(require_react());
var DataProviderContext = (0, import_react37.createContext)(null);
DataProviderContext.displayName = "DataProviderContext";
var DataProviderContext_default = DataProviderContext;

// node_modules/ra-core/src/dataProvider/HttpError.ts
var HttpError = class _HttpError extends Error {
  constructor(message, status, body = null) {
    super(message);
    this.message = message;
    this.status = status;
    this.body = body;
    Object.setPrototypeOf(this, _HttpError.prototype);
    this.name = this.constructor.name;
    if (typeof Error.captureStackTrace === "function") {
      Error.captureStackTrace(this, this.constructor);
    } else {
      this.stack = new Error(message).stack;
    }
    this.stack = new Error().stack;
  }
};
var HttpError_default = HttpError;

// node_modules/ra-core/src/dataProvider/fetch.ts
var fetch_exports = {};
__export(fetch_exports, {
  createHeadersFromOptions: () => createHeadersFromOptions,
  fetchJson: () => fetchJson,
  flattenObject: () => flattenObject,
  queryParameters: () => queryParameters
});
var import_query_string = __toESM(require_query_string());
var createHeadersFromOptions = (options) => {
  const requestHeaders = options.headers || new Headers({
    Accept: "application/json"
  });
  const hasBody = options && options.body;
  const isContentTypeSet = requestHeaders.has("Content-Type");
  const isGetMethod = !(options == null ? void 0 : options.method) || (options == null ? void 0 : options.method) === "GET";
  const isFormData = (options == null ? void 0 : options.body) instanceof FormData;
  const shouldSetContentType = hasBody && !isContentTypeSet && !isGetMethod && !isFormData;
  if (shouldSetContentType) {
    requestHeaders.set("Content-Type", "application/json");
  }
  if (options.user && options.user.authenticated && options.user.token) {
    requestHeaders.set("Authorization", options.user.token);
  }
  return requestHeaders;
};
var fetchJson = (url, options = {}) => {
  const requestHeaders = createHeadersFromOptions(options);
  return fetch(url, { ...options, headers: requestHeaders }).then(
    (response) => response.text().then((text) => ({
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
      body: text
    }))
  ).then(({ status, statusText, headers, body }) => {
    let json;
    try {
      json = JSON.parse(body);
    } catch (e) {
    }
    if (status < 200 || status >= 300) {
      return Promise.reject(
        new HttpError_default(
          json && json.message || statusText,
          status,
          json
        )
      );
    }
    return Promise.resolve({ status, headers, body, json });
  });
};
var queryParameters = import_query_string.stringify;
var isValidObject = (value) => {
  if (!value) {
    return false;
  }
  const isArray2 = Array.isArray(value);
  const isBuffer = typeof Buffer !== "undefined" && Buffer.isBuffer(value);
  const isObject3 = Object.prototype.toString.call(value) === "[object Object]";
  const hasKeys = !!Object.keys(value).length;
  return !isArray2 && !isBuffer && isObject3 && hasKeys;
};
var flattenObject = (value, path = []) => {
  if (isValidObject(value)) {
    return Object.assign(
      {},
      ...Object.keys(value).map(
        (key) => flattenObject(value[key], path.concat([key]))
      )
    );
  } else {
    return path.length ? { [path.join(".")]: value } : value;
  }
};

// node_modules/eventemitter3/index.mjs
var import_index2 = __toESM(require_eventemitter3(), 1);
var eventemitter3_default = import_index2.default;

// node_modules/ra-core/src/dataProvider/undoableEventEmitter.ts
var undoableEventEmitter_default = new eventemitter3_default();

// node_modules/ra-core/src/dataProvider/combineDataProviders.ts
var combineDataProviders = (dataProviderMatcher) => new Proxy(defaultDataProvider, {
  get: (target, name) => {
    if (name === "then") {
      return null;
    }
    return (resource, ...params) => {
      if (typeof name === "symbol") {
        return;
      }
      return dataProviderMatcher(resource)[name](resource, ...params);
    };
  }
});

// node_modules/ra-core/src/dataProvider/testDataProvider.ts
var defaultTestDataProvider = {
  create: async () => {
    throw new Error("create is not implemented");
  },
  delete: async () => {
    throw new Error("delete not implemented");
  },
  deleteMany: async () => {
    throw new Error("deleteMany is not implemented");
  },
  getList: async () => {
    throw new Error("getList is not implemented");
  },
  getMany: async () => {
    throw new Error("getMany is not implemented");
  },
  getManyReference: async () => {
    throw new Error("getManyReference is not implemented");
  },
  getOne: async () => {
    throw new Error("getOne is not implemented");
  },
  update: async () => {
    throw new Error("update not implemented");
  },
  updateMany: async () => {
    throw new Error("updateMany not implemented");
  }
};
var testDataProvider = (overrides) => ({
  ...defaultTestDataProvider,
  ...overrides
});

// node_modules/ra-core/src/dataProvider/withLifecycleCallbacks.ts
var withLifecycleCallbacks = (dataProvider, handlers) => {
  return {
    ...dataProvider,
    getList: async function(resource, params) {
      let newParams = params;
      newParams = await applyCallbacks({
        name: "beforeGetList",
        params: newParams,
        dataProvider,
        handlers,
        resource
      });
      let result = await dataProvider.getList(
        resource,
        newParams
      );
      result = await applyCallbacks({
        name: "afterGetList",
        params: result,
        dataProvider,
        handlers,
        resource
      });
      result.data = await Promise.all(
        result.data.map(
          (record) => applyCallbacks({
            name: "afterRead",
            params: record,
            dataProvider,
            handlers,
            resource
          })
        )
      );
      return result;
    },
    getOne: async function(resource, params) {
      let newParams = params;
      newParams = await applyCallbacks({
        name: "beforeGetOne",
        params: newParams,
        dataProvider,
        handlers,
        resource
      });
      let result = await dataProvider.getOne(
        resource,
        newParams
      );
      result = await applyCallbacks({
        name: "afterGetOne",
        params: result,
        dataProvider,
        handlers,
        resource
      });
      result.data = await applyCallbacks({
        name: "afterRead",
        params: result.data,
        dataProvider,
        handlers,
        resource
      });
      return result;
    },
    getMany: async function(resource, params) {
      let newParams = params;
      newParams = await applyCallbacks({
        name: "beforeGetMany",
        params: newParams,
        dataProvider,
        handlers,
        resource
      });
      let result = await dataProvider.getMany(
        resource,
        newParams
      );
      result = await applyCallbacks({
        name: "afterGetMany",
        params: result,
        dataProvider,
        handlers,
        resource
      });
      result.data = await Promise.all(
        result.data.map(
          (record) => applyCallbacks({
            name: "afterRead",
            params: record,
            dataProvider,
            handlers,
            resource
          })
        )
      );
      return result;
    },
    getManyReference: async function(resource, params) {
      let newParams = params;
      newParams = await applyCallbacks({
        name: "beforeGetManyReference",
        params: newParams,
        dataProvider,
        handlers,
        resource
      });
      let result = await dataProvider.getManyReference(
        resource,
        newParams
      );
      result = await applyCallbacks({
        name: "afterGetManyReference",
        params: result,
        dataProvider,
        handlers,
        resource
      });
      result.data = await Promise.all(
        result.data.map(
          (record) => applyCallbacks({
            name: "afterRead",
            params: record,
            dataProvider,
            handlers,
            resource
          })
        )
      );
      return result;
    },
    update: async function(resource, params) {
      let newParams = params;
      newParams = await applyCallbacks({
        name: "beforeUpdate",
        params: newParams,
        dataProvider,
        handlers,
        resource
      });
      newParams.data = await applyCallbacks({
        name: "beforeSave",
        params: newParams.data,
        dataProvider,
        handlers,
        resource
      });
      let result = await dataProvider.update(
        resource,
        newParams
      );
      result = await applyCallbacks({
        name: "afterUpdate",
        params: result,
        dataProvider,
        handlers,
        resource
      });
      result.data = await applyCallbacks({
        name: "afterSave",
        params: result.data,
        dataProvider,
        handlers,
        resource
      });
      return result;
    },
    create: async function(resource, params) {
      let newParams = params;
      newParams = await applyCallbacks({
        name: "beforeCreate",
        params: newParams,
        dataProvider,
        handlers,
        resource
      });
      newParams.data = await applyCallbacks({
        name: "beforeSave",
        params: newParams.data,
        dataProvider,
        handlers,
        resource
      });
      let result = await dataProvider.create(
        resource,
        newParams
      );
      result = await applyCallbacks({
        name: "afterCreate",
        params: result,
        dataProvider,
        handlers,
        resource
      });
      result.data = await applyCallbacks({
        name: "afterSave",
        params: result.data,
        dataProvider,
        handlers,
        resource
      });
      return result;
    },
    delete: async function(resource, params) {
      let newParams = params;
      newParams = await applyCallbacks({
        name: "beforeDelete",
        params: newParams,
        dataProvider,
        handlers,
        resource
      });
      let result = await dataProvider.delete(
        resource,
        newParams
      );
      result = await applyCallbacks({
        name: "afterDelete",
        params: result,
        dataProvider,
        handlers,
        resource
      });
      return result;
    },
    updateMany: async function(resource, params) {
      let newParams = params;
      newParams = await applyCallbacks({
        name: "beforeUpdateMany",
        params: newParams,
        dataProvider,
        handlers,
        resource
      });
      newParams.data = await applyCallbacks({
        name: "beforeSave",
        params: newParams.data,
        dataProvider,
        handlers,
        resource
      });
      let result = await dataProvider.updateMany(
        resource,
        newParams
      );
      result = await applyCallbacks({
        name: "afterUpdateMany",
        params: result,
        dataProvider,
        handlers,
        resource
      });
      const afterSaveHandlers = handlers.filter(
        (h) => (h.resource === resource || h.resource === "*") && h.afterSave
      );
      if (afterSaveHandlers.length > 0) {
        const { data: records } = await dataProvider.getMany(resource, {
          //@ts-ignore
          ids: result.data
        });
        await Promise.all(
          records.map(
            (record) => applyCallbacks({
              name: "afterSave",
              params: record,
              dataProvider,
              handlers,
              resource
            })
          )
        );
      }
      return result;
    },
    deleteMany: async function(resource, params) {
      let newParams = params;
      newParams = await applyCallbacks({
        name: "beforeDeleteMany",
        params: newParams,
        dataProvider,
        handlers,
        resource
      });
      let result = await dataProvider.deleteMany(
        resource,
        newParams
      );
      result = await applyCallbacks({
        name: "afterDeleteMany",
        params: result,
        dataProvider,
        handlers,
        resource
      });
      return result;
    }
  };
};
var applyCallbacks = async function({
  name,
  params,
  dataProvider,
  handlers,
  resource
}) {
  let newParams = params;
  const handlersToApply = handlers.filter(
    (h) => (h.resource === resource || h.resource === "*") && h[name]
  );
  for (let handler of handlersToApply) {
    const callbacksValue = handler[name];
    if (Array.isArray(callbacksValue)) {
      for (let callback of callbacksValue ?? []) {
        newParams = await callback(newParams, dataProvider, resource);
      }
    } else {
      newParams = await callbacksValue(newParams, dataProvider, resource);
    }
  }
  return newParams;
};

// node_modules/ra-core/src/dataProvider/useDataProvider.ts
var import_react39 = __toESM(require_react());

// node_modules/ra-core/src/dataProvider/validateResponseFormat.ts
function validateResponseFormat(response, type, logger = console.error) {
  if (!response) {
    logger(`The dataProvider returned an empty response for '${type}'.`);
    throw new Error("ra.notification.data_provider_error");
  }
  if (!response.hasOwnProperty("data")) {
    logger(
      `The response to '${type}' must be like { data: ... }, but the received response does not have a 'data' key. The dataProvider is probably wrong for '${type}'.`
    );
    throw new Error("ra.notification.data_provider_error");
  }
  if (fetchActionsWithArrayOfRecordsResponse.includes(type) && !Array.isArray(response.data)) {
    logger(
      `The response to '${type}' must be like { data : [...] }, but the received data is not an array. The dataProvider is probably wrong for '${type}'`
    );
    throw new Error("ra.notification.data_provider_error");
  }
  if (fetchActionsWithArrayOfIdentifiedRecordsResponse.includes(type) && Array.isArray(response.data) && response.data.length > 0 && !response.data[0].hasOwnProperty("id")) {
    logger(
      `The response to '${type}' must be like { data : [{ id: 123, ...}, ...] }, but the received data items do not have an 'id' key. The dataProvider is probably wrong for '${type}'`
    );
    throw new Error("ra.notification.data_provider_error");
  }
  if (fetchActionsWithRecordResponse.includes(type) && !response.data.hasOwnProperty("id")) {
    logger(
      `The response to '${type}' must be like { data: { id: 123, ... } }, but the received data does not have an 'id' key. The dataProvider is probably wrong for '${type}'`
    );
    throw new Error("ra.notification.data_provider_error");
  }
  if (fetchActionsWithTotalResponse.includes(type) && !response.hasOwnProperty("total") && !response.hasOwnProperty("pageInfo")) {
    logger(
      `The response to '${type}' must be like { data: [...], total: 123 } or { data: [...], pageInfo: {...} }, but the received response has neither a 'total' nor a 'pageInfo' key. The dataProvider is probably wrong for '${type}'`
    );
    throw new Error("ra.notification.data_provider_error");
  }
}
var validateResponseFormat_default = validateResponseFormat;

// node_modules/ra-core/src/auth/useLogoutIfAccessDenied.ts
var import_react38 = __toESM(require_react());
var timer;
var useLogoutIfAccessDenied = () => {
  const authProvider = useAuthProvider_default();
  const logout = useLogout_default();
  const notify = useNotify();
  const navigate = useNavigate();
  const logoutIfAccessDenied = (0, import_react38.useCallback)(
    (error) => {
      if (!authProvider) {
        return logoutIfAccessDeniedWithoutProvider();
      }
      return authProvider.checkError(error).then(() => false).catch(async (e) => {
        const logoutUser = (e == null ? void 0 : e.logoutUser) ?? true;
        if (timer) {
          return true;
        }
        timer = setTimeout(() => {
          timer = void 0;
        }, 0);
        const redirectTo = e && e.redirectTo != null ? e.redirectTo : error && error.redirectTo ? error.redirectTo : void 0;
        const shouldNotify = !(e && e.message === false || error && error.message === false || (redirectTo == null ? void 0 : redirectTo.startsWith("http")));
        if (shouldNotify) {
          authProvider.checkAuth({}).then(() => {
            if (logoutUser) {
              notify(
                getErrorMessage(
                  e,
                  "ra.notification.logged_out"
                ),
                { type: "error" }
              );
            } else {
              notify(
                getErrorMessage(
                  e,
                  "ra.notification.not_authorized"
                ),
                { type: "error" }
              );
            }
          }).catch(() => {
          });
        }
        if (logoutUser) {
          logout({}, redirectTo);
        } else {
          if (redirectTo.startsWith("http")) {
            window.location.href = redirectTo;
          } else {
            navigate(redirectTo);
          }
        }
        return true;
      });
    },
    [authProvider, logout, notify, navigate]
  );
  return logoutIfAccessDenied;
};
var logoutIfAccessDeniedWithoutProvider = () => Promise.resolve(false);
var getErrorMessage = (error, defaultMessage) => typeof error === "string" ? error : typeof error === "undefined" || !error.message ? defaultMessage : error.message;
var useLogoutIfAccessDenied_default = useLogoutIfAccessDenied;

// node_modules/ra-core/src/dataProvider/populateQueryCache.ts
var populateQueryCache = ({
  data,
  queryClient,
  staleTime = 1e3
  // ms
}) => {
  const updatedAt = Date.now() + staleTime;
  Object.keys(data).forEach((resource) => {
    data[resource].forEach((record) => {
      if (!record || record.id == null) return;
      queryClient.setQueryData(
        [resource, "getOne", { id: String(record.id) }],
        record,
        { updatedAt }
      );
    });
    const recordIds = data[resource].map((record) => String(record.id));
    queryClient.setQueryData(
      [resource, "getMany", { ids: recordIds }],
      data[resource],
      { updatedAt }
    );
  });
};

// node_modules/ra-core/src/dataProvider/useDataProvider.ts
var arrayReturnTypes = ["getList", "getMany", "getManyReference"];
var useDataProvider = () => {
  const dataProvider = (0, import_react39.useContext)(DataProviderContext_default) || defaultDataProvider;
  const queryClient = useQueryClient();
  const logoutIfAccessDenied = useLogoutIfAccessDenied_default();
  const dataProviderProxy = (0, import_react39.useMemo)(() => {
    return new Proxy(dataProvider, {
      get: (_, name) => {
        if (typeof name === "symbol" || name === "then") {
          return;
        }
        if (name === "supportAbortSignal") {
          return dataProvider.supportAbortSignal;
        }
        return (...args) => {
          const type = name.toString();
          if (typeof dataProvider[type] !== "function") {
            throw new Error(
              `Unknown dataProvider function: ${type}`
            );
          }
          try {
            return dataProvider[type].apply(dataProvider, args).then((response) => {
              var _a;
              if (reactAdminFetchActions.includes(type)) {
                validateResponseFormat_default(response, type);
              }
              if ((_a = response == null ? void 0 : response.meta) == null ? void 0 : _a.prefetched) {
                populateQueryCache({
                  data: response == null ? void 0 : response.meta.prefetched,
                  queryClient
                });
              }
              return response;
            }).catch((error) => {
              if (
                // do not log AbortErrors
                !isAbortError(error)
              ) {
                console.error(error);
              }
              return logoutIfAccessDenied(error).then(
                (loggedOut) => {
                  if (loggedOut)
                    return {
                      data: arrayReturnTypes.includes(
                        type
                      ) ? [] : {}
                    };
                  throw error;
                }
              );
            });
          } catch (e) {
            if (true) {
              console.error(e);
            }
            throw new Error(
              "The dataProvider threw an error. It should return a rejected Promise instead."
            );
          }
        };
      }
    });
  }, [dataProvider, logoutIfAccessDenied, queryClient]);
  return dataProviderProxy;
};
var isAbortError = (error) => error instanceof DOMException && error.name === "AbortError";

// node_modules/ra-core/src/dataProvider/useIsDataLoaded.ts
var import_react40 = __toESM(require_react());
var useIsDataLoaded = (queryKey, options = {}) => {
  const { enabled = true } = options;
  const queryClient = useQueryClient();
  const [isDataLoaded, setDataLoaded] = (0, import_react40.useState)(() => {
    if (!enabled) {
      return false;
    }
    return queryClient.getQueryData(queryKey) !== void 0;
  });
  (0, import_react40.useEffect)(() => {
    if (!enabled) return;
    if (queryClient.getQueryData(queryKey) === void 0) {
      const observer = new QueryObserver(queryClient, { queryKey });
      const unsubscribe = observer.subscribe((result) => {
        setDataLoaded(!result.isPending);
        unsubscribe();
      });
      return unsubscribe;
    }
  }, [enabled, queryKey, queryClient]);
  return isDataLoaded;
};

// node_modules/ra-core/src/dataProvider/useLoading.ts
var import_react41 = __toESM(require_react());
var useLoading = () => {
  const client = useQueryClient();
  const mountedRef = import_react41.default.useRef(false);
  const isFetchingRef = import_react41.default.useRef(client.isFetching() > 0);
  const isMutatingRef = import_react41.default.useRef(client.isMutating() > 0);
  const [isLoading, setIsLoading] = import_react41.default.useState(
    isFetchingRef.current || isMutatingRef.current
  );
  import_react41.default.useEffect(() => {
    mountedRef.current = true;
    const unsubscribeQueryCache = client.getQueryCache().subscribe(
      notifyManager.batchCalls(() => {
        if (mountedRef.current) {
          isFetchingRef.current = client.isFetching() > 0;
          setIsLoading(
            isFetchingRef.current || isMutatingRef.current
          );
        }
      })
    );
    const unsubscribeMutationCache = client.getMutationCache().subscribe(
      notifyManager.batchCalls(() => {
        if (mountedRef.current) {
          isMutatingRef.current = client.isMutating() > 0;
          setIsLoading(
            isFetchingRef.current || isMutatingRef.current
          );
        }
      })
    );
    return () => {
      mountedRef.current = false;
      unsubscribeQueryCache();
      unsubscribeMutationCache();
    };
  }, [client]);
  return isLoading;
};

// node_modules/ra-core/src/dataProvider/useRefresh.ts
var import_react42 = __toESM(require_react());
var useRefresh = () => {
  const queryClient = useQueryClient();
  return (0, import_react42.useCallback)(() => {
    queryClient.invalidateQueries();
  }, [queryClient]);
};

// node_modules/ra-core/src/dataProvider/useGetOne.ts
var import_react43 = __toESM(require_react());
var useGetOne = (resource, { id, meta }, options = {}) => {
  const dataProvider = useDataProvider();
  const {
    onError = noop,
    onSuccess = noop,
    onSettled = noop,
    enabled,
    ...queryOptions
  } = options;
  const onSuccessEvent = useEvent(onSuccess);
  const onErrorEvent = useEvent(onError);
  const onSettledEvent = useEvent(onSettled);
  const result = useQuery({
    // Sometimes the id comes as a string (e.g. when read from the URL in a Show view).
    // Sometimes the id comes as a number (e.g. when read from a Record in useGetList response).
    // As the react-query cache is type-sensitive, we always stringify the identifier to get a match
    queryKey: [resource, "getOne", { id: String(id), meta }],
    queryFn: (queryParams) => id == null ? new Promise(() => {
    }) : dataProvider.getOne(resource, {
      id,
      meta,
      signal: dataProvider.supportAbortSignal === true ? queryParams.signal : void 0
    }).then(({ data }) => data),
    enabled: enabled ?? id != null,
    ...queryOptions
  });
  (0, import_react43.useEffect)(() => {
    if (result.data === void 0 || result.error != null || result.isFetching)
      return;
    onSuccessEvent(result.data);
  }, [onSuccessEvent, result.data, result.error, result.isFetching]);
  (0, import_react43.useEffect)(() => {
    if (result.error == null || result.isFetching) return;
    onErrorEvent(result.error);
  }, [onErrorEvent, result.error, result.isFetching]);
  (0, import_react43.useEffect)(() => {
    if (result.status === "pending" || result.isFetching) return;
    onSettledEvent(result.data, result.error);
  }, [
    onSettledEvent,
    result.data,
    result.error,
    result.status,
    result.isFetching
  ]);
  return result;
};
var noop = () => void 0;

// node_modules/ra-core/src/dataProvider/useGetList.ts
var import_react44 = __toESM(require_react());
var MAX_DATA_LENGTH_TO_CACHE = 100;
var useGetList = (resource, params = {}, options = {}) => {
  const {
    pagination = { page: 1, perPage: 25 },
    sort = { field: "id", order: "DESC" },
    filter = {},
    meta
  } = params;
  const dataProvider = useDataProvider();
  const queryClient = useQueryClient();
  const {
    onError = noop2,
    onSuccess = noop2,
    onSettled = noop2,
    ...queryOptions
  } = options;
  const onSuccessEvent = useEvent(onSuccess);
  const onErrorEvent = useEvent(onError);
  const onSettledEvent = useEvent(onSettled);
  const result = useQuery({
    queryKey: [resource, "getList", { pagination, sort, filter, meta }],
    queryFn: (queryParams) => dataProvider.getList(resource, {
      pagination,
      sort,
      filter,
      meta,
      signal: dataProvider.supportAbortSignal === true ? queryParams.signal : void 0
    }).then(({ data, total, pageInfo, meta: meta2 }) => ({
      data,
      total,
      pageInfo,
      meta: meta2
    })),
    ...queryOptions
  });
  const metaValue = (0, import_react44.useRef)(meta);
  const resourceValue = (0, import_react44.useRef)(resource);
  (0, import_react44.useEffect)(() => {
    metaValue.current = meta;
  }, [meta]);
  (0, import_react44.useEffect)(() => {
    resourceValue.current = resource;
  }, [resource]);
  (0, import_react44.useEffect)(() => {
    var _a;
    if (result.data === void 0 || result.error != null || result.isFetching)
      return;
    if (((_a = result.data) == null ? void 0 : _a.data) && result.data.data.length <= MAX_DATA_LENGTH_TO_CACHE) {
      result.data.data.forEach((record) => {
        queryClient.setQueryData(
          [
            resourceValue.current,
            "getOne",
            { id: String(record.id), meta: metaValue.current }
          ],
          (oldRecord) => oldRecord ?? record
        );
      });
    }
    onSuccessEvent(result.data);
  }, [
    onSuccessEvent,
    queryClient,
    result.data,
    result.error,
    result.isFetching
  ]);
  (0, import_react44.useEffect)(() => {
    if (result.error == null || result.isFetching) return;
    onErrorEvent(result.error);
  }, [onErrorEvent, result.error, result.isFetching]);
  (0, import_react44.useEffect)(() => {
    if (result.status === "pending" || result.isFetching) return;
    onSettledEvent(result.data, result.error);
  }, [
    onSettledEvent,
    result.data,
    result.error,
    result.status,
    result.isFetching
  ]);
  return (0, import_react44.useMemo)(
    () => result.data ? {
      ...result,
      ...result.data
    } : result,
    [result]
  );
};
var noop2 = () => void 0;

// node_modules/ra-core/src/dataProvider/useGetMany.ts
var import_react45 = __toESM(require_react());
var useGetMany = (resource, params, options = {}) => {
  const { ids, meta } = params;
  const dataProvider = useDataProvider();
  const queryClient = useQueryClient();
  const {
    onError = noop3,
    onSuccess = noop3,
    onSettled = noop3,
    enabled,
    ...queryOptions
  } = options;
  const onSuccessEvent = useEvent(onSuccess);
  const onErrorEvent = useEvent(onError);
  const onSettledEvent = useEvent(onSettled);
  const result = useQuery({
    queryKey: [
      resource,
      "getMany",
      {
        ids: !ids || ids.length === 0 ? [] : ids.map((id) => String(id)),
        meta
      }
    ],
    queryFn: (queryParams) => {
      if (!ids || ids.length === 0) {
        return Promise.resolve([]);
      }
      return dataProvider.getMany(resource, {
        ids,
        meta,
        signal: dataProvider.supportAbortSignal === true ? queryParams.signal : void 0
      }).then(({ data }) => data);
    },
    placeholderData: () => {
      const records = !ids || ids.length === 0 ? [] : ids.map(
        (id) => queryClient.getQueryData([
          resource,
          "getOne",
          { id: String(id), meta }
        ])
      );
      if (records.some((record) => record === void 0)) {
        return void 0;
      } else {
        return records;
      }
    },
    retry: false,
    enabled: enabled ?? ids != null,
    ...queryOptions
  });
  const metaValue = (0, import_react45.useRef)(meta);
  const resourceValue = (0, import_react45.useRef)(resource);
  (0, import_react45.useEffect)(() => {
    metaValue.current = meta;
  }, [meta]);
  (0, import_react45.useEffect)(() => {
    resourceValue.current = resource;
  }, [resource]);
  (0, import_react45.useEffect)(() => {
    if (result.data === void 0 || result.error != null || result.isFetching)
      return;
    result.data.forEach((record) => {
      queryClient.setQueryData(
        [
          resourceValue.current,
          "getOne",
          { id: String(record.id), meta: metaValue.current }
        ],
        (oldRecord) => oldRecord ?? record
      );
    });
    onSuccessEvent(result.data);
  }, [
    queryClient,
    onSuccessEvent,
    result.data,
    result.error,
    result.isFetching
  ]);
  (0, import_react45.useEffect)(() => {
    if (result.error == null || result.isFetching) return;
    onErrorEvent(result.error);
  }, [onErrorEvent, result.error, result.isFetching]);
  (0, import_react45.useEffect)(() => {
    if (result.status === "pending" || result.isFetching) return;
    onSettledEvent(result.data, result.error);
  }, [
    onSettledEvent,
    result.data,
    result.error,
    result.status,
    result.isFetching
  ]);
  return result;
};
var noop3 = () => void 0;

// node_modules/ra-core/src/dataProvider/useGetManyAggregate.ts
var import_react46 = __toESM(require_react());
var import_union = __toESM(require_union());
var useGetManyAggregate = (resource, params, options = {}) => {
  const dataProvider = useDataProvider();
  const queryClient = useQueryClient();
  const {
    onError = noop4,
    onSuccess = noop4,
    onSettled = noop4,
    enabled,
    ...queryOptions
  } = options;
  const onSuccessEvent = useEvent(onSuccess);
  const onErrorEvent = useEvent(onError);
  const onSettledEvent = useEvent(onSettled);
  const { ids, meta } = params;
  const placeholderData = (0, import_react46.useMemo)(() => {
    const records = (Array.isArray(ids) ? ids : [ids]).map(
      (id) => queryClient.getQueryData([
        resource,
        "getOne",
        { id: String(id), meta }
      ])
    );
    if (records.some((record) => record === void 0)) {
      return void 0;
    } else {
      return records;
    }
  }, [ids, queryClient, resource, meta]);
  const result = useQuery({
    queryKey: [
      resource,
      "getMany",
      {
        ids: (Array.isArray(ids) ? ids : [ids]).map((id) => String(id)),
        meta
      }
    ],
    queryFn: (queryParams) => new Promise((resolve, reject) => {
      if (!ids || ids.length === 0) {
        return resolve([]);
      }
      return callGetManyQueries({
        resource,
        ids,
        meta,
        resolve,
        reject,
        dataProvider,
        queryClient,
        signal: dataProvider.supportAbortSignal === true ? queryParams.signal : void 0
      });
    }),
    placeholderData,
    enabled: enabled ?? ids != null,
    retry: false,
    ...queryOptions
  });
  const metaValue = (0, import_react46.useRef)(meta);
  const resourceValue = (0, import_react46.useRef)(resource);
  (0, import_react46.useEffect)(() => {
    metaValue.current = meta;
  }, [meta]);
  (0, import_react46.useEffect)(() => {
    resourceValue.current = resource;
  }, [resource]);
  (0, import_react46.useEffect)(() => {
    if (result.data === void 0 || result.error != null || result.isFetching)
      return;
    (result.data ?? []).forEach((record) => {
      queryClient.setQueryData(
        [
          resourceValue.current,
          "getOne",
          { id: String(record.id), meta: metaValue.current }
        ],
        (oldRecord) => oldRecord ?? record
      );
    });
    onSuccessEvent(result.data);
  }, [
    queryClient,
    onSuccessEvent,
    result.data,
    result.error,
    result.isFetching
  ]);
  (0, import_react46.useEffect)(() => {
    if (result.error == null || result.isFetching) return;
    onErrorEvent(result.error);
  }, [onErrorEvent, result.error, result.isFetching]);
  (0, import_react46.useEffect)(() => {
    if (result.status === "pending" || result.isFetching) return;
    onSettledEvent(result.data, result.error);
  }, [
    onSettledEvent,
    result.data,
    result.error,
    result.status,
    result.isFetching
  ]);
  return result;
};
var batch = (fn) => {
  let capturedArgs = [];
  let timeout = null;
  return (arg) => {
    capturedArgs.push(arg);
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => {
      timeout = null;
      fn([...capturedArgs]);
      capturedArgs = [];
    }, 0);
  };
};
var callGetManyQueries = batch((calls) => {
  const dataProvider = calls[0].dataProvider;
  const queryClient = calls[0].queryClient;
  const callsByResource = calls.reduce(
    (acc, callArgs) => {
      if (!acc[callArgs.resource]) {
        acc[callArgs.resource] = [];
      }
      acc[callArgs.resource].push(callArgs);
      return acc;
    },
    {}
  );
  Object.keys(callsByResource).forEach((resource) => {
    const callsForResource = callsByResource[resource];
    const aggregatedIds = callsForResource.reduce((acc, { ids }) => (0, import_union.default)(acc, ids), []).filter((v) => v != null && v !== "");
    const uniqueMeta = callsForResource.reduce(
      (acc, { meta }) => meta || acc,
      void 0
    );
    if (aggregatedIds.length === 0) {
      callsForResource.forEach(({ resolve }) => {
        resolve([]);
      });
      return;
    }
    const callThatHasAllAggregatedIds = callsForResource.find(
      ({ ids, signal }) => JSON.stringify(ids) === JSON.stringify(aggregatedIds) && !(signal == null ? void 0 : signal.aborted)
    );
    if (callThatHasAllAggregatedIds) {
      const { dataProvider: dataProvider2, resource: resource2, ids, meta, signal } = callThatHasAllAggregatedIds;
      dataProvider2.getMany(resource2, { ids, meta, signal }).then(({ data }) => data).then(
        (data) => {
          callsForResource.forEach(({ ids: ids2, resolve }) => {
            resolve(
              data.filter(
                (record) => ids2.map((id) => String(id)).includes(String(record.id))
              )
            );
          });
        },
        (error) => {
          callsForResource.forEach(({ reject }) => {
            reject(error);
          });
        }
      );
      return;
    }
    queryClient.fetchQuery({
      queryKey: [
        resource,
        "getMany",
        {
          ids: aggregatedIds.map((id) => String(id)),
          meta: uniqueMeta
        }
      ],
      queryFn: (queryParams) => dataProvider.getMany(resource, {
        ids: aggregatedIds,
        meta: uniqueMeta,
        signal: dataProvider.supportAbortSignal === true ? queryParams.signal : void 0
      }).then(({ data }) => data)
    }).then((data) => {
      callsForResource.forEach(({ ids, resolve }) => {
        resolve(
          data.filter(
            (record) => ids.map((id) => String(id)).includes(String(record.id))
          )
        );
      });
    }).catch(
      (error) => callsForResource.forEach(({ reject }) => reject(error))
    );
  });
});
var noop4 = () => void 0;

// node_modules/ra-core/src/dataProvider/useGetManyReference.ts
var import_react47 = __toESM(require_react());
var useGetManyReference = (resource, params = {}, options = {}) => {
  const {
    target,
    id,
    pagination = { page: 1, perPage: 25 },
    sort = { field: "id", order: "DESC" },
    filter = {},
    meta
  } = params;
  const dataProvider = useDataProvider();
  const queryClient = useQueryClient();
  const {
    onError = noop5,
    onSuccess = noop5,
    onSettled = noop5,
    ...queryOptions
  } = options;
  const onSuccessEvent = useEvent(onSuccess);
  const onErrorEvent = useEvent(onError);
  const onSettledEvent = useEvent(onSettled);
  const result = useQuery({
    queryKey: [
      resource,
      "getManyReference",
      { target, id, pagination, sort, filter, meta }
    ],
    queryFn: (queryParams) => {
      if (!target || id == null) {
        return Promise.reject(new Error("target and id are required"));
      }
      return dataProvider.getManyReference(resource, {
        target,
        id,
        pagination,
        sort,
        filter,
        meta,
        signal: dataProvider.supportAbortSignal === true ? queryParams.signal : void 0
      }).then(({ data, total, pageInfo, meta: meta2 }) => ({
        data,
        total,
        pageInfo,
        meta: meta2
      }));
    },
    ...queryOptions
  });
  (0, import_react47.useEffect)(() => {
    var _a, _b;
    if (result.data === void 0) return;
    (_b = (_a = result.data) == null ? void 0 : _a.data) == null ? void 0 : _b.forEach((record) => {
      queryClient.setQueryData(
        [resource, "getOne", { id: String(record.id), meta }],
        (oldRecord) => oldRecord ?? record
      );
    });
    onSuccessEvent(result.data);
  }, [queryClient, meta, onSuccessEvent, resource, result.data]);
  (0, import_react47.useEffect)(() => {
    if (result.error == null) return;
    onErrorEvent(result.error);
  }, [onErrorEvent, result.error]);
  (0, import_react47.useEffect)(() => {
    if (result.status === "pending") return;
    onSettledEvent(result.data, result.error);
  }, [onSettledEvent, result.data, result.error, result.status]);
  return (0, import_react47.useMemo)(
    () => result.data ? {
      ...result,
      ...result.data
    } : result,
    [result]
  );
};
var noop5 = () => void 0;

// node_modules/ra-core/src/controller/useFilterState.ts
var import_react48 = __toESM(require_react());
var import_debounce2 = __toESM(require_debounce());
var import_isEqual2 = __toESM(require_isEqual());
var defaultFilter = {};
var defaultFilterToQuery = (v) => ({ q: v });
var useFilterState_default = ({
  filterToQuery = defaultFilterToQuery,
  permanentFilter = {},
  debounceTime = 500
}) => {
  const permanentFilterProp = (0, import_react48.useRef)(permanentFilter);
  const latestValue = (0, import_react48.useRef)();
  const [filter, setFilterValue] = (0, import_react48.useState)({
    ...permanentFilter,
    ...filterToQuery("")
  });
  const permanentFilterSignature = JSON.stringify(permanentFilter);
  (0, import_react48.useEffect)(() => {
    if (!(0, import_isEqual2.default)(permanentFilterProp.current, permanentFilter)) {
      permanentFilterProp.current = permanentFilter;
      setFilterValue({
        ...permanentFilter,
        ...filterToQuery(latestValue.current || "")
      });
    }
  }, [permanentFilterSignature, permanentFilterProp, filterToQuery]);
  const setFilter = (0, import_react48.useCallback)(
    (0, import_debounce2.default)((value) => {
      setFilterValue({
        ...permanentFilter,
        ...filterToQuery(value)
      });
      latestValue.current = value;
    }, debounceTime),
    [permanentFilterSignature]
    // eslint-disable-line react-hooks/exhaustive-deps
  );
  return {
    filter: filter ?? defaultFilter,
    setFilter
  };
};

// node_modules/ra-core/src/controller/useSortState.ts
var import_react49 = __toESM(require_react());

// node_modules/ra-core/src/controller/list/queryReducer.ts
var import_set3 = __toESM(require_set());

// node_modules/ra-core/src/util/shallowEqual.ts
function is(x, y) {
  if (x === y) {
    return x !== 0 || y !== 0 || 1 / x === 1 / y;
  } else {
    return x !== x && y !== y;
  }
}
var shallowEqual = (objA, objB) => {
  if (is(objA, objB)) return true;
  if (typeof objA !== "object" || objA === null || typeof objB !== "object" || objB === null) {
    return false;
  }
  const keysA = Object.keys(objA);
  const keysB = Object.keys(objB);
  if (keysA.length !== keysB.length) return false;
  for (let i = 0; i < keysA.length; i++) {
    if (!Object.prototype.hasOwnProperty.call(objB, keysA[i]) || !is(objA[keysA[i]], objB[keysA[i]])) {
      return false;
    }
  }
  return true;
};

// node_modules/ra-core/src/util/removeEmpty.ts
var isObject = (obj) => obj && Object.prototype.toString.call(obj) === "[object Object]";
var isEmpty = (obj) => obj instanceof Date ? false : obj === "" || obj === null || obj === void 0 || shallowEqual(obj, {});
var removeEmpty = (object) => Object.keys(object).reduce((acc, key) => {
  let child = object[key];
  if (isObject(object[key])) {
    child = removeEmpty(object[key]);
  }
  return isEmpty(child) ? acc : { ...acc, [key]: child };
}, {});
var removeEmpty_default = removeEmpty;

// node_modules/ra-core/src/util/removeKey.ts
var removeKey = (target, path) => Object.keys(target).reduce((acc, key) => {
  if (key !== path) {
    return Object.assign({}, acc, { [key]: target[key] });
  }
  return acc;
}, {});
var deepRemoveKey = (target, path) => {
  const paths = path.split(".");
  if (paths.length === 1) {
    return removeKey(target, path);
  }
  const deepKey = paths[0];
  if (target[deepKey] === void 0) {
    return target;
  }
  const deep = deepRemoveKey(target[deepKey], paths.slice(1).join("."));
  if (Object.keys(deep).length === 0) {
    return removeKey(target, deepKey);
  }
  return Object.assign({}, target, { [deepKey]: deep });
};
var removeKey_default = deepRemoveKey;

// node_modules/ra-core/src/controller/list/queryReducer.ts
var SET_SORT = "SET_SORT";
var SORT_ASC = "ASC";
var SORT_DESC = "DESC";
var SET_PAGE = "SET_PAGE";
var SET_PER_PAGE = "SET_PER_PAGE";
var SET_FILTER = "SET_FILTER";
var SHOW_FILTER = "SHOW_FILTER";
var HIDE_FILTER = "HIDE_FILTER";
var oppositeOrder = (direction) => direction === SORT_DESC ? SORT_ASC : SORT_DESC;
var queryReducer = (previousState, action) => {
  switch (action.type) {
    case SET_SORT:
      if (action.payload.field === previousState.sort) {
        return {
          ...previousState,
          order: action.payload.order ?? oppositeOrder(previousState.order),
          page: 1
        };
      }
      return {
        ...previousState,
        sort: action.payload.field,
        order: action.payload.order || SORT_ASC,
        page: 1
      };
    case SET_PAGE:
      return { ...previousState, page: action.payload };
    case SET_PER_PAGE:
      return { ...previousState, page: 1, perPage: action.payload };
    case SET_FILTER: {
      return {
        ...previousState,
        page: 1,
        filter: action.payload.filter,
        displayedFilters: action.payload.displayedFilters ? action.payload.displayedFilters : previousState.displayedFilters
      };
    }
    case SHOW_FILTER: {
      if (previousState.displayedFilters && previousState.displayedFilters[action.payload.filterName]) {
        return previousState;
      }
      return {
        ...previousState,
        filter: typeof action.payload.defaultValue !== "undefined" ? (0, import_set3.default)(
          previousState.filter,
          action.payload.filterName,
          action.payload.defaultValue
        ) : previousState.filter,
        // we don't use lodash.set() for displayed filters
        // to avoid problems with compound filter names (e.g. 'author.name')
        displayedFilters: {
          ...previousState.displayedFilters,
          [action.payload.filterName]: true
        }
      };
    }
    case HIDE_FILTER: {
      return {
        ...previousState,
        filter: removeEmpty_default(
          removeKey_default(previousState.filter, action.payload)
        ),
        // we don't use lodash.set() for displayed filters
        // to avoid problems with compound filter names (e.g. 'author.name')
        displayedFilters: previousState.displayedFilters ? Object.keys(previousState.displayedFilters).reduce(
          (filters, filter) => {
            return filter !== action.payload ? { ...filters, [filter]: true } : filters;
          },
          {}
        ) : previousState.displayedFilters
      };
    }
    default:
      return previousState;
  }
};
var queryReducer_default = queryReducer;

// node_modules/ra-core/src/controller/useSortState.ts
var sortReducer = (state, action) => {
  switch (action.type) {
    case "SET_SORT":
      return action.payload;
    case "SET_SORT_FIELD": {
      const field = action.payload;
      const order = state.field === field ? state.order === SORT_ASC ? SORT_DESC : SORT_ASC : SORT_ASC;
      return { field, order };
    }
    case "SET_SORT_ORDER": {
      const order = action.payload;
      if (!state.field) {
        throw new Error(
          "cannot change the order on an undefined sort field"
        );
      }
      return {
        field: state.field,
        order
      };
    }
    default:
      return state;
  }
};
var defaultSort = { field: "", order: "ASC" };
var useSortState = (initialSort = defaultSort) => {
  const [sort, dispatch] = (0, import_react49.useReducer)(sortReducer, initialSort);
  const isFirstRender = (0, import_react49.useRef)(true);
  (0, import_react49.useEffect)(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }
    dispatch({ type: "SET_SORT", payload: initialSort });
  }, [initialSort.field, initialSort.order]);
  return {
    setSort: (0, import_react49.useCallback)(
      (sort2) => dispatch({ type: "SET_SORT", payload: sort2 }),
      [dispatch]
    ),
    setSortField: (0, import_react49.useCallback)(
      (field) => dispatch({ type: "SET_SORT_FIELD", payload: field }),
      [dispatch]
    ),
    setSortOrder: (0, import_react49.useCallback)(
      (order) => dispatch({ type: "SET_SORT_ORDER", payload: order }),
      [dispatch]
    ),
    sort
  };
};
var useSortState_default = useSortState;

// node_modules/ra-core/src/controller/usePaginationState.ts
var import_react50 = __toESM(require_react());
var paginationReducer = (prevState, nextState) => {
  return {
    ...prevState,
    ...nextState
  };
};
var defaultPagination = {
  page: 1,
  perPage: 25
};
var usePaginationState_default = (initialPagination = {}) => {
  const [pagination, setPagination] = (0, import_react50.useReducer)(paginationReducer, {
    ...defaultPagination,
    ...initialPagination
  });
  const isFirstRender = (0, import_react50.useRef)(true);
  const setPerPage = (0, import_react50.useCallback)(
    (perPage) => setPagination({ perPage, page: 1 }),
    []
  );
  const setPage = (0, import_react50.useCallback)((page) => setPagination({ page }), []);
  (0, import_react50.useEffect)(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }
    setPerPage(initialPagination.perPage || 25);
  }, [initialPagination.perPage, setPerPage]);
  return {
    page: pagination.page,
    perPage: pagination.perPage,
    pagination,
    setPage,
    setPerPage,
    setPagination
  };
};

// node_modules/ra-core/src/controller/checkMinimumRequiredProps.tsx
var import_jsx_runtime12 = __toESM(require_jsx_runtime());
var useCheckMinimumRequiredProps = (displayName, requiredProps, props) => {
  const propNames = Object.keys(props);
  const missingProps = requiredProps.filter(
    (prop) => !propNames.includes(prop)
  );
  if (missingProps.length > 0) {
    throw new Error(
      `<${displayName}> component is not properly configured, some essential props are missing.
Be sure to pass the props from the parent. Example:

const My${displayName} = props => (
    <${displayName} {...props}></${displayName}>
);

The missing props are: ${missingProps.join(", ")}`
    );
  }
};

// node_modules/ra-core/src/controller/button/useDeleteWithUndoController.tsx
var import_react51 = __toESM(require_react());
var useDeleteWithUndoController = (props) => {
  const {
    record,
    redirect: redirectTo = "list",
    onClick,
    mutationOptions = {},
    successMessage
  } = props;
  const { meta: mutationMeta, ...otherMutationOptions } = mutationOptions;
  const resource = useResourceContext(props);
  const notify = useNotify();
  const unselect = useUnselect(resource);
  const redirect = useRedirect();
  const translate = useTranslate();
  const [deleteOne, { isPending }] = useDelete(
    resource,
    void 0,
    {
      onSuccess: () => {
        notify(
          successMessage ?? `resources.${resource}.notifications.deleted`,
          {
            type: "info",
            messageArgs: {
              smart_count: 1,
              _: translate("ra.notification.deleted", {
                smart_count: 1
              })
            },
            undoable: true
          }
        );
        record && unselect([record.id]);
        redirect(redirectTo, resource);
      },
      onError: (error) => {
        notify(
          typeof error === "string" ? error : (error == null ? void 0 : error.message) || "ra.notification.http_error",
          {
            type: "error",
            messageArgs: {
              _: typeof error === "string" ? error : (error == null ? void 0 : error.message) ? error.message : void 0
            }
          }
        );
      }
    }
  );
  const handleDelete = (0, import_react51.useCallback)(
    (event) => {
      event.stopPropagation();
      if (!record) {
        throw new Error(
          "The record cannot be deleted because no record has been passed"
        );
      }
      deleteOne(
        resource,
        {
          id: record.id,
          previousData: record,
          meta: mutationMeta
        },
        {
          mutationMode: "undoable",
          ...otherMutationOptions
        }
      );
      if (typeof onClick === "function") {
        onClick(event);
      }
    },
    [
      deleteOne,
      mutationMeta,
      otherMutationOptions,
      onClick,
      record,
      resource
    ]
  );
  return { isPending, isLoading: isPending, handleDelete };
};
var useDeleteWithUndoController_default = useDeleteWithUndoController;

// node_modules/ra-core/src/controller/button/useDeleteWithConfirmController.tsx
var import_react52 = __toESM(require_react());
var useDeleteWithConfirmController = (props) => {
  const {
    record,
    redirect: redirectTo = "list",
    mutationMode,
    onClick,
    mutationOptions = {},
    successMessage
  } = props;
  const { meta: mutationMeta, ...otherMutationOptions } = mutationOptions;
  const resource = useResourceContext(props);
  const [open, setOpen] = (0, import_react52.useState)(false);
  const notify = useNotify();
  const unselect = useUnselect(resource);
  const redirect = useRedirect();
  const translate = useTranslate();
  const [deleteOne, { isPending }] = useDelete(
    resource,
    void 0,
    {
      onSuccess: () => {
        setOpen(false);
        notify(
          successMessage ?? `resources.${resource}.notifications.deleted`,
          {
            type: "info",
            messageArgs: {
              smart_count: 1,
              _: translate("ra.notification.deleted", {
                smart_count: 1
              })
            },
            undoable: mutationMode === "undoable"
          }
        );
        record && unselect([record.id]);
        redirect(redirectTo, resource);
      },
      onError: (error) => {
        setOpen(false);
        notify(
          typeof error === "string" ? error : (error == null ? void 0 : error.message) || "ra.notification.http_error",
          {
            type: "error",
            messageArgs: {
              _: typeof error === "string" ? error : (error == null ? void 0 : error.message) ? error.message : void 0
            }
          }
        );
      }
    }
  );
  const handleDialogOpen = (e) => {
    setOpen(true);
    e.stopPropagation();
  };
  const handleDialogClose = (e) => {
    setOpen(false);
    e.stopPropagation();
  };
  const handleDelete = (0, import_react52.useCallback)(
    (event) => {
      event.stopPropagation();
      if (!record) {
        throw new Error(
          "The record cannot be deleted because no record has been passed"
        );
      }
      deleteOne(
        resource,
        {
          id: record.id,
          previousData: record,
          meta: mutationMeta
        },
        {
          mutationMode,
          ...otherMutationOptions
        }
      );
      if (typeof onClick === "function") {
        onClick(event);
      }
    },
    [
      deleteOne,
      mutationMeta,
      mutationMode,
      otherMutationOptions,
      onClick,
      record,
      resource
    ]
  );
  return {
    open,
    isPending,
    isLoading: isPending,
    handleDialogOpen,
    handleDialogClose,
    handleDelete
  };
};
var useDeleteWithConfirmController_default = useDeleteWithConfirmController;

// node_modules/ra-core/src/controller/create/useCreateController.ts
var import_react58 = __toESM(require_react());

// node_modules/ra-core/src/controller/saveContext/SaveContext.ts
var import_react53 = __toESM(require_react());
var SaveContext = (0, import_react53.createContext)({});

// node_modules/ra-core/src/controller/saveContext/SaveContextProvider.tsx
var import_jsx_runtime13 = __toESM(require_jsx_runtime());
var SaveContextProvider = ({ children, value }) => (0, import_jsx_runtime13.jsx)(SaveContext.Provider, { value, children });

// node_modules/ra-core/src/controller/saveContext/usePickSaveContext.ts
var import_react54 = __toESM(require_react());
var import_pick = __toESM(require_pick());
var usePickSaveContext = (context) => {
  const value = (0, import_react54.useMemo)(
    () => (0, import_pick.default)(context, [
      "save",
      "saving",
      "mutationMode",
      "registerMutationMiddleware",
      "unregisterMutationMiddleware"
    ]),
    /* eslint-disable react-hooks/exhaustive-deps */
    [
      context.save,
      context.saving,
      context.mutationMode,
      context.registerMutationMiddleware,
      context.unregisterMutationMiddleware
    ]
    /* eslint-enable react-hooks/exhaustive-deps */
  );
  return value;
};

// node_modules/ra-core/src/controller/saveContext/useSaveContext.ts
var import_react55 = __toESM(require_react());
var useSaveContext = (_props) => {
  return (0, import_react55.useContext)(SaveContext);
};

// node_modules/ra-core/src/controller/saveContext/useMutationMiddlewares.ts
var import_react56 = __toESM(require_react());
var useMutationMiddlewares = () => {
  const callbacks = (0, import_react56.useRef)([]);
  const registerMutationMiddleware = (0, import_react56.useCallback)(
    (callback) => {
      callbacks.current.push(callback);
    },
    []
  );
  const unregisterMutationMiddleware = (0, import_react56.useCallback)(
    (callback) => {
      callbacks.current = callbacks.current.filter((cb) => cb !== callback);
    },
    []
  );
  const getMutateWithMiddlewares = (0, import_react56.useCallback)((fn) => {
    const currentCallbacks = [...callbacks.current];
    return (...args) => {
      let index = currentCallbacks.length - 1;
      const next = (...newArgs) => {
        index--;
        if (index >= 0) {
          return currentCallbacks[index](...newArgs, next);
        } else {
          return fn(...newArgs);
        }
      };
      if (currentCallbacks.length > 0) {
        return currentCallbacks[index](...args, next);
      }
      return fn(...args);
    };
  }, []);
  const functions = (0, import_react56.useMemo)(
    () => ({
      registerMutationMiddleware,
      getMutateWithMiddlewares,
      unregisterMutationMiddleware
    }),
    [
      registerMutationMiddleware,
      getMutateWithMiddlewares,
      unregisterMutationMiddleware
    ]
  );
  return functions;
};

// node_modules/ra-core/src/controller/saveContext/useRegisterMutationMiddleware.ts
var import_react57 = __toESM(require_react());
var useRegisterMutationMiddleware = (callback) => {
  const { registerMutationMiddleware, unregisterMutationMiddleware } = useSaveContext();
  (0, import_react57.useEffect)(() => {
    if (!registerMutationMiddleware || !unregisterMutationMiddleware) {
      return;
    }
    registerMutationMiddleware(callback);
    return () => {
      unregisterMutationMiddleware(callback);
    };
  }, [callback, registerMutationMiddleware, unregisterMutationMiddleware]);
};

// node_modules/ra-core/src/controller/create/useCreateController.ts
var useCreateController = (props = {}) => {
  const {
    disableAuthentication,
    record,
    redirect: redirectTo,
    transform: transform2,
    mutationOptions = {}
  } = props;
  const resource = useResourceContext(props);
  if (!resource) {
    throw new Error(
      "useCreateController requires a non-empty resource prop or context"
    );
  }
  const { isPending: isPendingAuthenticated } = useAuthenticated({
    enabled: !disableAuthentication
  });
  const { isPending: isPendingCanAccess } = useRequireAccess({
    action: "create",
    resource,
    // If disableAuthentication is true then isPendingAuthenticated will always be true so this hook is disabled
    enabled: !isPendingAuthenticated
  });
  const { hasEdit, hasShow } = useResourceDefinition(props);
  const finalRedirectTo = redirectTo ?? getDefaultRedirectRoute(hasShow, hasEdit);
  const translate = useTranslate();
  const notify = useNotify();
  const redirect = useRedirect();
  const { onSuccess, onError, meta, ...otherMutationOptions } = mutationOptions;
  const {
    registerMutationMiddleware,
    getMutateWithMiddlewares,
    unregisterMutationMiddleware
  } = useMutationMiddlewares();
  const [create, { isPending: saving }] = useCreate(resource, void 0, {
    onSuccess: async (data, variables, context) => {
      if (onSuccess) {
        return onSuccess(data, variables, context);
      }
      notify(`resources.${resource}.notifications.created`, {
        type: "info",
        messageArgs: {
          smart_count: 1,
          _: translate(`ra.notification.created`, {
            smart_count: 1
          })
        }
      });
      redirect(finalRedirectTo, resource, data.id, data);
    },
    onError: (error, variables, context) => {
      var _a;
      if (onError) {
        return onError(error, variables, context);
      }
      const validationErrors = (_a = error == null ? void 0 : error.body) == null ? void 0 : _a.errors;
      const hasValidationErrors = !!validationErrors && Object.keys(validationErrors).length > 0;
      if (!hasValidationErrors) {
        notify(
          typeof error === "string" ? error : error.message || "ra.notification.http_error",
          {
            type: "error",
            messageArgs: {
              _: typeof error === "string" ? error : error instanceof Error || typeof error === "object" && error !== null && error.hasOwnProperty("message") ? (
                // @ts-ignore
                error.message
              ) : void 0
            }
          }
        );
      }
    },
    ...otherMutationOptions,
    returnPromise: true,
    getMutateWithMiddlewares
  });
  const save = (0, import_react58.useCallback)(
    (data, {
      transform: transformFromSave,
      meta: metaFromSave,
      ...callTimeOptions
    } = {}) => Promise.resolve(
      transformFromSave ? transformFromSave(data) : transform2 ? transform2(data) : data
    ).then(async (data2) => {
      var _a;
      try {
        await create(
          resource,
          { data: data2, meta: metaFromSave ?? meta },
          callTimeOptions
        );
      } catch (error) {
        if ((error instanceof HttpError_default || typeof error === "object" && error !== null && error.hasOwnProperty("body")) && ((_a = error.body) == null ? void 0 : _a.errors) != null) {
          return error.body.errors;
        }
      }
    }),
    [create, meta, resource, transform2]
  );
  const getResourceLabel = useGetResourceLabel();
  const defaultTitle = translate("ra.page.create", {
    name: getResourceLabel(resource, 1)
  });
  return {
    isFetching: false,
    isLoading: false,
    isPending: disableAuthentication ? false : isPendingCanAccess,
    saving,
    defaultTitle,
    save,
    record,
    resource,
    redirect: finalRedirectTo,
    registerMutationMiddleware,
    unregisterMutationMiddleware
  };
};
var getDefaultRedirectRoute = (hasShow, hasEdit) => {
  if (hasEdit) {
    return "edit";
  }
  if (hasShow) {
    return "show";
  }
  return "list";
};

// node_modules/ra-core/src/controller/create/CreateContext.tsx
var import_react59 = __toESM(require_react());
var CreateContext = (0, import_react59.createContext)(null);
CreateContext.displayName = "CreateContext";

// node_modules/ra-core/src/controller/create/CreateContextProvider.tsx
var import_jsx_runtime14 = __toESM(require_jsx_runtime());
var CreateContextProvider = ({
  children,
  value
}) => (0, import_jsx_runtime14.jsx)(CreateContext.Provider, { value, children: (0, import_jsx_runtime14.jsx)(
  SaveContextProvider,
  {
    value: {
      ...usePickSaveContext(value),
      mutationMode: "pessimistic"
    },
    children: (0, import_jsx_runtime14.jsx)(
      RecordContextProvider,
      {
        value: value && value.record,
        children
      }
    )
  }
) });

// node_modules/ra-core/src/controller/create/CreateBase.tsx
var import_jsx_runtime15 = __toESM(require_jsx_runtime());
var CreateBase = ({
  children,
  loading = null,
  ...props
}) => {
  const controllerProps = useCreateController(props);
  const isAuthPending = useIsAuthPending({
    resource: controllerProps.resource,
    action: "create"
  });
  if (isAuthPending && !props.disableAuthentication) {
    return loading;
  }
  return (
    // We pass props.resource here as we don't need to create a new ResourceContext if the props is not provided
    (0, import_jsx_runtime15.jsx)(OptionalResourceContextProvider, { value: props.resource, children: (0, import_jsx_runtime15.jsx)(CreateContextProvider, { value: controllerProps, children }) })
  );
};

// node_modules/ra-core/src/controller/create/CreateController.tsx
var CreateController = ({
  children,
  ...props
}) => {
  const controllerProps = useCreateController(props);
  return children(controllerProps);
};

// node_modules/ra-core/src/controller/create/useCreateContext.tsx
var import_react60 = __toESM(require_react());
var useCreateContext = () => {
  const context = (0, import_react60.useContext)(CreateContext);
  if (!context) {
    throw new Error(
      "useCreateContext must be used inside a CreateContextProvider"
    );
  }
  return context;
};

// node_modules/ra-core/src/controller/edit/useEditController.ts
var import_react61 = __toESM(require_react());
var useEditController = (props = {}) => {
  const {
    disableAuthentication = false,
    id: propsId,
    mutationMode = "undoable",
    mutationOptions = {},
    queryOptions = {},
    redirect: redirectTo = DefaultRedirect,
    transform: transform2
  } = props;
  const resource = useResourceContext(props);
  if (!resource) {
    throw new Error(
      "useEditController requires a non-empty resource prop or context"
    );
  }
  const { isPending: isPendingAuthenticated } = useAuthenticated({
    enabled: !disableAuthentication
  });
  const { isPending: isPendingCanAccess } = useRequireAccess({
    action: "edit",
    resource,
    // If disableAuthentication is true then isPendingAuthenticated will always be true so this hook is disabled
    enabled: !isPendingAuthenticated
  });
  const getRecordRepresentation = useGetRecordRepresentation(resource);
  const translate = useTranslate();
  const notify = useNotify();
  const redirect = useRedirect();
  const refresh = useRefresh();
  const { id: routeId } = useParams();
  if (!routeId && !propsId) {
    throw new Error(
      "useEditController requires an id prop or a route with an /:id? parameter."
    );
  }
  const id = propsId ?? routeId;
  const { meta: queryMeta, ...otherQueryOptions } = queryOptions;
  const {
    meta: mutationMeta,
    onSuccess,
    onError,
    ...otherMutationOptions
  } = mutationOptions;
  const {
    registerMutationMiddleware,
    getMutateWithMiddlewares,
    unregisterMutationMiddleware
  } = useMutationMiddlewares();
  const {
    data: record,
    error,
    isLoading,
    isFetching,
    isPending,
    refetch: refetch2
  } = useGetOne(
    resource,
    { id, meta: queryMeta },
    {
      enabled: !isPendingAuthenticated && !isPendingCanAccess || disableAuthentication,
      onError: () => {
        notify("ra.notification.item_doesnt_exist", {
          type: "error"
        });
        redirect("list", resource);
        refresh();
      },
      refetchOnReconnect: false,
      refetchOnWindowFocus: false,
      retry: false,
      ...otherQueryOptions
    }
  );
  if (record && record.id && record.id != id) {
    throw new Error(
      `useEditController: Fetched record's id attribute (${record.id}) must match the requested 'id' (${id})`
    );
  }
  const getResourceLabel = useGetResourceLabel();
  const recordRepresentation = getRecordRepresentation(record);
  const defaultTitle = translate("ra.page.edit", {
    name: getResourceLabel(resource, 1),
    id,
    record,
    recordRepresentation: typeof recordRepresentation === "string" ? recordRepresentation : ""
  });
  const recordCached = { id, previousData: record };
  const [update, { isPending: saving }] = useUpdate(
    resource,
    recordCached,
    {
      onSuccess: async (data, variables, context) => {
        if (onSuccess) {
          return onSuccess(data, variables, context);
        }
        notify(`resources.${resource}.notifications.updated`, {
          type: "info",
          messageArgs: {
            smart_count: 1,
            _: translate("ra.notification.updated", {
              smart_count: 1
            })
          },
          undoable: mutationMode === "undoable"
        });
        redirect(redirectTo, resource, data.id, data);
      },
      onError: (error2, variables, context) => {
        var _a;
        if (onError) {
          return onError(error2, variables, context);
        }
        const validationErrors = (_a = error2 == null ? void 0 : error2.body) == null ? void 0 : _a.errors;
        const hasValidationErrors = !!validationErrors && Object.keys(validationErrors).length > 0;
        if (!hasValidationErrors || mutationMode !== "pessimistic") {
          notify(
            typeof error2 === "string" ? error2 : error2.message || "ra.notification.http_error",
            {
              type: "error",
              messageArgs: {
                _: typeof error2 === "string" ? error2 : error2 instanceof Error || typeof error2 === "object" && error2 !== null && error2.hasOwnProperty("message") ? (
                  // @ts-ignore
                  error2.message
                ) : void 0
              }
            }
          );
        }
      },
      ...otherMutationOptions,
      mutationMode,
      returnPromise: mutationMode === "pessimistic",
      getMutateWithMiddlewares
    }
  );
  const save = (0, import_react61.useCallback)(
    (data, {
      onSuccess: onSuccessFromSave,
      onError: onErrorFromSave,
      transform: transformFromSave,
      meta: metaFromSave
    } = {}) => Promise.resolve(
      transformFromSave ? transformFromSave(data, {
        previousData: recordCached.previousData
      }) : transform2 ? transform2(data, {
        previousData: recordCached.previousData
      }) : data
    ).then(async (data2) => {
      var _a;
      try {
        await update(
          resource,
          {
            id,
            data: data2,
            meta: metaFromSave ?? mutationMeta
          },
          {
            onError: onErrorFromSave,
            onSuccess: onSuccessFromSave
          }
        );
      } catch (error2) {
        if (((_a = error2.body) == null ? void 0 : _a.errors) != null) {
          return error2.body.errors;
        }
      }
    }),
    [
      id,
      mutationMeta,
      resource,
      transform2,
      update,
      recordCached.previousData
    ]
  );
  return {
    defaultTitle,
    error,
    isFetching,
    isLoading,
    isPending,
    mutationMode,
    record,
    redirect: redirectTo,
    refetch: refetch2,
    registerMutationMiddleware,
    resource,
    save,
    saving,
    unregisterMutationMiddleware
  };
};
var DefaultRedirect = "list";

// node_modules/ra-core/src/controller/record/WithRecord.tsx
var import_jsx_runtime16 = __toESM(require_jsx_runtime());
var WithRecord = ({
  render
}) => {
  const record = useRecordContext();
  return record ? (0, import_jsx_runtime16.jsx)(import_jsx_runtime16.Fragment, { children: render(record) }) : null;
};

// node_modules/ra-core/src/controller/record/OptionalRecordContextProvider.tsx
var import_jsx_runtime17 = __toESM(require_jsx_runtime());
var OptionalRecordContextProvider = ({
  value,
  children
}) => value ? (0, import_jsx_runtime17.jsx)(RecordContextProvider, { value, children }) : children;

// node_modules/ra-core/src/controller/record/RecordRepresentation.tsx
var import_jsx_runtime18 = __toESM(require_jsx_runtime());
var RecordRepresentation = (props) => {
  const record = useRecordContext(props);
  const resource = useResourceContext(props);
  const getRecordRepresentation = useGetRecordRepresentation(resource);
  return (0, import_jsx_runtime18.jsx)(import_jsx_runtime18.Fragment, { children: getRecordRepresentation(record) });
};

// node_modules/ra-core/src/controller/edit/EditContext.tsx
var import_react62 = __toESM(require_react());
var EditContext = (0, import_react62.createContext)(null);
EditContext.displayName = "EditContext";

// node_modules/ra-core/src/controller/edit/EditContextProvider.tsx
var import_jsx_runtime19 = __toESM(require_jsx_runtime());
var EditContextProvider = ({
  children,
  value
}) => (0, import_jsx_runtime19.jsx)(EditContext.Provider, { value, children: (0, import_jsx_runtime19.jsx)(SaveContextProvider, { value: usePickSaveContext(value), children: (0, import_jsx_runtime19.jsx)(
  RecordContextProvider,
  {
    value: value && value.record,
    children
  }
) }) });

// node_modules/ra-core/src/controller/edit/EditBase.tsx
var import_jsx_runtime20 = __toESM(require_jsx_runtime());
var EditBase = ({
  children,
  loading = null,
  ...props
}) => {
  const controllerProps = useEditController(props);
  const isAuthPending = useIsAuthPending({
    resource: controllerProps.resource,
    action: "edit"
  });
  if (isAuthPending && !props.disableAuthentication) {
    return loading;
  }
  return (
    // We pass props.resource here as we don't need to create a new ResourceContext if the props is not provided
    (0, import_jsx_runtime20.jsx)(OptionalResourceContextProvider, { value: props.resource, children: (0, import_jsx_runtime20.jsx)(EditContextProvider, { value: controllerProps, children }) })
  );
};

// node_modules/ra-core/src/controller/edit/EditController.tsx
var EditController = ({
  children,
  ...props
}) => {
  const controllerProps = useEditController(props);
  return children(controllerProps);
};

// node_modules/ra-core/src/controller/edit/useEditContext.tsx
var import_react63 = __toESM(require_react());
var useEditContext = () => {
  const context = (0, import_react63.useContext)(EditContext);
  if (!context) {
    throw new Error(
      "useEditContext must be used inside an EditContextProvider"
    );
  }
  return context;
};

// node_modules/ra-core/src/controller/field/ReferenceFieldContext.tsx
var import_react64 = __toESM(require_react());
var ReferenceFieldContext = (0, import_react64.createContext)(null);
var ReferenceFieldContextProvider = ReferenceFieldContext.Provider;
var useReferenceFieldContext = () => {
  const context = (0, import_react64.useContext)(ReferenceFieldContext);
  if (!context) {
    throw new Error(
      "useReferenceFieldContext must be used inside a ReferenceFieldContextProvider"
    );
  }
  return context;
};

// node_modules/ra-core/src/controller/field/useReferenceFieldController.ts
var import_react65 = __toESM(require_react());

// node_modules/ra-core/src/controller/useReference.ts
var useReference = ({
  reference,
  id,
  options = {}
}) => {
  const { meta, ...otherQueryOptions } = options;
  const { data, error, isLoading, isFetching, isPending, refetch: refetch2 } = useGetManyAggregate(
    reference,
    { ids: [id], meta },
    otherQueryOptions
  );
  return {
    referenceRecord: error ? void 0 : data ? data[0] : void 0,
    refetch: refetch2,
    error,
    isLoading,
    isFetching,
    isPending
  };
};

// node_modules/ra-core/src/controller/field/useReferenceFieldController.ts
var useReferenceFieldController = (options) => {
  const { link, reference, queryOptions } = options;
  if (!reference) {
    throw new Error(
      'useReferenceFieldController: missing reference prop. You must provide a reference, e.g. reference="posts".'
    );
  }
  const id = useFieldValue(options);
  const referenceRecordQuery = useReference({
    reference,
    id,
    options: {
      ...queryOptions,
      enabled: ((queryOptions == null ? void 0 : queryOptions.enabled) == null || (queryOptions == null ? void 0 : queryOptions.enabled) === true) && id != null
    }
  });
  const path = useGetPathForRecord({
    record: referenceRecordQuery.referenceRecord,
    resource: reference,
    link
  });
  const result = (0, import_react65.useMemo)(
    () => ({
      ...referenceRecordQuery,
      link: path
    }),
    [path, referenceRecordQuery]
  );
  return result;
};

// node_modules/ra-core/src/controller/field/ReferenceFieldBase.tsx
var import_jsx_runtime21 = __toESM(require_jsx_runtime());
var ReferenceFieldBase = (props) => {
  const { children } = props;
  const controllerProps = useReferenceFieldController(props);
  return (0, import_jsx_runtime21.jsx)(ResourceContextProvider, { value: props.reference, children: (0, import_jsx_runtime21.jsx)(ReferenceFieldContextProvider, { value: controllerProps, children: (0, import_jsx_runtime21.jsx)(RecordContextProvider, { value: controllerProps.referenceRecord, children }) }) });
};

// node_modules/ra-core/src/controller/field/useReferenceArrayFieldController.ts
var import_get5 = __toESM(require_get());

// node_modules/ra-core/src/controller/list/useInfiniteListController.ts
var import_react70 = __toESM(require_react());

// node_modules/ra-core/src/export/defaultExporter.ts
var import_dist = __toESM(require_dist());

// node_modules/ra-core/src/export/downloadCSV.ts
var downloadCSV = (csv, filename = "export") => {
  const fakeLink = document.createElement("a");
  fakeLink.style.display = "none";
  document.body.appendChild(fakeLink);
  const blob = new Blob([csv], { type: "text/csv;charset=utf-8" });
  if (window.navigator && window.navigator.msSaveOrOpenBlob) {
    window.navigator.msSaveOrOpenBlob(blob, `${filename}.csv`);
  } else {
    fakeLink.setAttribute("href", URL.createObjectURL(blob));
    fakeLink.setAttribute("download", `${filename}.csv`);
    fakeLink.click();
  }
};

// node_modules/ra-core/src/export/defaultExporter.ts
var defaultExporter = (data, _, __, resource) => (0, import_dist.default)(data, (err, csv) => downloadCSV(csv, resource));

// node_modules/ra-core/src/export/ExporterContext.ts
var import_react66 = __toESM(require_react());
var ExporterContext = (0, import_react66.createContext)(defaultExporter);
ExporterContext.displayName = "ExporterContext";

// node_modules/ra-core/src/export/getRelatedIds.ts
var getRelatedIds = (records, field) => Array.from(
  new Set(
    records.filter((record) => record[field] != null).map((record) => record[field]).reduce((ids, value) => ids.concat(value), [])
  )
);

// node_modules/ra-core/src/export/fetchRelatedRecords.ts
var fetchRelatedRecords = (dataProvider) => (data, field, resource) => dataProvider.getMany(resource, { ids: getRelatedIds(data, field) }).then(
  ({ data: data2 }) => data2.reduce((acc, post) => {
    acc[post.id] = post;
    return acc;
  }, {})
);

// node_modules/ra-core/src/controller/list/useRecordSelection.ts
var import_react67 = __toESM(require_react());
var useRecordSelection = (args) => {
  const { resource = "", disableSyncWithStore = false } = args;
  const storeKey = `${resource}.selectedIds`;
  const [localIds, setLocalIds] = (0, import_react67.useState)(defaultSelection);
  const [storeIds, setStoreIds] = useStore(
    storeKey,
    defaultSelection
  );
  const resetStore = useRemoveFromStore(storeKey);
  const ids = disableSyncWithStore ? localIds : storeIds;
  const setIds = disableSyncWithStore ? setLocalIds : setStoreIds;
  const reset = (0, import_react67.useCallback)(() => {
    if (disableSyncWithStore) {
      setLocalIds(defaultSelection);
    } else {
      resetStore();
    }
  }, [disableSyncWithStore, resetStore]);
  const selectionModifiers = (0, import_react67.useMemo)(
    () => ({
      select: (idsToAdd) => {
        if (!idsToAdd) return;
        setIds([...idsToAdd]);
      },
      unselect(idsToRemove) {
        if (!idsToRemove || idsToRemove.length === 0) return;
        setIds((ids2) => {
          if (!Array.isArray(ids2)) return [];
          return ids2.filter((id) => !idsToRemove.includes(id));
        });
      },
      toggle: (id) => {
        if (typeof id === "undefined") return;
        setIds((ids2) => {
          if (!Array.isArray(ids2)) return [...ids2];
          const index = ids2.indexOf(id);
          return index > -1 ? [...ids2.slice(0, index), ...ids2.slice(index + 1)] : [...ids2, id];
        });
      },
      clearSelection: () => {
        reset();
      }
    }),
    [setIds, reset]
  );
  return [ids, selectionModifiers];
};
var defaultSelection = [];

// node_modules/ra-core/src/controller/list/useListParams.ts
var import_react69 = __toESM(require_react());
var import_query_string2 = __toESM(require_query_string());
var import_debounce3 = __toESM(require_debounce());

// node_modules/ra-core/src/util/hooks.ts
var import_react68 = __toESM(require_react());
var import_isEqual3 = __toESM(require_isEqual());
function useSafeSetState(initialState2) {
  const [state, setState] = (0, import_react68.useState)(initialState2);
  const mountedRef = (0, import_react68.useRef)(false);
  (0, import_react68.useEffect)(() => {
    mountedRef.current = true;
    return () => {
      mountedRef.current = false;
    };
  }, []);
  const safeSetState = (0, import_react68.useCallback)(
    (args) => {
      if (mountedRef.current) {
        return setState(args);
      }
    },
    [mountedRef, setState]
  );
  return [state, safeSetState];
}
function usePrevious(value) {
  const ref = (0, import_react68.useRef)();
  (0, import_react68.useEffect)(() => {
    ref.current = value;
  });
  return ref.current;
}
function useDeepCompareEffect(callback, inputs) {
  const cleanupRef = (0, import_react68.useRef)();
  (0, import_react68.useEffect)(() => {
    if (!(0, import_isEqual3.default)(previousInputs, inputs)) {
      cleanupRef.current = callback();
    }
    return cleanupRef.current;
  });
  const previousInputs = usePrevious(inputs);
}
function useTimeout(ms = 0, key = "") {
  const [ready, setReady] = (0, import_react68.useState)(false);
  (0, import_react68.useEffect)(() => {
    setReady(false);
    let timer2 = setTimeout(() => {
      setReady(true);
    }, ms);
    return () => {
      clearTimeout(timer2);
    };
  }, [key, ms, setReady]);
  return ready;
}
function useIsMounted() {
  const isMounted = (0, import_react68.useRef)(true);
  (0, import_react68.useEffect)(() => {
    isMounted.current = true;
    return () => {
      isMounted.current = false;
    };
  }, []);
  return isMounted;
}

// node_modules/ra-core/src/controller/list/useListParams.ts
var useListParams = ({
  debounce: debounce5 = 500,
  disableSyncWithLocation = false,
  filterDefaultValues,
  perPage = 10,
  resource,
  sort = defaultSort2,
  storeKey = disableSyncWithLocation ? false : `${resource}.listParams`
}) => {
  const location = useLocation();
  const navigate = useNavigate();
  const [localParams, setLocalParams] = (0, import_react69.useState)(defaultParams);
  const [params, setParams] = useStore(
    storeKey || `${resource}.listParams`,
    defaultParams
  );
  const tempParams = (0, import_react69.useRef)();
  const isMounted = useIsMounted();
  const requestSignature = [
    location.search,
    resource,
    storeKey,
    JSON.stringify(!storeKey ? localParams : params),
    JSON.stringify(filterDefaultValues),
    JSON.stringify(sort),
    perPage,
    disableSyncWithLocation
  ];
  const queryFromLocation = disableSyncWithLocation ? {} : parseQueryFromLocation(location);
  const query = (0, import_react69.useMemo)(
    () => getQuery({
      queryFromLocation,
      params: !storeKey ? localParams : params,
      filterDefaultValues,
      sort,
      perPage
    }),
    requestSignature
    // eslint-disable-line react-hooks/exhaustive-deps
  );
  (0, import_react69.useEffect)(() => {
    if (Object.keys(queryFromLocation).length > 0) {
      setParams(query);
    }
  }, [location.search]);
  const changeParams = (0, import_react69.useCallback)(
    (action) => {
      if (!isMounted.current) return;
      if (!tempParams.current) {
        tempParams.current = queryReducer_default(query, action);
        setTimeout(() => {
          if (!tempParams.current) {
            return;
          }
          if (disableSyncWithLocation && !storeKey) {
            setLocalParams(tempParams.current);
          } else if (disableSyncWithLocation && !!storeKey) {
            setParams(tempParams.current);
          } else {
            navigate(
              {
                search: `?${(0, import_query_string2.stringify)({
                  ...tempParams.current,
                  filter: JSON.stringify(
                    tempParams.current.filter
                  ),
                  displayedFilters: JSON.stringify(
                    tempParams.current.displayedFilters
                  )
                })}`
              },
              {
                state: {
                  _scrollToTop: action.type === SET_PAGE
                }
              }
            );
          }
          tempParams.current = void 0;
        }, 0);
      } else {
        tempParams.current = queryReducer_default(tempParams.current, action);
      }
    },
    [...requestSignature, navigate]
    // eslint-disable-line react-hooks/exhaustive-deps
  );
  const setSort = (0, import_react69.useCallback)(
    (sort2) => changeParams({
      type: SET_SORT,
      payload: sort2
    }),
    [changeParams]
  );
  const setPage = (0, import_react69.useCallback)(
    (newPage) => changeParams({ type: SET_PAGE, payload: newPage }),
    [changeParams]
  );
  const setPerPage = (0, import_react69.useCallback)(
    (newPerPage) => changeParams({ type: SET_PER_PAGE, payload: newPerPage }),
    [changeParams]
  );
  const filterValues = query.filter || emptyObject;
  const displayedFilterValues = query.displayedFilters || emptyObject;
  const debouncedSetFilters = (0, import_debounce3.default)((filter, displayedFilters) => {
    changeParams({
      type: SET_FILTER,
      payload: {
        filter: removeEmpty_default(filter),
        displayedFilters
      }
    });
  }, debounce5);
  const setFilters = (0, import_react69.useCallback)(
    (filter, displayedFilters = void 0, debounce6 = false) => debounce6 ? debouncedSetFilters(filter, displayedFilters) : changeParams({
      type: SET_FILTER,
      payload: {
        filter: removeEmpty_default(filter),
        displayedFilters
      }
    }),
    [changeParams]
    // eslint-disable-line react-hooks/exhaustive-deps
  );
  const hideFilter = (0, import_react69.useCallback)(
    (filterName) => {
      changeParams({
        type: HIDE_FILTER,
        payload: filterName
      });
    },
    [changeParams]
  );
  const showFilter = (0, import_react69.useCallback)(
    (filterName, defaultValue) => {
      changeParams({
        type: SHOW_FILTER,
        payload: {
          filterName,
          defaultValue
        }
      });
    },
    [changeParams]
  );
  return [
    {
      filterValues,
      requestSignature,
      ...query,
      displayedFilters: displayedFilterValues
    },
    {
      changeParams,
      setPage,
      setPerPage,
      setSort,
      setFilters,
      hideFilter,
      showFilter
    }
  ];
};
var parseObject = (query, field) => {
  if (query[field] && typeof query[field] === "string") {
    try {
      query[field] = JSON.parse(query[field]);
    } catch (err) {
      delete query[field];
    }
  }
};
var parseQueryFromLocation = ({ search }) => {
  const query = (0, import_query_string2.parse)(search);
  parseObject(query, "filter");
  parseObject(query, "displayedFilters");
  return query;
};
var hasCustomParams = (params) => {
  return params && params.filter && (Object.keys(params.filter).length > 0 || params.order != null || params.page !== 1 || params.perPage != null || params.sort != null);
};
var getQuery = ({
  queryFromLocation,
  params,
  filterDefaultValues,
  sort,
  perPage
}) => {
  const query = Object.keys(queryFromLocation).length > 0 ? queryFromLocation : hasCustomParams(params) ? { ...params } : { filter: filterDefaultValues || {} };
  if (!query.sort) {
    query.sort = sort.field;
    query.order = sort.order;
  }
  if (query.perPage == null) {
    query.perPage = perPage;
  }
  if (query.page == null) {
    query.page = 1;
  }
  return {
    ...query,
    page: getNumberOrDefault(query.page, 1),
    perPage: getNumberOrDefault(query.perPage, 10)
  };
};
var getNumberOrDefault = (possibleNumber, defaultValue) => {
  if (typeof possibleNumber === "undefined") {
    return defaultValue;
  }
  const parsedNumber = typeof possibleNumber === "string" ? parseInt(possibleNumber, 10) : possibleNumber;
  return isNaN(parsedNumber) ? defaultValue : parsedNumber;
};
var emptyObject = {};
var defaultSort2 = {
  field: "id",
  order: SORT_ASC
};
var defaultParams = {};

// node_modules/ra-core/src/controller/list/useSelectAll.tsx
var useSelectAll = (params) => {
  const { sort, filter } = params;
  const resource = useResourceContext(params);
  if (!resource) {
    throw new Error(
      "useSelectAll should be used inside a ResourceContextProvider or passed a resource prop"
    );
  }
  const dataProvider = useDataProvider();
  const queryClient = useQueryClient();
  const [, { select }] = useRecordSelection({ resource });
  const notify = useNotify();
  const handleSelectAll = useEvent(
    async ({
      queryOptions = {},
      limit = 250
    } = {}) => {
      var _a;
      const { meta, onSuccess, onError, ...otherQueryOptions } = queryOptions;
      try {
        const results = await queryClient.fetchQuery({
          queryKey: [
            resource,
            "getList",
            {
              pagination: { page: 1, perPage: limit },
              sort,
              filter,
              meta
            }
          ],
          queryFn: () => dataProvider.getList(resource, {
            pagination: {
              page: 1,
              perPage: limit
            },
            sort,
            filter,
            meta
          }),
          ...otherQueryOptions
        });
        const allIds = ((_a = results.data) == null ? void 0 : _a.map(({ id }) => id)) || [];
        select(allIds);
        if (allIds.length === limit) {
          notify("ra.message.select_all_limit_reached", {
            messageArgs: { max: limit },
            type: "warning"
          });
        }
        if (onSuccess) {
          onSuccess(results);
        }
        return results.data;
      } catch (error) {
        if (onError) {
          onError(error);
        } else {
          notify("ra.notification.http_error", { type: "warning" });
        }
      }
    }
  );
  return handleSelectAll;
};

// node_modules/ra-core/src/controller/list/useInfiniteListController.ts
var useInfiniteListController = (props = {}) => {
  const {
    debounce: debounce5 = 500,
    disableAuthentication = false,
    disableSyncWithLocation = false,
    exporter = defaultExporter,
    filter,
    filterDefaultValues,
    perPage = 10,
    queryOptions,
    sort,
    storeKey
  } = props;
  const resource = useResourceContext(props);
  const { meta, ...otherQueryOptions } = queryOptions ?? {};
  if (!resource) {
    throw new Error(
      `<InfiniteList> was called outside of a ResourceContext and without a resource prop. You must set the resource prop.`
    );
  }
  if (filter && (0, import_react70.isValidElement)(filter)) {
    throw new Error(
      "<InfiniteList> received a React element as `filter` props. If you intended to set the list filter elements, use the `filters` (with an s) prop instead. The `filter` prop is internal and should not be set by the developer."
    );
  }
  const { isPending: isPendingAuthenticated } = useAuthenticated({
    enabled: !disableAuthentication
  });
  const { isPending: isPendingCanAccess } = useRequireAccess({
    action: "list",
    resource,
    // If disableAuthentication is true then isPendingAuthenticated will always be true so this hook is disabled
    enabled: !isPendingAuthenticated
  });
  const translate = useTranslate();
  const notify = useNotify();
  const [query, queryModifiers] = useListParams({
    debounce: debounce5,
    disableSyncWithLocation,
    filterDefaultValues,
    perPage,
    resource,
    sort,
    storeKey
  });
  const [selectedIds, selectionModifiers] = useRecordSelection({ resource });
  const {
    data,
    total,
    error,
    isLoading,
    isPending,
    isFetching,
    hasNextPage,
    hasPreviousPage,
    fetchNextPage,
    isFetchingNextPage,
    fetchPreviousPage,
    isFetchingPreviousPage,
    refetch: refetch2
  } = useInfiniteGetList(
    resource,
    {
      pagination: {
        page: query.page,
        perPage: query.perPage
      },
      sort: { field: query.sort, order: query.order },
      filter: { ...query.filter, ...filter },
      meta
    },
    {
      enabled: !isPendingAuthenticated && !isPendingCanAccess || disableAuthentication,
      placeholderData: (previousData) => previousData,
      retry: false,
      onError: (error2) => notify(
        (error2 == null ? void 0 : error2.message) || "ra.notification.http_error",
        {
          type: "error",
          messageArgs: {
            _: error2 == null ? void 0 : error2.message
          }
        }
      ),
      ...otherQueryOptions
    }
  );
  const onSelectAll = useSelectAll({
    resource,
    sort: { field: query.sort, order: query.order },
    filter: { ...query.filter, ...filter }
  });
  (0, import_react70.useEffect)(() => {
    if (query.page <= 0 || !isFetching && query.page > 1 && (data == null || (data == null ? void 0 : data.pages.length) === 0)) {
      queryModifiers.setPage(1);
      return;
    }
    if (total == null) {
      return;
    }
    const totalPages = Math.ceil(total / query.perPage) || 1;
    if (!isFetching && query.page > totalPages) {
      queryModifiers.setPage(totalPages);
    }
  }, [isFetching, query.page, query.perPage, data, queryModifiers, total]);
  const currentSort = (0, import_react70.useMemo)(
    () => ({
      field: query.sort,
      order: query.order
    }),
    [query.sort, query.order]
  );
  const getResourceLabel = useGetResourceLabel();
  const defaultTitle = translate("ra.page.list", {
    name: getResourceLabel(resource, 2)
  });
  const unwrappedData = (0, import_react70.useMemo)(
    () => {
      var _a;
      return (_a = data == null ? void 0 : data.pages) == null ? void 0 : _a.reduce((acc, page) => [...acc, ...page.data], []);
    },
    [data]
  );
  return {
    sort: currentSort,
    data: unwrappedData,
    defaultTitle,
    displayedFilters: query.displayedFilters,
    error,
    exporter,
    filter,
    filterValues: query.filterValues,
    hideFilter: queryModifiers.hideFilter,
    isFetching,
    isLoading,
    isPending,
    onSelect: selectionModifiers.select,
    onSelectAll,
    onToggleItem: selectionModifiers.toggle,
    onUnselectItems: selectionModifiers.clearSelection,
    page: query.page,
    perPage: query.perPage,
    refetch: refetch2,
    resource,
    selectedIds,
    setFilters: queryModifiers.setFilters,
    setPage: queryModifiers.setPage,
    setPerPage: queryModifiers.setPerPage,
    setSort: queryModifiers.setSort,
    showFilter: queryModifiers.showFilter,
    total,
    hasNextPage,
    hasPreviousPage,
    fetchNextPage,
    isFetchingNextPage,
    fetchPreviousPage,
    isFetchingPreviousPage
  };
};

// node_modules/ra-core/src/controller/list/ListContext.tsx
var import_react71 = __toESM(require_react());
var ListContext = (0, import_react71.createContext)(null);
ListContext.displayName = "ListContext";

// node_modules/ra-core/src/controller/list/ListFilterContext.tsx
var import_react72 = __toESM(require_react());
var import_pick2 = __toESM(require_pick());
var ListFilterContext = (0, import_react72.createContext)(void 0);
var usePickFilterContext = (context) => (0, import_react72.useMemo)(
  () => (0, import_pick2.default)(context, [
    "displayedFilters",
    "filterValues",
    "hideFilter",
    "setFilters",
    "showFilter",
    "resource"
  ]),
  // eslint-disable-next-line react-hooks/exhaustive-deps
  [
    context.displayedFilters,
    context.filterValues,
    context.hideFilter,
    context.setFilters,
    context.showFilter
  ]
);
ListFilterContext.displayName = "ListFilterContext";

// node_modules/ra-core/src/controller/list/ListSortContext.tsx
var import_react73 = __toESM(require_react());
var import_pick3 = __toESM(require_pick());
var ListSortContext = (0, import_react73.createContext)(
  void 0
);
var usePickSortContext = (context) => (0, import_react73.useMemo)(
  () => (0, import_pick3.default)(context, ["sort", "setSort", "resource"]),
  // eslint-disable-next-line react-hooks/exhaustive-deps
  [context.sort, context.setSort]
);
ListSortContext.displayName = "ListSortContext";

// node_modules/ra-core/src/controller/list/ListPaginationContext.tsx
var import_react74 = __toESM(require_react());
var import_pick4 = __toESM(require_pick());
var ListPaginationContext = (0, import_react74.createContext)(void 0);
ListPaginationContext.displayName = "ListPaginationContext";
var usePickPaginationContext = (context) => (0, import_react74.useMemo)(
  () => (0, import_pick4.default)(context, [
    "isLoading",
    "isPending",
    "hasPreviousPage",
    "hasNextPage",
    "page",
    "perPage",
    "setPage",
    "setPerPage",
    "total",
    "resource"
  ]),
  // eslint-disable-next-line react-hooks/exhaustive-deps
  [
    context.isLoading,
    context.isPending,
    context.hasPreviousPage,
    context.hasNextPage,
    context.page,
    context.perPage,
    context.setPage,
    context.setPerPage,
    context.total
  ]
);

// node_modules/ra-core/src/controller/list/ListContextProvider.tsx
var import_jsx_runtime22 = __toESM(require_jsx_runtime());
var ListContextProvider = ({
  value,
  children
}) => (0, import_jsx_runtime22.jsx)(ListContext.Provider, { value, children: (0, import_jsx_runtime22.jsx)(ListFilterContext.Provider, { value: usePickFilterContext(value), children: (0, import_jsx_runtime22.jsx)(ListSortContext.Provider, { value: usePickSortContext(value), children: (0, import_jsx_runtime22.jsx)(
  ListPaginationContext.Provider,
  {
    value: usePickPaginationContext(value),
    children
  }
) }) }) });

// node_modules/ra-core/src/controller/list/InfinitePaginationContext.ts
var import_react75 = __toESM(require_react());
var InfinitePaginationContext = (0, import_react75.createContext)({
  hasNextPage: false,
  fetchNextPage: () => Promise.reject("not implemented"),
  isFetchingNextPage: false,
  hasPreviousPage: false,
  fetchPreviousPage: () => Promise.reject("not implemented"),
  isFetchingPreviousPage: false
});
InfinitePaginationContext.displayName = "InfinitePaginationContext";

// node_modules/ra-core/src/controller/list/InfiniteListBase.tsx
var import_jsx_runtime23 = __toESM(require_jsx_runtime());
var InfiniteListBase = ({
  children,
  loading = null,
  ...props
}) => {
  const controllerProps = useInfiniteListController(props);
  const isAuthPending = useIsAuthPending({
    resource: controllerProps.resource,
    action: "list"
  });
  if (isAuthPending && !props.disableAuthentication) {
    return loading;
  }
  return (
    // We pass props.resource here as we don't need to create a new ResourceContext if the props is not provided
    (0, import_jsx_runtime23.jsx)(OptionalResourceContextProvider, { value: props.resource, children: (0, import_jsx_runtime23.jsx)(ListContextProvider, { value: controllerProps, children: (0, import_jsx_runtime23.jsx)(
      InfinitePaginationContext.Provider,
      {
        value: {
          hasNextPage: controllerProps.hasNextPage,
          fetchNextPage: controllerProps.fetchNextPage,
          isFetchingNextPage: controllerProps.isFetchingNextPage,
          hasPreviousPage: controllerProps.hasPreviousPage,
          fetchPreviousPage: controllerProps.fetchPreviousPage,
          isFetchingPreviousPage: controllerProps.isFetchingPreviousPage
        },
        children
      }
    ) }) })
  );
};

// node_modules/ra-core/src/controller/list/useListController.ts
var import_react76 = __toESM(require_react());
var useListController = (props = {}) => {
  const {
    debounce: debounce5 = 500,
    disableAuthentication = false,
    disableSyncWithLocation = false,
    exporter = defaultExporter,
    filter,
    filterDefaultValues,
    perPage = 10,
    queryOptions = {},
    sort = defaultSort3,
    storeKey
  } = props;
  const resource = useResourceContext(props);
  const { meta, ...otherQueryOptions } = queryOptions;
  if (!resource) {
    throw new Error(
      `useListController requires a non-empty resource prop or context`
    );
  }
  if (filter && ((0, import_react76.isValidElement)(filter) || Array.isArray(filter) && filter.some(import_react76.isValidElement))) {
    throw new Error(
      "useListController received a React element as `filter` props. If you intended to set the list filter elements, use the `filters` (with an s) prop instead. The `filter` prop is internal and should not be set by the developer."
    );
  }
  const { isPending: isPendingAuthenticated } = useAuthenticated({
    enabled: !disableAuthentication
  });
  const { isPending: isPendingCanAccess } = useRequireAccess({
    action: "list",
    resource,
    // If disableAuthentication is true then isPendingAuthenticated will always be true so this hook is disabled
    enabled: !isPendingAuthenticated
  });
  const translate = useTranslate();
  const notify = useNotify();
  const [query, queryModifiers] = useListParams({
    debounce: debounce5,
    disableSyncWithLocation,
    filterDefaultValues,
    perPage,
    resource,
    sort,
    storeKey
  });
  const [selectedIds, selectionModifiers] = useRecordSelection({
    resource,
    disableSyncWithStore: storeKey === false
  });
  const {
    data,
    pageInfo,
    total,
    meta: responseMeta,
    error,
    isLoading,
    isFetching,
    isPending,
    refetch: refetch2,
    isPaused,
    isPlaceholderData
  } = useGetList(
    resource,
    {
      pagination: {
        page: query.page,
        perPage: query.perPage
      },
      sort: { field: query.sort, order: query.order },
      filter: { ...query.filter, ...filter },
      meta
    },
    {
      enabled: !isPendingAuthenticated && !isPendingCanAccess || disableAuthentication,
      placeholderData: (previousData) => previousData,
      retry: false,
      onError: (error2) => notify(
        (error2 == null ? void 0 : error2.message) || "ra.notification.http_error",
        {
          type: "error",
          messageArgs: {
            _: error2 == null ? void 0 : error2.message
          }
        }
      ),
      ...otherQueryOptions
    }
  );
  (0, import_react76.useEffect)(() => {
    if (isPaused && isPlaceholderData) {
      notify("ra.message.placeholder_data_warning", {
        type: "warning",
        messageArgs: {
          _: "Network issue: Data refresh failed."
        }
      });
    }
  }, [isPaused, isPlaceholderData, notify]);
  (0, import_react76.useEffect)(() => {
    if (query.page <= 0 || !isFetching && query.page > 1 && (data == null || (data == null ? void 0 : data.length) === 0)) {
      queryModifiers.setPage(1);
      return;
    }
    if (total == null) {
      return;
    }
    const totalPages = Math.ceil(total / query.perPage) || 1;
    if (!isFetching && query.page > totalPages) {
      queryModifiers.setPage(totalPages);
    }
  }, [isFetching, query.page, query.perPage, data, queryModifiers, total]);
  const currentSort = (0, import_react76.useMemo)(
    () => ({
      field: query.sort,
      order: query.order
    }),
    [query.sort, query.order]
  );
  const getResourceLabel = useGetResourceLabel();
  const defaultTitle = translate("ra.page.list", {
    name: getResourceLabel(resource, 2)
  });
  const onSelectAll = useSelectAll({
    resource,
    sort: { field: query.sort, order: query.order },
    filter: { ...query.filter, ...filter }
  });
  return {
    sort: currentSort,
    data,
    meta: responseMeta,
    defaultTitle,
    displayedFilters: query.displayedFilters,
    error,
    exporter,
    filter,
    filterValues: query.filterValues,
    hideFilter: queryModifiers.hideFilter,
    isFetching,
    isLoading,
    isPending,
    onSelect: selectionModifiers.select,
    onSelectAll,
    onToggleItem: selectionModifiers.toggle,
    onUnselectItems: selectionModifiers.clearSelection,
    page: query.page,
    perPage: query.perPage,
    refetch: refetch2,
    resource,
    selectedIds,
    setFilters: queryModifiers.setFilters,
    setPage: queryModifiers.setPage,
    setPerPage: queryModifiers.setPerPage,
    setSort: queryModifiers.setSort,
    showFilter: queryModifiers.showFilter,
    total,
    hasNextPage: pageInfo ? pageInfo.hasNextPage : total != null ? query.page * query.perPage < total : void 0,
    hasPreviousPage: pageInfo ? pageInfo.hasPreviousPage : query.page > 1
  };
};
var defaultSort3 = {
  field: "id",
  order: SORT_ASC
};
var injectedProps = [
  "sort",
  "data",
  "defaultTitle",
  "displayedFilters",
  "error",
  "exporter",
  "filterValues",
  "hasNextPage",
  "hasPreviousPage",
  "hideFilter",
  "isFetching",
  "isLoading",
  "isPending",
  "onSelect",
  "onSelectAll",
  "onToggleItem",
  "onUnselectItems",
  "page",
  "perPage",
  "refetch",
  "refresh",
  "resource",
  "selectedIds",
  "setFilters",
  "setPage",
  "setPerPage",
  "setSort",
  "showFilter",
  "total",
  "totalPages"
];
var getListControllerProps = (props) => injectedProps.reduce((acc, key) => ({ ...acc, [key]: props[key] }), {});
var sanitizeListRestProps = (props) => Object.keys(props).filter((propName) => !injectedProps.includes(propName)).reduce((acc, key) => ({ ...acc, [key]: props[key] }), {});

// node_modules/ra-core/src/controller/list/ListBase.tsx
var import_jsx_runtime24 = __toESM(require_jsx_runtime());
var ListBase = ({
  children,
  loading = null,
  ...props
}) => {
  const controllerProps = useListController(props);
  const isAuthPending = useIsAuthPending({
    resource: controllerProps.resource,
    action: "list"
  });
  if (isAuthPending && !props.disableAuthentication) {
    return loading;
  }
  return (
    // We pass props.resource here as we don't need to create a new ResourceContext if the props is not provided
    (0, import_jsx_runtime24.jsx)(OptionalResourceContextProvider, { value: props.resource, children: (0, import_jsx_runtime24.jsx)(ListContextProvider, { value: controllerProps, children }) })
  );
};

// node_modules/ra-core/src/controller/list/ListController.tsx
var ListController = ({
  children,
  ...props
}) => {
  const controllerProps = useListController(props);
  return children(controllerProps);
};

// node_modules/ra-core/src/controller/list/useExpanded.tsx
var import_react77 = __toESM(require_react());
var useExpanded = (resource, id, single = false) => {
  const [expandedIds, setExpandedIds] = useStore(
    `${resource}.datagrid.expanded`,
    []
  );
  const expanded = Array.isArray(expandedIds) ? (
    // eslint-disable-next-line eqeqeq
    expandedIds.map((el) => el == id).indexOf(true) !== -1
  ) : false;
  const toggleExpanded = (0, import_react77.useCallback)(() => {
    setExpandedIds((ids) => {
      if (!Array.isArray(ids)) {
        return [id];
      }
      const index = ids.findIndex((el) => el == id);
      return index > -1 ? single ? [] : [...ids.slice(0, index), ...ids.slice(index + 1)] : single ? [id] : [...ids, id];
    });
  }, [setExpandedIds, id, single]);
  return [expanded, toggleExpanded];
};
var useExpandAll = (resource, ids) => {
  const [expandedIds, setExpandedIds] = useStore(
    `${resource}.datagrid.expanded`,
    []
  );
  const isExpanded = Array.isArray(expandedIds) ? (
    // eslint-disable-next-line eqeqeq
    expandedIds.some((id) => ids.some((id2) => id2 == id))
  ) : false;
  const toggleExpandedAll = (0, import_react77.useCallback)(() => {
    const unaffectedExpandedIds = expandedIds.filter(
      // eslint-disable-next-line eqeqeq
      (expanded_id) => !ids.some((id) => id == expanded_id)
    );
    setExpandedIds(
      isExpanded ? unaffectedExpandedIds : unaffectedExpandedIds.concat(ids)
    );
  }, [expandedIds, setExpandedIds, isExpanded, ids]);
  return [isExpanded, toggleExpandedAll];
};

// node_modules/ra-core/src/controller/list/useInfinitePaginationContext.ts
var import_react78 = __toESM(require_react());
var useInfinitePaginationContext = () => (0, import_react78.useContext)(InfinitePaginationContext);

// node_modules/ra-core/src/controller/list/useList.ts
var import_react79 = __toESM(require_react());
var import_get4 = __toESM(require_get());
var import_isEqual4 = __toESM(require_isEqual());
var refetch = () => {
  throw new Error(
    "refetch is not available for a ListContext built from useList based on local data"
  );
};
var useList = (props) => {
  const {
    data,
    error,
    filter = defaultFilter2,
    isFetching = false,
    isLoading = false,
    isPending = false,
    page: initialPage = 1,
    perPage: initialPerPage = 1e3,
    sort: initialSort,
    filterCallback = (record) => Boolean(record)
  } = props;
  const resource = useResourceContext(props);
  const [fetchingState, setFetchingState] = (0, import_react79.useState)(isFetching);
  const [loadingState, setLoadingState] = (0, import_react79.useState)(isLoading);
  const [pendingState, setPendingState] = (0, import_react79.useState)(isPending);
  const [finalItems, setFinalItems] = (0, import_react79.useState)(() => ({
    data,
    total: data ? data.length : void 0
  }));
  const { page, setPage, perPage, setPerPage } = usePaginationState_default({
    page: initialPage,
    perPage: initialPerPage
  });
  const { sort, setSort: setSortState } = useSortState_default(initialSort);
  const setSort = (0, import_react79.useCallback)(
    (sort2) => {
      setSortState(sort2);
      setPage(1);
    },
    [setPage, setSortState]
  );
  const [selectedIds, selectionModifiers] = useRecordSelection(
    resource ? {
      resource
    } : { disableSyncWithStore: true }
  );
  const filterRef = (0, import_react79.useRef)(filter);
  const [displayedFilters, setDisplayedFilters] = (0, import_react79.useState)({});
  const [filterValues, setFilterValues] = (0, import_react79.useState)(filter);
  const hideFilter = (0, import_react79.useCallback)(
    (filterName) => {
      setDisplayedFilters((previousState) => {
        const { [filterName]: _, ...newState } = previousState;
        return newState;
      });
      setFilterValues((previousState) => {
        const { [filterName]: _, ...newState } = previousState;
        return newState;
      });
    },
    [setDisplayedFilters, setFilterValues]
  );
  const showFilter = (0, import_react79.useCallback)(
    (filterName, defaultValue) => {
      setDisplayedFilters((previousState) => ({
        ...previousState,
        [filterName]: true
      }));
      setFilterValues(
        (previousState) => removeEmpty_default({
          ...previousState,
          [filterName]: defaultValue
        })
      );
    },
    [setDisplayedFilters, setFilterValues]
  );
  const setFilters = (0, import_react79.useCallback)(
    (filters, displayedFilters2 = void 0) => {
      setFilterValues(removeEmpty_default(filters));
      if (displayedFilters2) {
        setDisplayedFilters(displayedFilters2);
      }
      setPage(1);
    },
    [setDisplayedFilters, setFilterValues, setPage]
  );
  (0, import_react79.useEffect)(() => {
    if (!(0, import_isEqual4.default)(filter, filterRef.current)) {
      filterRef.current = filter;
      setFilterValues(filter);
    }
  }, [filter]);
  (0, import_react79.useEffect)(
    () => {
      if (isPending || !data) return;
      let tempData = data;
      if (filterValues) {
        const flattenFilterValues = flattenObject(filterValues);
        tempData = data.filter(
          (record) => Object.entries(flattenFilterValues).every(
            ([filterName, filterValue]) => {
              const recordValue = (0, import_get4.default)(record, filterName);
              const result = Array.isArray(recordValue) ? Array.isArray(filterValue) ? recordValue.some(
                (item) => filterValue.includes(item)
              ) : recordValue.includes(filterValue) : Array.isArray(filterValue) ? filterValue.includes(recordValue) : filterName === "q" ? Object.keys(record).some(
                (key) => typeof record[key] === "string" && record[key].toLowerCase().includes(
                  filterValue.toLowerCase()
                )
              ) : filterValue == recordValue;
              return result;
            }
          )
        ).filter(filterCallback);
      }
      const filteredLength = tempData.length;
      if (sort.field) {
        tempData = tempData.sort((a, b) => {
          if ((0, import_get4.default)(a, sort.field) > (0, import_get4.default)(b, sort.field)) {
            return sort.order === "ASC" ? 1 : -1;
          }
          if ((0, import_get4.default)(a, sort.field) < (0, import_get4.default)(b, sort.field)) {
            return sort.order === "ASC" ? -1 : 1;
          }
          return 0;
        });
      }
      tempData = tempData.slice((page - 1) * perPage, page * perPage);
      setFinalItems({
        data: tempData,
        total: filteredLength
      });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      // eslint-disable-next-line react-hooks/exhaustive-deps
      JSON.stringify(data),
      filterValues,
      isPending,
      page,
      perPage,
      setFinalItems,
      sort.field,
      sort.order
    ]
  );
  (0, import_react79.useEffect)(() => {
    if (isFetching !== fetchingState) {
      setFetchingState(isFetching);
    }
  }, [isFetching, fetchingState, setFetchingState]);
  (0, import_react79.useEffect)(() => {
    if (isLoading !== loadingState) {
      setLoadingState(isLoading);
    }
  }, [isLoading, loadingState, setLoadingState]);
  (0, import_react79.useEffect)(() => {
    if (isPending !== pendingState) {
      setPendingState(isPending);
    }
  }, [isPending, pendingState, setPendingState]);
  const onSelectAll = (0, import_react79.useCallback)(() => {
    const allIds = (data == null ? void 0 : data.map(({ id }) => id)) || [];
    selectionModifiers.select(allIds);
  }, [data, selectionModifiers]);
  return {
    sort,
    data: pendingState ? void 0 : (finalItems == null ? void 0 : finalItems.data) ?? [],
    defaultTitle: "",
    error: error ?? null,
    displayedFilters,
    filterValues,
    hasNextPage: (finalItems == null ? void 0 : finalItems.total) == null ? false : page * perPage < finalItems.total,
    hasPreviousPage: page > 1,
    hideFilter,
    isFetching: fetchingState,
    isLoading: loadingState,
    isPending: pendingState,
    onSelect: selectionModifiers.select,
    onSelectAll,
    onToggleItem: selectionModifiers.toggle,
    onUnselectItems: selectionModifiers.clearSelection,
    page,
    perPage,
    resource: "",
    refetch,
    selectedIds,
    setFilters,
    setPage,
    setPerPage,
    setSort,
    showFilter,
    total: finalItems == null ? void 0 : finalItems.total
  };
};
var defaultFilter2 = {};

// node_modules/ra-core/src/controller/list/useListContext.ts
var import_react80 = __toESM(require_react());
var useListContext = () => {
  const context = (0, import_react80.useContext)(ListContext);
  if (!context) {
    throw new Error(
      "useListContext must be used inside a ListContextProvider"
    );
  }
  return context;
};

// node_modules/ra-core/src/controller/list/useListContextWithProps.ts
var import_react81 = __toESM(require_react());
var import_defaults = __toESM(require_defaults());
var useListContextWithProps = (props) => {
  const context = (0, import_react81.useContext)(ListContext);
  return (0, import_react81.useMemo)(
    () => (0, import_defaults.default)(
      {},
      props != null ? extractListContextProps(props) : {},
      context
    ),
    [context, props]
  );
};
var extractListContextProps = ({
  sort,
  data,
  defaultTitle,
  displayedFilters,
  exporter,
  filterValues,
  hasCreate,
  hideFilter,
  isFetching,
  isLoading,
  isPending,
  onSelect,
  onSelectAll,
  onToggleItem,
  onUnselectItems,
  page,
  perPage,
  refetch: refetch2,
  resource,
  selectedIds,
  setFilters,
  setPage,
  setPerPage,
  setSort,
  showFilter,
  total
}) => ({
  sort,
  data,
  defaultTitle,
  displayedFilters,
  exporter,
  filterValues,
  hasCreate,
  hideFilter,
  isFetching,
  isLoading,
  isPending,
  onSelect,
  onSelectAll,
  onToggleItem,
  onUnselectItems,
  page,
  perPage,
  refetch: refetch2,
  resource,
  selectedIds,
  setFilters,
  setPage,
  setPerPage,
  setSort,
  showFilter,
  total
});

// node_modules/ra-core/src/controller/list/useListFilterContext.ts
var import_react82 = __toESM(require_react());
var useListFilterContext = () => {
  const context = (0, import_react82.useContext)(ListFilterContext);
  if (!context) {
    throw new Error(
      "useListFilterContext must be used inside a ListFilterContextProvider"
    );
  }
  return context;
};

// node_modules/ra-core/src/controller/list/useListPaginationContext.ts
var import_react83 = __toESM(require_react());
var useListPaginationContext = () => {
  const context = (0, import_react83.useContext)(ListPaginationContext);
  if (!context) {
    throw new Error(
      "useListPaginationContext must be used inside a ListPaginationContextProvider"
    );
  }
  return context;
};

// node_modules/ra-core/src/controller/list/useListSortContext.ts
var import_react84 = __toESM(require_react());
var useListSortContext = () => {
  const context = (0, import_react84.useContext)(ListSortContext);
  if (!context) {
    throw new Error(
      "useListSortContext must be used inside a ListSortContextProvider"
    );
  }
  return context;
};

// node_modules/ra-core/src/controller/list/useUnselect.ts
var import_react85 = __toESM(require_react());
var useUnselect = (resource) => {
  const [, { unselect }] = useRecordSelection(
    resource ? { resource } : { disableSyncWithStore: true }
  );
  return (0, import_react85.useCallback)(
    (ids) => {
      unselect(ids);
    },
    [unselect]
  );
};

// node_modules/ra-core/src/controller/list/useUnselectAll.ts
var import_react86 = __toESM(require_react());
var useUnselectAll = (resource) => {
  const [, { clearSelection }] = useRecordSelection(
    resource ? { resource } : { disableSyncWithStore: true }
  );
  return (0, import_react86.useCallback)(() => {
    clearSelection();
  }, [clearSelection]);
};

// node_modules/ra-core/src/controller/list/WithListContext.tsx
var WithListContext = ({
  render
}) => render(useListContext()) || null;

// node_modules/ra-core/src/controller/field/useReferenceArrayFieldController.ts
var emptyArray = [];
var defaultFilter3 = {};
var useReferenceArrayFieldController = (props) => {
  const {
    filter = defaultFilter3,
    page = 1,
    perPage = 1e3,
    record,
    reference,
    sort,
    source,
    queryOptions = {}
  } = props;
  const notify = useNotify();
  const value = (0, import_get5.default)(record, source);
  const { meta, ...otherQueryOptions } = queryOptions;
  const ids = Array.isArray(value) ? value : emptyArray;
  const { data, error, isLoading, isFetching, isPending, refetch: refetch2 } = useGetManyAggregate(
    reference,
    { ids, meta },
    {
      onError: (error2) => notify(
        typeof error2 === "string" ? error2 : (error2 == null ? void 0 : error2.message) || "ra.notification.http_error",
        {
          type: "error",
          messageArgs: {
            _: typeof error2 === "string" ? error2 : (error2 == null ? void 0 : error2.message) ? error2.message : void 0
          }
        }
      ),
      ...otherQueryOptions
    }
  );
  const listProps = useList({
    data,
    error,
    filter,
    isFetching,
    isLoading,
    isPending,
    page,
    perPage,
    sort
  });
  return {
    ...listProps,
    defaultTitle: void 0,
    refetch: refetch2,
    resource: reference
  };
};

// node_modules/ra-core/src/controller/field/useReferenceManyFieldController.ts
var import_react87 = __toESM(require_react());
var import_get6 = __toESM(require_get());
var import_isEqual5 = __toESM(require_isEqual());
var import_debounce4 = __toESM(require_debounce());
var useReferenceManyFieldController = (props) => {
  const {
    debounce: debounce5 = 500,
    reference,
    record,
    target,
    filter = defaultFilter4,
    source = "id",
    page: initialPage,
    perPage: initialPerPage,
    sort: initialSort = { field: "id", order: "DESC" },
    queryOptions = {}
  } = props;
  const notify = useNotify();
  const resource = useResourceContext(props);
  const dataProvider = useDataProvider();
  const queryClient = useQueryClient();
  const storeKey = props.storeKey ?? `${resource}.${record == null ? void 0 : record.id}.${reference}`;
  const { meta, ...otherQueryOptions } = queryOptions;
  const { page, setPage, perPage, setPerPage } = usePaginationState_default({
    page: initialPage,
    perPage: initialPerPage
  });
  const { sort, setSort: setSortState } = useSortState_default(initialSort);
  const setSort = (0, import_react87.useCallback)(
    (sort2) => {
      setSortState(sort2);
      setPage(1);
    },
    [setPage, setSortState]
  );
  const [selectedIds, selectionModifiers] = useRecordSelection({
    resource: storeKey
  });
  const filterRef = (0, import_react87.useRef)(filter);
  const [displayedFilters, setDisplayedFilters] = (0, import_react87.useState)({});
  const [filterValues, setFilterValues] = (0, import_react87.useState)(filter);
  const hideFilter = (0, import_react87.useCallback)(
    (filterName) => {
      setDisplayedFilters((previousState) => {
        const { [filterName]: _, ...newState } = previousState;
        return newState;
      });
      setFilterValues((previousState) => {
        const { [filterName]: _, ...newState } = previousState;
        return newState;
      });
    },
    [setDisplayedFilters, setFilterValues]
  );
  const showFilter = (0, import_react87.useCallback)(
    (filterName, defaultValue) => {
      setDisplayedFilters((previousState) => ({
        ...previousState,
        [filterName]: true
      }));
      setFilterValues((previousState) => ({
        ...previousState,
        [filterName]: defaultValue
      }));
    },
    [setDisplayedFilters, setFilterValues]
  );
  const debouncedSetFilters = (0, import_react87.useCallback)(
    (0, import_debounce4.default)((filters, displayedFilters2) => {
      setFilterValues(removeEmpty_default(filters));
      setDisplayedFilters(displayedFilters2);
      setPage(1);
    }, debounce5),
    [setDisplayedFilters, setFilterValues, setPage]
  );
  const setFilters = (0, import_react87.useCallback)(
    (filters, displayedFilters2, debounce6 = false) => {
      if (debounce6) {
        debouncedSetFilters(filters, displayedFilters2);
      } else {
        setFilterValues(removeEmpty_default(filters));
        setDisplayedFilters(displayedFilters2);
        setPage(1);
      }
    },
    [setDisplayedFilters, setFilterValues, setPage, debouncedSetFilters]
  );
  (0, import_react87.useEffect)(() => {
    if (!(0, import_isEqual5.default)(filter, filterRef.current)) {
      filterRef.current = filter;
      setFilterValues(filter);
    }
  }, [filter]);
  const {
    data,
    total,
    meta: responseMeta,
    pageInfo,
    error,
    isFetching,
    isLoading,
    isPending,
    refetch: refetch2
  } = useGetManyReference(
    reference,
    {
      target,
      id: (0, import_get6.default)(record, source),
      pagination: { page, perPage },
      sort,
      filter: filterValues,
      meta
    },
    {
      enabled: (0, import_get6.default)(record, source) != null,
      placeholderData: (previousData) => previousData,
      onError: (error2) => notify(
        typeof error2 === "string" ? error2 : (error2 == null ? void 0 : error2.message) || "ra.notification.http_error",
        {
          type: "error",
          messageArgs: {
            _: typeof error2 === "string" ? error2 : (error2 == null ? void 0 : error2.message) ? error2.message : void 0
          }
        }
      ),
      ...otherQueryOptions
    }
  );
  const onSelectAll = useEvent(
    async ({
      limit = 250,
      queryOptions: queryOptions2 = {}
    } = {}) => {
      var _a;
      const { meta: meta2, onSuccess, onError } = queryOptions2;
      try {
        const results = await queryClient.fetchQuery({
          queryKey: [
            resource,
            "getManyReference",
            {
              target,
              id: (0, import_get6.default)(record, source),
              pagination: { page: 1, perPage: limit },
              sort,
              filter,
              meta: meta2
            }
          ],
          queryFn: () => dataProvider.getManyReference(reference, {
            target,
            id: (0, import_get6.default)(record, source),
            pagination: { page: 1, perPage: limit },
            sort,
            filter,
            meta: meta2
          })
        });
        const allIds = ((_a = results.data) == null ? void 0 : _a.map(({ id }) => id)) || [];
        selectionModifiers.select(allIds);
        if (allIds.length === limit) {
          notify("ra.message.select_all_limit_reached", {
            messageArgs: { max: limit },
            type: "warning"
          });
        }
        if (onSuccess) {
          onSuccess(results);
        }
        return results.data;
      } catch (error2) {
        if (onError) {
          onError(error2);
        }
        notify("ra.notification.http_error", { type: "warning" });
      }
    }
  );
  return {
    sort,
    data,
    meta: responseMeta,
    defaultTitle: void 0,
    displayedFilters,
    error,
    filterValues,
    hideFilter,
    isFetching,
    isLoading,
    isPending,
    onSelect: selectionModifiers.select,
    onSelectAll,
    onToggleItem: selectionModifiers.toggle,
    onUnselectItems: selectionModifiers.clearSelection,
    page,
    perPage,
    refetch: refetch2,
    resource: reference,
    selectedIds,
    setFilters,
    setPage,
    setPerPage,
    hasNextPage: pageInfo ? pageInfo.hasNextPage : total != null ? page * perPage < total : void 0,
    hasPreviousPage: pageInfo ? pageInfo.hasPreviousPage : page > 1,
    setSort,
    showFilter,
    total
  };
};
var defaultFilter4 = {};

// node_modules/ra-core/src/controller/field/useReferenceOneFieldController.tsx
var import_get7 = __toESM(require_get());
var useReferenceOneFieldController = (props) => {
  const {
    reference,
    record,
    target,
    source = "id",
    sort = { field: "id", order: "ASC" },
    filter = {},
    queryOptions = {}
  } = props;
  const notify = useNotify();
  const { meta, ...otherQueryOptions } = queryOptions;
  const { data, error, isFetching, isLoading, isPending, refetch: refetch2 } = useGetManyReference(
    reference,
    {
      target,
      id: (0, import_get7.default)(record, source),
      pagination: { page: 1, perPage: 1 },
      sort,
      filter,
      meta
    },
    {
      enabled: !!record,
      onError: (error2) => notify(
        typeof error2 === "string" ? error2 : error2.message || "ra.notification.http_error",
        {
          type: "error",
          messageArgs: {
            _: typeof error2 === "string" ? error2 : (error2 == null ? void 0 : error2.message) ? error2.message : void 0
          }
        }
      ),
      ...otherQueryOptions
    }
  );
  return {
    referenceRecord: data ? data[0] : void 0,
    error,
    isFetching,
    isLoading,
    isPending,
    refetch: refetch2
  };
};

// node_modules/ra-core/src/controller/input/referenceDataStatus.ts
var isMatchingReferencesError = (matchingReferences) => matchingReferences && matchingReferences.error !== void 0;
var getStatusForInput = ({
  field,
  matchingReferences,
  referenceRecord,
  translate = (x) => x
}) => {
  const matchingReferencesError = isMatchingReferencesError(
    matchingReferences
  ) ? translate(matchingReferences.error, {
    _: matchingReferences.error
  }) : null;
  const selectedReferenceError = field.value && !referenceRecord ? translate("ra.input.references.single_missing", {
    _: "ra.input.references.single_missing"
  }) : null;
  return {
    waiting: field.value && selectedReferenceError && !matchingReferences || !field.value && !matchingReferences,
    error: field.value && selectedReferenceError && matchingReferencesError || !field.value && matchingReferencesError ? field.value ? selectedReferenceError : matchingReferencesError : null,
    warning: selectedReferenceError || matchingReferencesError,
    choices: Array.isArray(matchingReferences) ? matchingReferences : [referenceRecord].filter((choice) => choice)
  };
};
var REFERENCES_STATUS_READY = "REFERENCES_STATUS_READY";
var REFERENCES_STATUS_INCOMPLETE = "REFERENCES_STATUS_INCOMPLETE";
var REFERENCES_STATUS_EMPTY = "REFERENCES_STATUS_EMPTY";
var getSelectedReferencesStatus = (field, referenceRecords) => !field.value || field.value.length === referenceRecords.length ? REFERENCES_STATUS_READY : referenceRecords.length > 0 ? REFERENCES_STATUS_INCOMPLETE : REFERENCES_STATUS_EMPTY;
var getStatusForArrayInput = ({
  field,
  matchingReferences,
  referenceRecords,
  translate = (x) => x
}) => {
  const selectedReferencesDataStatus = getSelectedReferencesStatus(
    field,
    referenceRecords
  );
  const matchingReferencesError = isMatchingReferencesError(
    matchingReferences
  ) ? translate(matchingReferences.error, {
    _: matchingReferences.error
  }) : null;
  const choices2 = Array.isArray(matchingReferences) ? referenceRecords.concat(
    matchingReferences.filter(
      (choice) => referenceRecords.findIndex((c) => c.id === choice.id) === -1
    )
  ) : referenceRecords;
  return {
    waiting: !matchingReferences && field.value && selectedReferencesDataStatus === REFERENCES_STATUS_EMPTY || !matchingReferences && !field.value,
    error: matchingReferencesError && (!field.value || field.value && selectedReferencesDataStatus === REFERENCES_STATUS_EMPTY) ? translate("ra.input.references.all_missing", {
      _: "ra.input.references.all_missing"
    }) : null,
    warning: matchingReferencesError || field.value && selectedReferencesDataStatus !== REFERENCES_STATUS_READY ? matchingReferencesError || translate("ra.input.references.many_missing", {
      _: "ra.input.references.many_missing"
    }) : null,
    choices: choices2
  };
};

// node_modules/ra-core/src/controller/input/useReferenceArrayInputController.ts
var import_react89 = __toESM(require_react());

// node_modules/ra-core/src/controller/input/useReferenceParams.ts
var import_react88 = __toESM(require_react());
var import_debounce5 = __toESM(require_debounce());
var useReferenceParams = ({
  resource,
  filter,
  sort = defaultSort4,
  page = 1,
  perPage = 10,
  debounce: debounce5 = 500
}) => {
  const [params, setParams] = (0, import_react88.useState)(defaultParams2);
  const tempParams = (0, import_react88.useRef)();
  const requestSignature = [
    resource,
    JSON.stringify(params),
    JSON.stringify(filter),
    JSON.stringify(sort),
    page,
    perPage
  ];
  const query = (0, import_react88.useMemo)(
    () => getQuery2({
      params,
      filterDefaultValues: filter,
      sort,
      page,
      perPage
    }),
    requestSignature
    // eslint-disable-line react-hooks/exhaustive-deps
  );
  const changeParams = (0, import_react88.useCallback)((action) => {
    if (!tempParams.current) {
      const newTempParams = queryReducer(query, action);
      tempParams.current = newTempParams;
      setTimeout(() => {
        setParams(newTempParams);
        tempParams.current = void 0;
      }, 0);
    } else {
      tempParams.current = queryReducer(tempParams.current, action);
    }
  }, requestSignature);
  const setSort = (0, import_react88.useCallback)(
    (sort2) => changeParams({
      type: SET_SORT,
      payload: sort2
    }),
    requestSignature
    // eslint-disable-line react-hooks/exhaustive-deps
  );
  const setPage = (0, import_react88.useCallback)(
    (newPage) => changeParams({ type: SET_PAGE, payload: newPage }),
    requestSignature
    // eslint-disable-line react-hooks/exhaustive-deps
  );
  const setPerPage = (0, import_react88.useCallback)(
    (newPerPage) => changeParams({ type: SET_PER_PAGE, payload: newPerPage }),
    requestSignature
    // eslint-disable-line react-hooks/exhaustive-deps
  );
  const filterValues = query.filter || emptyObject2;
  const displayedFilterValues = query.displayedFilters || emptyObject2;
  const debouncedSetFilters = (0, import_react88.useRef)(
    (0, import_debounce5.default)((filter2, displayedFilters = void 0) => {
      changeParams({
        type: SET_FILTER,
        payload: {
          filter: removeEmpty_default(filter2),
          displayedFilters
        }
      });
    }, debounce5)
  );
  (0, import_react88.useEffect)(() => {
    return () => {
      debouncedSetFilters.current.cancel();
    };
  }, []);
  const setFilters = (0, import_react88.useCallback)(
    (filter2, displayedFilters = void 0, debounce6 = false) => {
      debounce6 ? debouncedSetFilters.current(filter2, displayedFilters) : changeParams({
        type: SET_FILTER,
        payload: {
          filter: removeEmpty_default(filter2),
          displayedFilters
        }
      });
    },
    requestSignature
    // eslint-disable-line react-hooks/exhaustive-deps
  );
  const hideFilter = (0, import_react88.useCallback)((filterName) => {
    changeParams({
      type: HIDE_FILTER,
      payload: filterName
    });
  }, requestSignature);
  const showFilter = (0, import_react88.useCallback)((filterName, defaultValue) => {
    changeParams({
      type: SHOW_FILTER,
      payload: {
        filterName,
        defaultValue
      }
    });
  }, requestSignature);
  return [
    {
      filterValues,
      requestSignature,
      ...query,
      displayedFilters: displayedFilterValues
    },
    {
      changeParams,
      setPage,
      setPerPage,
      setSort,
      setFilters,
      hideFilter,
      showFilter
    }
  ];
};
var getQuery2 = ({
  params,
  filterDefaultValues,
  sort,
  page,
  perPage
}) => {
  const query = hasCustomParams2(params) ? { ...params } : { filter: filterDefaultValues || {} };
  if (!query.sort) {
    query.sort = sort.field;
    query.order = sort.order;
  }
  if (query.page == null) {
    query.page = page;
  }
  if (query.perPage == null) {
    query.perPage = perPage;
  }
  if (query.page == null) {
    query.page = 1;
  }
  return {
    ...query,
    page: getNumberOrDefault2(query.page, 1),
    perPage: getNumberOrDefault2(query.perPage, 10)
  };
};
var hasCustomParams2 = (params) => {
  return params && params.filter && (Object.keys(params.filter).length > 0 || params.order != null || params.page !== 1 || params.perPage != null || params.sort != null);
};
var getNumberOrDefault2 = (possibleNumber, defaultValue) => {
  if (typeof possibleNumber === "undefined") {
    return defaultValue;
  }
  const parsedNumber = typeof possibleNumber === "string" ? parseInt(possibleNumber, 10) : possibleNumber;
  return isNaN(parsedNumber) ? defaultValue : parsedNumber;
};
var emptyObject2 = {};
var defaultSort4 = {
  field: "id",
  order: SORT_ASC
};
var defaultParams2 = {};

// node_modules/ra-core/src/controller/input/useReferenceArrayInputController.ts
var useReferenceArrayInputController = (props) => {
  const {
    debounce: debounce5,
    enableGetChoices,
    filter,
    page: initialPage = 1,
    perPage: initialPerPage = 25,
    sort: initialSort = { field: "id", order: "DESC" },
    queryOptions = {},
    reference,
    source
  } = props;
  const { getValues: getValues2 } = useFormContext();
  const finalSource = useWrappedSource(source);
  const value = useWatch({ name: finalSource }) ?? getValues2(finalSource);
  const { meta, ...otherQueryOptions } = queryOptions;
  const {
    data: referenceRecords,
    error: errorGetMany,
    isLoading: isLoadingGetMany,
    isFetching: isFetchingGetMany,
    isPending: isPendingGetMany,
    refetch: refetchGetMany
  } = useGetManyAggregate(
    reference,
    {
      ids: value || EmptyArray,
      meta
    },
    {
      enabled: value != null && value.length > 0
    }
  );
  const [params, paramsModifiers] = useReferenceParams({
    resource: reference,
    page: initialPage,
    perPage: initialPerPage,
    sort: initialSort,
    debounce: debounce5,
    filter
  });
  const finalReferenceRecords = referenceRecords ? referenceRecords.filter(Boolean) : [];
  const isGetMatchingEnabled = enableGetChoices ? enableGetChoices(params.filterValues) : true;
  const {
    data: matchingReferences,
    total,
    pageInfo,
    error: errorGetList,
    isLoading: isLoadingGetList,
    isFetching: isFetchingGetList,
    isPending: isPendingGetList,
    refetch: refetchGetMatching
  } = useGetList(
    reference,
    {
      pagination: {
        page: params.page,
        perPage: params.perPage
      },
      sort: { field: params.sort, order: params.order },
      filter: { ...params.filter, ...filter },
      meta
    },
    {
      retry: false,
      enabled: isGetMatchingEnabled,
      placeholderData: (previousData) => previousData,
      ...otherQueryOptions
    }
  );
  const finalMatchingReferences = matchingReferences && matchingReferences.length > 0 ? mergeReferences(matchingReferences, finalReferenceRecords) : finalReferenceRecords.length > 0 ? finalReferenceRecords : matchingReferences;
  const refetch2 = (0, import_react89.useCallback)(() => {
    refetchGetMany();
    refetchGetMatching();
  }, [refetchGetMany, refetchGetMatching]);
  const currentSort = (0, import_react89.useMemo)(
    () => ({
      field: params.sort,
      order: params.order
    }),
    [params.sort, params.order]
  );
  return {
    sort: currentSort,
    allChoices: finalMatchingReferences,
    availableChoices: matchingReferences,
    selectedChoices: finalReferenceRecords,
    displayedFilters: params.displayedFilters,
    error: errorGetMany || errorGetList,
    filter,
    filterValues: params.filterValues,
    hideFilter: paramsModifiers.hideFilter,
    isFetching: isFetchingGetMany || isFetchingGetList,
    isLoading: isLoadingGetMany || isLoadingGetList,
    isPending: isPendingGetMany || isPendingGetList,
    page: params.page,
    perPage: params.perPage,
    refetch: refetch2,
    resource: reference,
    setFilters: paramsModifiers.setFilters,
    setPage: paramsModifiers.setPage,
    setPerPage: paramsModifiers.setPerPage,
    setSort: paramsModifiers.setSort,
    showFilter: paramsModifiers.showFilter,
    // we return source and not finalSource because child inputs (e.g. AutocompleteArrayInput) already call useInput and compute the final source
    source,
    total,
    hasNextPage: pageInfo ? pageInfo.hasNextPage : total != null ? params.page * params.perPage < total : void 0,
    hasPreviousPage: pageInfo ? pageInfo.hasPreviousPage : params.page > 1,
    isFromReference: true
  };
};
var EmptyArray = [];
var mergeReferences = (ref1, ref2) => {
  const res = [...ref1];
  const ids = ref1.map((ref) => ref.id);
  ref2.forEach((ref) => {
    if (!ids.includes(ref.id)) {
      ids.push(ref.id);
      res.push(ref);
    }
  });
  return res;
};

// node_modules/ra-core/src/controller/input/useReferenceInputController.ts
var import_react90 = __toESM(require_react());
var useReferenceInputController = (props) => {
  const {
    debounce: debounce5,
    enableGetChoices,
    filter,
    page: initialPage = 1,
    perPage: initialPerPage = 25,
    sort: initialSort,
    queryOptions = {},
    reference,
    source
  } = props;
  const { meta, ...otherQueryOptions } = queryOptions;
  const [params, paramsModifiers] = useReferenceParams({
    resource: reference,
    page: initialPage,
    perPage: initialPerPage,
    sort: initialSort,
    debounce: debounce5,
    filter
  });
  const finalSource = useWrappedSource(source);
  const currentValue = useWatch({ name: finalSource });
  const isGetMatchingEnabled = enableGetChoices ? enableGetChoices(params.filterValues) : true;
  const {
    data: possibleValuesData = [],
    total,
    pageInfo,
    isFetching: isFetchingPossibleValues,
    isLoading: isLoadingPossibleValues,
    isPending: isPendingPossibleValues,
    error: errorPossibleValues,
    refetch: refetchGetList
  } = useGetList(
    reference,
    {
      pagination: {
        page: params.page,
        perPage: params.perPage
      },
      sort: { field: params.sort, order: params.order },
      filter: { ...params.filter, ...filter },
      meta
    },
    {
      enabled: isGetMatchingEnabled,
      placeholderData: (previousData) => previousData,
      ...otherQueryOptions
    }
  );
  const {
    referenceRecord: currentReferenceRecord,
    refetch: refetchReference,
    error: errorReference,
    isLoading: isLoadingReference,
    isFetching: isFetchingReference,
    isPending: isPendingReference
  } = useReference({
    id: currentValue,
    reference,
    // @ts-ignore the types of the queryOptions for the getMAny and getList are not compatible
    options: {
      enabled: currentValue != null && currentValue !== "",
      meta,
      ...otherQueryOptions
    }
  });
  const isPending = (
    // The reference query isn't enabled when there is no value yet but as it has no data, react-query will flag it as pending
    currentValue != null && currentValue !== "" && isPendingReference || isPendingPossibleValues
  );
  const [referenceRecord, setReferenceRecord] = (0, import_react90.useState)(void 0);
  (0, import_react90.useEffect)(() => {
    setReferenceRecord(currentReferenceRecord);
  }, [currentReferenceRecord]);
  let finalData, finalTotal;
  if (!referenceRecord || possibleValuesData.find((record) => record.id === referenceRecord.id)) {
    finalData = possibleValuesData;
    finalTotal = total;
  } else {
    finalData = [referenceRecord, ...possibleValuesData];
    finalTotal = total == null ? void 0 : total + 1;
  }
  const refetch2 = (0, import_react90.useCallback)(() => {
    refetchGetList();
    refetchReference();
  }, [refetchGetList, refetchReference]);
  const currentSort = (0, import_react90.useMemo)(
    () => ({
      field: params.sort,
      order: params.order
    }),
    [params.sort, params.order]
  );
  return {
    sort: currentSort,
    allChoices: finalData,
    availableChoices: possibleValuesData,
    selectedChoices: referenceRecord ? [referenceRecord] : [],
    displayedFilters: params.displayedFilters,
    error: errorReference || errorPossibleValues,
    filter: params.filter,
    filterValues: params.filterValues,
    hideFilter: paramsModifiers.hideFilter,
    isFetching: isFetchingReference || isFetchingPossibleValues,
    isLoading: isLoadingReference || isLoadingPossibleValues,
    isPending,
    page: params.page,
    perPage: params.perPage,
    refetch: refetch2,
    resource: reference,
    setFilters: paramsModifiers.setFilters,
    setPage: paramsModifiers.setPage,
    setPerPage: paramsModifiers.setPerPage,
    setSort: paramsModifiers.setSort,
    showFilter: paramsModifiers.showFilter,
    // we return source and not finalSource because child inputs (e.g. AutocompleteInput) already call useInput and compute the final source
    source,
    total: finalTotal,
    hasNextPage: pageInfo ? pageInfo.hasNextPage : total != null ? params.page * params.perPage < total : void 0,
    hasPreviousPage: pageInfo ? pageInfo.hasPreviousPage : params.page > 1,
    isFromReference: true
  };
};

// node_modules/ra-core/src/form/choices/ChoicesContext.ts
var import_react91 = __toESM(require_react());
var ChoicesContext = (0, import_react91.createContext)(
  void 0
);

// node_modules/ra-core/src/form/choices/ChoicesContextProvider.tsx
var import_jsx_runtime25 = __toESM(require_jsx_runtime());
var ChoicesContextProvider = ({
  children,
  value
}) => (0, import_jsx_runtime25.jsx)(ChoicesContext.Provider, { value, children });

// node_modules/ra-core/src/form/choices/useChoicesContext.ts
var import_react92 = __toESM(require_react());
var useChoicesContext = (options = {}) => {
  const context = (0, import_react92.useContext)(
    ChoicesContext
  );
  const choices2 = options.choices && isArrayOfStrings(options.choices) ? convertOptionsToChoices(options.choices) : options.choices;
  const { data, ...list } = useList({
    data: choices2,
    isLoading: options.isLoading ?? false,
    isPending: options.isPending ?? false,
    isFetching: options.isFetching ?? false,
    error: options.error,
    // When not in a ChoicesContext, paginating does not make sense (e.g. AutocompleteInput).
    perPage: Infinity
  });
  const result = (0, import_react92.useMemo)(() => {
    if (options.choices || !context) {
      return {
        allChoices: data,
        availableChoices: options.availableChoices ?? data,
        selectedChoices: options.selectedChoices ?? data,
        displayedFilters: options.selectedChoices ?? list.displayedFilters,
        error: options.error,
        filter: options.filter ?? list.filter,
        filterValues: options.filterValues ?? list.filterValues,
        hasNextPage: options.hasNextPage ?? list.hasNextPage,
        hasPreviousPage: options.hasPreviousPage ?? list.hasPreviousPage,
        hideFilter: options.hideFilter ?? list.hideFilter,
        isLoading: list.isLoading ?? false,
        // we must take the one for useList, otherwise the loading state isn't synchronized with the data
        isPending: list.isPending ?? false,
        // same
        isFetching: list.isFetching ?? false,
        // same
        page: options.page ?? list.page,
        perPage: options.perPage ?? list.perPage,
        refetch: options.refetch ?? list.refetch,
        resource: options.resource ?? list.resource,
        setFilters: options.setFilters ?? list.setFilters,
        setPage: options.setPage ?? list.setPage,
        setPerPage: options.setPerPage ?? list.setPerPage,
        setSort: options.setSort ?? list.setSort,
        showFilter: options.showFilter ?? list.showFilter,
        sort: options.sort ?? list.sort,
        source: options.source,
        total: options.total ?? list.total,
        isFromReference: false
      };
    }
    return context;
  }, [context, data, list, options]);
  return result;
};
var isArrayOfStrings = (choices2) => Array.isArray(choices2) && choices2.every((choice) => typeof choice === "string");
var convertOptionsToChoices = (options) => options.map((choice) => ({
  id: choice,
  name: choice
}));

// node_modules/ra-core/src/form/choices/useChoices.tsx
var import_react93 = __toESM(require_react());
var import_get8 = __toESM(require_get());
var import_jsx_runtime26 = __toESM(require_jsx_runtime());
var useChoices = ({
  optionText = "name",
  optionValue = "id",
  disableValue = "disabled",
  translateChoice = true
}) => {
  const translate = useTranslate();
  const getChoiceText = (0, import_react93.useCallback)(
    (choice) => {
      if ((0, import_react93.isValidElement)(optionText)) {
        return (0, import_jsx_runtime26.jsx)(RecordContextProvider, { value: choice, children: optionText });
      }
      const choiceName = typeof optionText === "function" ? optionText(choice) : (0, import_get8.default)(choice, optionText);
      return (0, import_react93.isValidElement)(choiceName) ? choiceName : translateChoice ? translate(String(choiceName), { _: choiceName }) : String(choiceName);
    },
    [optionText, translate, translateChoice]
  );
  const getChoiceValue = (0, import_react93.useCallback)(
    (choice) => (0, import_get8.default)(choice, optionValue),
    [optionValue]
  );
  const getDisableValue = (0, import_react93.useCallback)(
    (choice) => (0, import_get8.default)(choice, disableValue),
    [disableValue]
  );
  return {
    getChoiceText,
    getChoiceValue,
    getDisableValue
  };
};

// node_modules/ra-core/src/form/Form.tsx
var React2 = __toESM(require_react());
var import_react100 = __toESM(require_react());

// node_modules/ra-core/src/form/groups/FormGroupsProvider.tsx
var import_react95 = __toESM(require_react());

// node_modules/ra-core/src/form/groups/FormGroupsContext.tsx
var import_react94 = __toESM(require_react());
var FormGroupsContext = (0, import_react94.createContext)(void 0);

// node_modules/ra-core/src/form/groups/FormGroupsProvider.tsx
var import_jsx_runtime27 = __toESM(require_jsx_runtime());
var FormGroupsProvider = ({ children }) => {
  const formGroups = (0, import_react95.useRef)({});
  const subscribers = (0, import_react95.useRef)({});
  const formContextValue = (0, import_react95.useMemo)(
    () => ({
      /**
       * Register a subscriber function for the specified group. The subscriber
       * will be called whenever the group content changes (fields added or removed).
       */
      subscribe: (group, subscriber) => {
        if (!subscribers.current[group]) {
          subscribers.current[group] = [];
        }
        subscribers.current[group].push(subscriber);
        return () => {
          subscribers.current[group] = subscribers.current[group].filter((s) => s !== subscriber);
        };
      },
      getGroupFields: (name) => formGroups.current[name] || [],
      registerGroup: (name) => {
        formGroups.current[name] = formGroups.current[name] || [];
      },
      unregisterGroup: (name) => {
        delete formGroups[name];
      },
      registerField: (source, group) => {
        if (group != null) {
          if (!(formGroups.current[group] || []).includes(source)) {
            formGroups.current[group] = [
              ...formGroups.current[group] || [],
              source
            ];
            if (subscribers.current[group]) {
              subscribers.current[group].forEach(
                (subscriber) => subscriber()
              );
            }
          }
        }
      },
      unregisterField: (source, group) => {
        if (group != null) {
          if (!formGroups.current[group]) {
            console.warn(`Invalid form group ${group}`);
          } else {
            const fields = new Set(formGroups.current[group]);
            fields.delete(source);
            formGroups.current[group] = Array.from(fields);
            if (subscribers.current[group]) {
              subscribers.current[group].forEach(
                (subscriber) => subscriber()
              );
            }
          }
        }
      }
    }),
    []
  );
  return (0, import_jsx_runtime27.jsx)(FormGroupsContext.Provider, { value: formContextValue, children });
};

// node_modules/ra-core/src/form/useWarnWhenUnsavedChanges.tsx
var import_react96 = __toESM(require_react());
var useWarnWhenUnsavedChanges = (enable, formRootPathname, control) => {
  const translate = useTranslate();
  const { isSubmitSuccessful, dirtyFields } = useFormState(
    control ? { control } : void 0
  );
  const isDirty = Object.keys(dirtyFields).length > 0;
  const [shouldNotify, setShouldNotify] = (0, import_react96.useState)(false);
  const shouldNotBlock = !enable || !isDirty || isSubmitSuccessful;
  const blocker = useBlocker(({ currentLocation, nextLocation }) => {
    if (shouldNotBlock) return false;
    const initialLocation = formRootPathname || currentLocation.pathname;
    const newLocationIsInsideCurrentLocation = nextLocation.pathname.startsWith(initialLocation);
    const newLocationIsShowView = nextLocation.pathname.startsWith(
      `${initialLocation}/show`
    );
    const newLocationIsInsideForm = newLocationIsInsideCurrentLocation && !newLocationIsShowView;
    if (newLocationIsInsideForm) return false;
    return true;
  });
  (0, import_react96.useEffect)(() => {
    if (blocker.state === "blocked") {
      if (shouldNotBlock) {
        blocker.proceed();
        return;
      }
      setShouldNotify(true);
    }
  }, [blocker.state]);
  (0, import_react96.useEffect)(() => {
    if (shouldNotify) {
      const shouldProceed = window.confirm(
        translate("ra.message.unsaved_changes")
      );
      if (shouldProceed) {
        blocker.proceed && blocker.proceed();
      } else {
        blocker.reset && blocker.reset();
      }
    }
    setShouldNotify(false);
  }, [shouldNotify, translate]);
  (0, import_react96.useEffect)(() => {
    const beforeunload = (e) => {
      e.preventDefault();
      e.returnValue = true;
    };
    if (shouldNotBlock) {
      return;
    }
    window.addEventListener("beforeunload", beforeunload);
    return () => {
      window.removeEventListener("beforeunload", beforeunload);
    };
  }, [shouldNotBlock]);
};

// node_modules/ra-core/src/form/WarnWhenUnsavedChanges.ts
var WarnWhenUnsavedChanges = ({
  enable = true,
  formRootPathName,
  formControl
}) => {
  useWarnWhenUnsavedChanges(enable, formRootPathName, formControl);
  return null;
};

// node_modules/ra-core/src/form/useAugmentedForm.ts
var import_react99 = __toESM(require_react());
var import_merge3 = __toESM(require_merge());

// node_modules/ra-core/src/form/getFormInitialValues.ts
var import_merge2 = __toESM(require_merge());
function getFormInitialValues(defaultValues, record) {
  const finalInitialValues = (0, import_merge2.default)(
    {},
    getValues(defaultValues, record),
    record
  );
  return finalInitialValues;
}
function getValues(values, record) {
  if (typeof values === "object") {
    return values;
  }
  if (typeof values === "function") {
    return values(record);
  }
  return {};
}

// node_modules/ra-core/src/form/validation/getSimpleValidationResolver.ts
var getSimpleValidationResolver = (validate) => async (data) => {
  const errors = await validate(data);
  if (!errors || isEmptyObject(errors)) {
    return { values: data, errors: {} };
  }
  const transformedErrors = transformErrorFields(errors);
  if (!transformedErrors || isEmptyObject(transformedErrors)) {
    return { values: data, errors: {} };
  }
  return {
    values: {},
    errors: transformedErrors
  };
};
var transformErrorFields = (error) => {
  return Object.keys(error).reduce((acc, field) => {
    if (Array.isArray(error[field])) {
      let arrayHasErrors = false;
      const transformedArrayErrors = error[field].map((item) => {
        if (!isEmptyObject(item)) {
          arrayHasErrors = true;
        }
        return transformErrorFields(item);
      });
      if (!arrayHasErrors) {
        return acc;
      }
      return {
        ...acc,
        [field]: transformedArrayErrors
      };
    }
    if (isEmptyObject(error[field])) {
      return acc;
    }
    if (typeof error[field] === "object" && !isRaTranslationObj(error[field])) {
      return {
        ...acc,
        [field]: transformErrorFields(error[field])
      };
    }
    return {
      ...acc,
      [field]: addTypeAndMessage(error[field])
    };
  }, {});
};
var addTypeAndMessage = (error) => ({
  type: "manual",
  message: isRaTranslationObj(error) ? error : { message: error }
});
var isRaTranslationObj = (obj) => Object.keys(obj).includes("message") && Object.keys(obj).includes("args");
var isEmptyObject = (obj) => obj == null || Object.getOwnPropertyNames(obj).length === 0;

// node_modules/ra-core/src/form/validation/setSubmissionErrors.ts
var setSubmissionErrors = (errors, setError) => {
  const setErrorFromObject = (errors2, rootPath) => {
    Object.entries(errors2).forEach(([name, error]) => {
      if (typeof error === "object") {
        setErrorFromObject(error, `${rootPath}${name}.`);
        return;
      }
      setError(`${rootPath}${name}`, {
        type: "server",
        message: error.toString()
      });
    });
  };
  setErrorFromObject(errors, "");
};

// node_modules/ra-core/src/form/validation/useNotifyIsFormInvalid.ts
var import_react97 = __toESM(require_react());
var useNotifyIsFormInvalid = (control, enabled = true) => {
  const { submitCount, errors } = useFormState(
    control ? { control } : void 0
  );
  const submitCountRef = (0, import_react97.useRef)(submitCount);
  const notify = useNotify();
  (0, import_react97.useEffect)(() => {
    var _a, _b;
    if (submitCount > submitCountRef.current && enabled) {
      submitCountRef.current = submitCount;
      if (Object.keys(errors).length > 0) {
        const serverError = typeof ((_b = (_a = errors.root) == null ? void 0 : _a.serverError) == null ? void 0 : _b.message) === "string" ? errors.root.serverError.message : void 0;
        notify(serverError || "ra.message.invalid_form", {
          type: "error"
        });
      }
    }
  }, [errors, submitCount, notify, enabled]);
};

// node_modules/ra-core/src/form/sanitizeEmptyValues.ts
var sanitizeEmptyValues = (values, record = {}) => {
  const sanitizedValues = {};
  Object.keys(values).forEach((key) => {
    if (values[key] == null || values[key] === "") {
      if (record.hasOwnProperty(key)) {
        sanitizedValues[key] = null;
      } else {
      }
    } else {
      sanitizedValues[key] = values[key];
    }
  });
  return sanitizedValues;
};

// node_modules/ra-core/src/form/useRecordFromLocation.ts
var import_react98 = __toESM(require_react());
var import_query_string3 = __toESM(require_query_string());
var import_isEqual6 = __toESM(require_isEqual());
var useRecordFromLocation = (props = {}) => {
  const { searchSource, stateSource } = props;
  const location = useLocation();
  const [recordFromLocation, setRecordFromLocation] = (0, import_react98.useState)(
    () => getRecordFromLocation(location, {
      stateSource,
      searchSource
    })
  );
  const previousRecordRef = (0, import_react98.useRef)(recordFromLocation);
  (0, import_react98.useEffect)(() => {
    const newRecordFromLocation = getRecordFromLocation(location, {
      stateSource,
      searchSource
    });
    if (!(0, import_isEqual6.default)(newRecordFromLocation, previousRecordRef.current)) {
      previousRecordRef.current = newRecordFromLocation;
      setRecordFromLocation(newRecordFromLocation);
    }
  }, [location, stateSource, searchSource]);
  return recordFromLocation;
};
var getRecordFromLocation = ({ state, search }, {
  searchSource = "source",
  stateSource = "record"
} = {}) => {
  if (state && state[stateSource]) {
    return state[stateSource];
  }
  if (search) {
    try {
      const searchParams = (0, import_query_string3.parse)(search);
      const source = searchParams[searchSource];
      if (source) {
        if (Array.isArray(source)) {
          console.error(
            `Failed to parse location ${searchSource} parameter '${search}'. To pre-fill some fields in the Create form, pass a stringified ${searchSource} parameter (e.g. '?${searchSource}={"title":"foo"}')`
          );
          return null;
        }
        return JSON.parse(source);
      }
    } catch (e) {
      console.error(
        `Failed to parse location ${searchSource} parameter '${search}'. To pre-fill some fields in the Create form, pass a stringified ${searchSource} parameter (e.g. '?${searchSource}={"title":"foo"}')`
      );
    }
  }
  return null;
};

// node_modules/ra-core/src/form/useAugmentedForm.ts
var useAugmentedForm = (props) => {
  const {
    criteriaMode = "firstError",
    defaultValues,
    formRootPathname,
    resolver,
    reValidateMode = "onChange",
    onSubmit,
    sanitizeEmptyValues: sanitizeEmptyValues2,
    validate,
    disableInvalidFormNotification,
    ...rest
  } = props;
  const saveContext = useSaveContext();
  const record = useRecordContext(props);
  const defaultValuesIncludingRecord = (0, import_react99.useMemo)(
    () => getFormInitialValues(defaultValues, record),
    // eslint-disable-next-line
    [
      // eslint-disable-next-line
      JSON.stringify({
        defaultValues: typeof defaultValues === "function" ? "function" : defaultValues,
        record
      })
    ]
  );
  const finalResolver = resolver ? resolver : validate ? getSimpleValidationResolver(validate) : void 0;
  const form = useForm({
    criteriaMode,
    values: defaultValuesIncludingRecord,
    reValidateMode,
    resolver: finalResolver,
    ...rest
  });
  const formRef = (0, import_react99.useRef)(form);
  useNotifyIsFormInvalid(form.control, !disableInvalidFormNotification);
  const recordFromLocation = useRecordFromLocation();
  const recordFromLocationApplied = (0, import_react99.useRef)(false);
  const { reset } = form;
  (0, import_react99.useEffect)(() => {
    if (recordFromLocation && !recordFromLocationApplied.current) {
      reset((0, import_merge3.default)({}, defaultValuesIncludingRecord, recordFromLocation), {
        keepDefaultValues: true
      });
      recordFromLocationApplied.current = true;
    }
  }, [defaultValuesIncludingRecord, recordFromLocation, reset]);
  const handleSubmit = (0, import_react99.useCallback)(
    async (values, event) => {
      let errors;
      const finalValues = sanitizeEmptyValues2 ? sanitizeEmptyValues(values, record) : values;
      if (onSubmit) {
        errors = await onSubmit(finalValues, event);
      }
      if (onSubmit == null && (saveContext == null ? void 0 : saveContext.save)) {
        errors = await saveContext.save(finalValues, event);
      }
      if (errors != null) {
        setSubmissionErrors(errors, formRef.current.setError);
      }
    },
    [onSubmit, saveContext, sanitizeEmptyValues2, record]
  );
  const formHandleSubmit = (0, import_react99.useCallback)(
    (event) => {
      if (!event.defaultPrevented) {
        event.stopPropagation();
        form.handleSubmit(handleSubmit)(event);
      }
      return;
    },
    [form, handleSubmit]
  );
  return {
    form,
    handleSubmit,
    formHandleSubmit
  };
};

// node_modules/ra-core/src/form/Form.tsx
var import_jsx_runtime28 = __toESM(require_jsx_runtime());
function Form(props) {
  const {
    children,
    id,
    className,
    noValidate = false,
    formRootPathname,
    warnWhenUnsavedChanges,
    WarnWhenUnsavedChangesComponent = WarnWhenUnsavedChanges
  } = props;
  const record = useRecordContext(props);
  const resource = useResourceContext(props);
  const { form, formHandleSubmit } = useAugmentedForm(props);
  const sourceContext = React2.useMemo(
    () => ({
      getSource: (source) => source,
      getLabel: (source) => `resources.${resource}.fields.${source}`
    }),
    [resource]
  );
  const dataRouterContext = (0, import_react100.useContext)(DataRouterContext);
  const dataRouterStateContext = (0, import_react100.useContext)(DataRouterStateContext);
  if (warnWhenUnsavedChanges && (!dataRouterContext || !dataRouterStateContext) && true) {
    console.error(
      "Cannot use the warnWhenUnsavedChanges feature outside of a DataRouter. The warnWhenUnsavedChanges feature is disabled. Remove the warnWhenUnsavedChanges prop or convert your custom router to a Data Router."
    );
  }
  return (0, import_jsx_runtime28.jsx)(OptionalRecordContextProvider, { value: record, children: (0, import_jsx_runtime28.jsx)(SourceContextProvider, { value: sourceContext, children: (0, import_jsx_runtime28.jsx)(FormProvider, { ...form, children: (0, import_jsx_runtime28.jsxs)(FormGroupsProvider, { children: [
    (0, import_jsx_runtime28.jsx)(
      "form",
      {
        onSubmit: formHandleSubmit,
        noValidate,
        id,
        className,
        children
      }
    ),
    warnWhenUnsavedChanges && dataRouterContext && dataRouterStateContext && (0, import_jsx_runtime28.jsx)(
      WarnWhenUnsavedChangesComponent,
      {
        enable: true,
        formRootPathName: formRootPathname,
        formControl: form.control
      }
    )
  ] }) }) }) });
}

// node_modules/ra-core/src/form/FormDataConsumer.tsx
var import_get9 = __toESM(require_get());

// node_modules/ra-core/src/form/useFormValues.ts
var useFormValues = () => {
  const { getValues: getValues2 } = useFormContext();
  return {
    ...useWatch(),
    // subscribe to form value updates
    ...getValues2()
    // always merge with latest form values
  };
};

// node_modules/ra-core/src/form/FormDataConsumer.tsx
var import_jsx_runtime29 = __toESM(require_jsx_runtime());
var FormDataConsumer = (props) => {
  const form = useFormContext();
  const {
    formState: {
      // Don't know exactly why, but this is needed for the form values to be updated
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      isDirty
    }
  } = form;
  const formData = useFormValues();
  return (0, import_jsx_runtime29.jsx)(FormDataConsumerView, { formData, ...props });
};
var FormDataConsumerView = (props) => {
  const { children, formData, source } = props;
  let result;
  const finalSource = useWrappedSource(source || "");
  const matches = ArraySourceRegex.exec(finalSource);
  if (matches) {
    const scopedFormData = (0, import_get9.default)(formData, matches[0]);
    result = children({ formData, scopedFormData });
  } else {
    result = children({ formData });
  }
  return result === void 0 ? null : result;
};
var ArraySourceRegex = new RegExp(/.+\.\d+$/);

// node_modules/ra-core/src/form/groups/FormGroupContext.ts
var import_react101 = __toESM(require_react());
var FormGroupContext = (0, import_react101.createContext)(
  null
);

// node_modules/ra-core/src/form/groups/FormGroupContextProvider.tsx
var import_react103 = __toESM(require_react());

// node_modules/ra-core/src/form/groups/useFormGroups.ts
var import_react102 = __toESM(require_react());
var useFormGroups = () => {
  const context = (0, import_react102.useContext)(FormGroupsContext);
  return context;
};

// node_modules/ra-core/src/form/groups/FormGroupContextProvider.tsx
var import_jsx_runtime30 = __toESM(require_jsx_runtime());
var FormGroupContextProvider = ({
  children,
  name
}) => {
  const formGroups = useFormGroups();
  (0, import_react103.useEffect)(() => {
    if (!formGroups || !formGroups.registerGroup || !formGroups.unregisterGroup) {
      console.warn(
        `The FormGroupContextProvider can only be used inside a FormContext such as provided by the SimpleForm and TabbedForm components`
      );
      return;
    }
    formGroups.registerGroup(name);
    return () => {
      formGroups.unregisterGroup(name);
    };
  }, [formGroups, name]);
  return (0, import_jsx_runtime30.jsx)(FormGroupContext.Provider, { value: name, children });
};

// node_modules/ra-core/src/form/groups/useFormGroup.ts
var import_react104 = __toESM(require_react());
var import_get10 = __toESM(require_get());
var import_isEqual7 = __toESM(require_isEqual());
var useFormGroup = (name) => {
  const { dirtyFields, touchedFields, validatingFields, errors } = useFormState();
  const dirtyFieldsNames = Object.keys(dirtyFields);
  const touchedFieldsNames = Object.keys(touchedFields);
  const validatingFieldsNames = Object.keys(validatingFields);
  const errorsNames = Object.keys(errors);
  const formGroups = useFormGroups();
  const [state, setState] = (0, import_react104.useState)({
    errors: void 0,
    isDirty: false,
    isTouched: false,
    isValid: true,
    isValidating: true
  });
  const updateGroupState = useEvent(() => {
    if (!formGroups) return;
    const fields = formGroups.getGroupFields(name);
    const fieldStates = fields.map((field) => {
      return {
        name: field,
        error: (0, import_get10.default)(errors, field, void 0),
        isDirty: (0, import_get10.default)(dirtyFields, field, false) !== false,
        isValid: (0, import_get10.default)(errors, field, void 0) == null,
        isValidating: (0, import_get10.default)(validatingFields, field, void 0) == null,
        isTouched: (0, import_get10.default)(touchedFields, field, false) !== false
      };
    }).filter((fieldState) => fieldState != void 0);
    const newState = getFormGroupState(fieldStates);
    setState((oldState) => {
      if (!(0, import_isEqual7.default)(oldState, newState)) {
        return newState;
      }
      return oldState;
    });
  });
  (0, import_react104.useEffect)(
    () => {
      updateGroupState();
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      // eslint-disable-next-line react-hooks/exhaustive-deps
      JSON.stringify(dirtyFieldsNames),
      // eslint-disable-next-line react-hooks/exhaustive-deps
      JSON.stringify(errorsNames),
      // eslint-disable-next-line react-hooks/exhaustive-deps
      JSON.stringify(touchedFieldsNames),
      // eslint-disable-next-line react-hooks/exhaustive-deps
      JSON.stringify(validatingFieldsNames),
      updateGroupState,
      name,
      formGroups
    ]
  );
  (0, import_react104.useEffect)(() => {
    if (!formGroups) return;
    const unsubscribe = formGroups.subscribe(name, () => {
      updateGroupState();
    });
    return unsubscribe;
  }, [formGroups, name, updateGroupState]);
  return state;
};
var getFormGroupState = (fieldStates) => {
  return fieldStates.reduce(
    (acc, fieldState) => {
      let errors = acc.errors || {};
      if (fieldState.error) {
        errors[fieldState.name] = fieldState.error;
      }
      const newState = {
        isDirty: acc.isDirty || fieldState.isDirty,
        errors,
        isTouched: acc.isTouched || fieldState.isTouched,
        isValid: acc.isValid && fieldState.isValid,
        isValidating: acc.isValidating && fieldState.isValidating
      };
      return newState;
    },
    {
      isDirty: false,
      errors: void 0,
      isValid: true,
      isTouched: false,
      isValidating: false
    }
  );
};

// node_modules/ra-core/src/form/groups/useFormGroupContext.ts
var import_react105 = __toESM(require_react());
var useFormGroupContext = () => {
  const context = (0, import_react105.useContext)(FormGroupContext);
  return context;
};

// node_modules/ra-core/src/form/useApplyInputDefaultValues.ts
var import_react106 = __toESM(require_react());
var import_get11 = __toESM(require_get());
var useApplyInputDefaultValues = ({
  inputProps,
  isArrayInput,
  fieldArrayInputControl
}) => {
  const { defaultValue, source } = inputProps;
  const finalSource = useWrappedSource(source);
  const record = useRecordContext(inputProps);
  const { getValues: getValues2, resetField, formState, reset } = useFormContext();
  const recordValue = (0, import_get11.default)(record, finalSource);
  const formValue = (0, import_get11.default)(getValues2(), finalSource);
  const { dirtyFields } = formState;
  const isDirty = Object.keys(dirtyFields).includes(finalSource);
  (0, import_react106.useEffect)(() => {
    if (defaultValue == null || formValue != null || recordValue != null || isDirty) {
      return;
    }
    const pathContainsIndex = finalSource.split(".").some((pathPart) => numericRegex.test(pathPart));
    if (pathContainsIndex) {
      const parentPath = finalSource.split(".").slice(0, -1).join(".");
      const parentValue = (0, import_get11.default)(getValues2(), parentPath);
      if (parentValue == null) {
        return;
      }
    }
    if (isArrayInput) {
      if (!fieldArrayInputControl) {
        throw new Error(
          "useApplyInputDefaultValues: No fieldArrayInputControl passed in props for array input usage"
        );
      }
      fieldArrayInputControl.replace(defaultValue);
      reset({}, { keepValues: true });
      return;
    }
    resetField(finalSource, { defaultValue });
  });
};
var numericRegex = /^\d+$/;

// node_modules/ra-core/src/form/useInput.ts
var import_react108 = __toESM(require_react());
var import_get12 = __toESM(require_get());

// node_modules/ra-core/src/form/validation/useGetValidationErrorMessage.ts
var useGetValidationErrorMessage = () => {
  const translate = useTranslate();
  return (error) => {
    if (error.message != null) {
      const { message, args } = error;
      return translate(message, { _: message, ...args });
    }
    return translate(error, { _: error });
  };
};

// node_modules/ra-core/src/form/validation/useUnique.ts
var import_react107 = __toESM(require_react());
var import_merge4 = __toESM(require_merge());
var import_set4 = __toESM(require_set());

// node_modules/ra-core/src/form/validation/validate.ts
var import_memoize = __toESM(require_memoize());
var EMAIL_REGEX = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
var isEmpty2 = (value) => typeof value === "undefined" || value === null || value === "" || Array.isArray(value) && value.length === 0;
function isValidationErrorMessageWithArgs(error) {
  return error ? error.hasOwnProperty("message") : false;
}
var getMessage = (message, messageArgs, value, values) => typeof message === "function" ? message({
  args: messageArgs,
  value,
  values
}) : messageArgs ? {
  message,
  args: messageArgs
} : message;
var memoize = (fn) => (0, import_memoize.default)(fn, (...args) => JSON.stringify(args));
var isFunction = (value) => typeof value === "function";
var combine2Validators = (validator1, validator2) => {
  return (value, values, meta) => {
    const result1 = validator1(value, values, meta);
    if (!result1) {
      return validator2(value, values, meta);
    }
    if (typeof result1 === "string" || isValidationErrorMessageWithArgs(result1)) {
      return result1;
    }
    return result1.then((resolvedResult1) => {
      if (!resolvedResult1) {
        return validator2(value, values, meta);
      }
      return resolvedResult1;
    });
  };
};
var composeValidators = (...validators) => {
  const allValidators = (Array.isArray(validators[0]) ? validators[0] : validators).filter(isFunction);
  return allValidators.reduce(combine2Validators, () => null);
};
var composeSyncValidators = (...validators) => (value, values, meta) => {
  const allValidators = (Array.isArray(validators[0]) ? validators[0] : validators).filter(isFunction);
  for (const validator of allValidators) {
    const error = validator(value, values, meta);
    if (error) {
      return error;
    }
  }
};
var required = memoize(
  (message = "ra.validation.required") => Object.assign(
    (value, values) => isEmpty2(value) ? getMessage(message, void 0, value, values) : void 0,
    { isRequired: true }
  )
);
var minLength = memoize(
  (min, message = "ra.validation.minLength") => (value, values) => !isEmpty2(value) && value.length < min ? getMessage(message, { min }, value, values) : void 0
);
var maxLength = memoize(
  (max, message = "ra.validation.maxLength") => (value, values) => !isEmpty2(value) && value.length > max ? getMessage(message, { max }, value, values) : void 0
);
var minValue = memoize(
  (min, message = "ra.validation.minValue") => (value, values) => !isEmpty2(value) && value < min ? getMessage(message, { min }, value, values) : void 0
);
var maxValue = memoize(
  (max, message = "ra.validation.maxValue") => (value, values) => !isEmpty2(value) && value > max ? getMessage(message, { max }, value, values) : void 0
);
var number = memoize(
  (message = "ra.validation.number") => (value, values) => !isEmpty2(value) && isNaN(Number(value)) ? getMessage(message, void 0, value, values) : void 0
);
var regex = (0, import_memoize.default)(
  (pattern, message = "ra.validation.regex") => (value, values) => !isEmpty2(value) && typeof value === "string" && !pattern.test(value) ? getMessage(message, { pattern }, value, values) : void 0,
  (pattern, message) => {
    return pattern.toString() + message;
  }
);
var email = memoize(
  (message = "ra.validation.email") => regex(EMAIL_REGEX, message)
);
var oneOfTypeMessage = ({ args }) => ({
  message: "ra.validation.oneOf",
  args
});
var choices = memoize(
  (list, message = oneOfTypeMessage) => (value, values) => !isEmpty2(value) && list.indexOf(value) === -1 ? getMessage(message, { list }, value, values) : void 0
);
var isRequired = (validate) => {
  if (validate && validate.isRequired) {
    return true;
  }
  if (Array.isArray(validate)) {
    return !!validate.find((it) => it.isRequired);
  }
  return false;
};

// node_modules/ra-core/src/form/validation/useUnique.ts
var useUnique = (options) => {
  const dataProvider = useDataProvider();
  const translateLabel = useTranslateLabel();
  const resource = useResourceContext(options);
  if (!resource) {
    throw new Error("useUnique: missing resource prop or context");
  }
  const translate = useTranslate();
  const record = useRecordContext();
  const debouncedGetList = (0, import_react107.useRef)(
    // The initial value is here to set the correct type on useRef
    asyncDebounce(
      dataProvider.getList,
      (options == null ? void 0 : options.debounce) ?? DEFAULT_DEBOUNCE
    )
  );
  const validateUnique = (0, import_react107.useCallback)(
    (callTimeOptions) => {
      const {
        message,
        filter,
        debounce: interval
      } = (0, import_merge4.default)(
        {
          debounce: DEFAULT_DEBOUNCE,
          filter: {},
          message: "ra.validation.unique"
        },
        options,
        callTimeOptions
      );
      debouncedGetList.current = asyncDebounce(
        dataProvider.getList,
        interval
      );
      return async (value, allValues, props) => {
        if (isEmpty2(value)) {
          return void 0;
        }
        try {
          const finalFilter = (0, import_set4.default)(
            (0, import_merge4.default)({}, filter),
            props.source,
            value
          );
          const { data, total } = await debouncedGetList.current(
            resource,
            {
              filter: finalFilter,
              pagination: { page: 1, perPage: 1 },
              sort: { field: "id", order: "ASC" }
            }
          );
          if (typeof total !== "undefined" && total > 0 && !data.some((r) => r.id === (record == null ? void 0 : record.id))) {
            return {
              message,
              args: {
                source: props.source,
                value,
                field: translateLabel({
                  label: props.label,
                  source: props.source,
                  resource
                })
              }
            };
          }
        } catch (error) {
          return translate("ra.notification.http_error");
        }
        return void 0;
      };
    },
    [dataProvider, options, record, resource, translate, translateLabel]
  );
  return validateUnique;
};
var DEFAULT_DEBOUNCE = 1e3;

// node_modules/ra-core/src/form/validation/ValidationError.tsx
var import_jsx_runtime31 = __toESM(require_jsx_runtime());
var ValidationErrorSpecialFormatPrefix = "@@react-admin@@";
var ValidationError = (props) => {
  const { error } = props;
  let errorMessage = error;
  const translate = useTranslate();
  if (typeof error === "string" && error.startsWith(ValidationErrorSpecialFormatPrefix)) {
    errorMessage = JSON.parse(
      error.substring(ValidationErrorSpecialFormatPrefix.length)
    );
  }
  if (errorMessage.message) {
    const { message, args } = errorMessage;
    return (0, import_jsx_runtime31.jsx)(import_jsx_runtime31.Fragment, { children: translate(message, { _: message, ...args }) });
  }
  return (0, import_jsx_runtime31.jsx)(import_jsx_runtime31.Fragment, { children: translate(errorMessage, { _: errorMessage }) });
};

// node_modules/ra-core/src/form/useInput.ts
var defaultFormat = (value) => value == null ? "" : value;
var defaultParse = (value) => value === "" ? null : value;
var useInput = (props) => {
  const {
    defaultValue,
    format = defaultFormat,
    id,
    isRequired: isRequiredOption,
    name,
    onBlur: initialOnBlur,
    onChange: initialOnChange,
    parse: parseProp = defaultParse,
    source,
    validate,
    ...options
  } = props;
  const finalSource = useWrappedSource(source);
  const finalName = name || finalSource;
  const formGroupName = useFormGroupContext();
  const formGroups = useFormGroups();
  const record = useRecordContext();
  const parse3 = useEvent(parseProp);
  const defaultId = (0, import_react108.useId)();
  if (!finalName && true) {
    console.warn(
      "Input components require either a source or a name prop."
    );
  }
  (0, import_react108.useEffect)(() => {
    if (!formGroups || formGroupName == null) {
      return;
    }
    formGroups.registerField(finalSource, formGroupName);
    return () => {
      formGroups.unregisterField(finalSource, formGroupName);
    };
  }, [formGroups, formGroupName, finalSource]);
  const sanitizedValidate = Array.isArray(validate) ? composeValidators(validate) : validate;
  const {
    field: controllerField,
    fieldState,
    formState
  } = useController({
    name: finalName,
    defaultValue: (0, import_get12.default)(record, finalSource, defaultValue),
    rules: {
      validate: async (value, values) => {
        if (!sanitizedValidate) return true;
        const error = await sanitizedValidate(value, values, {
          ...props,
          finalSource
        });
        if (!error) return true;
        return `@@react-admin@@${JSON.stringify(error)}`;
      }
    },
    ...options
  });
  useApplyInputDefaultValues({ inputProps: props });
  const onBlur = useEvent((...event) => {
    controllerField.onBlur();
    if (initialOnBlur) {
      initialOnBlur(...event);
    }
  });
  const onChange = useEvent((...event) => {
    var _a, _b, _c, _d;
    const eventOrValue = props.type === "checkbox" && ((_b = (_a = event[0]) == null ? void 0 : _a.target) == null ? void 0 : _b.value) === "on" ? event[0].target.checked : ((_d = (_c = event[0]) == null ? void 0 : _c.target) == null ? void 0 : _d.value) ?? event[0];
    controllerField.onChange(parse3 ? parse3(eventOrValue) : eventOrValue);
    if (initialOnChange) {
      initialOnChange(...event);
    }
  });
  const field = {
    ...controllerField,
    value: format ? format(controllerField.value) : controllerField.value,
    onBlur,
    onChange
  };
  return {
    id: id || defaultId,
    field,
    fieldState,
    formState,
    isRequired: isRequiredOption || isRequired(validate)
  };
};

// node_modules/ra-core/src/form/useSuggestions.ts
var import_react109 = __toESM(require_react());
var import_set5 = __toESM(require_set());
var useSuggestions = ({
  allowCreate,
  choices: choices2,
  createText = "ra.action.create",
  createValue = "@@create",
  limitChoicesToValue,
  matchSuggestion,
  optionText,
  optionValue,
  selectedItem,
  suggestionLimit = 0,
  translateChoice
}) => {
  const translate = useTranslate();
  const { getChoiceText, getChoiceValue } = useChoices({
    optionText,
    optionValue,
    translateChoice
  });
  const getSuggestions = (0, import_react109.useCallback)(
    getSuggestionsFactory({
      allowCreate,
      choices: choices2,
      createText,
      createValue,
      getChoiceText,
      getChoiceValue,
      limitChoicesToValue,
      matchSuggestion,
      optionText,
      optionValue,
      selectedItem,
      suggestionLimit
    }),
    [
      allowCreate,
      choices2,
      createText,
      createValue,
      getChoiceText,
      getChoiceValue,
      limitChoicesToValue,
      matchSuggestion,
      optionText,
      optionValue,
      selectedItem,
      suggestionLimit,
      translate
    ]
  );
  return {
    getChoiceText,
    getChoiceValue,
    getSuggestions
  };
};
var escapeRegExp = (value) => value ? value.replace(/[.*+?^${}()|[\]\\]/g, "\\$&") : "";
var defaultMatchSuggestion = (getChoiceText) => (filter, suggestion, exact = false) => {
  const suggestionText = getChoiceText(suggestion);
  const isReactElement = (0, import_react109.isValidElement)(suggestionText);
  const regex2 = escapeRegExp(filter);
  return isReactElement ? false : suggestionText && !!suggestionText.match(
    // We must escape any RegExp reserved characters to avoid errors
    // For example, the filter might contain * which must be escaped as \*
    new RegExp(exact ? `^${regex2}$` : regex2, "i")
  );
};
var getSuggestionsFactory = ({
  allowCreate = false,
  choices: choices2 = [],
  createText = "ra.action.create",
  createValue = "@@create",
  optionText = "name",
  optionValue = "id",
  getChoiceText,
  getChoiceValue,
  limitChoicesToValue = false,
  matchSuggestion = defaultMatchSuggestion(getChoiceText),
  selectedItem,
  suggestionLimit = 0
}) => (filter) => {
  let suggestions = [];
  if (selectedItem && !Array.isArray(selectedItem) && matchSuggestion(filter, selectedItem)) {
    if (limitChoicesToValue) {
      suggestions = choices2.filter(
        (choice) => getChoiceValue(choice) === getChoiceValue(selectedItem)
      );
    } else {
      suggestions = [...choices2];
    }
  } else {
    suggestions = choices2.filter(
      (choice) => matchSuggestion(filter, choice) || selectedItem != null && (!Array.isArray(selectedItem) ? getChoiceValue(choice) === getChoiceValue(selectedItem) : selectedItem.some(
        (selected) => getChoiceValue(choice) === getChoiceValue(selected)
      ))
    );
  }
  suggestions = limitSuggestions(suggestions, suggestionLimit);
  const hasExactMatch = suggestions.some(
    (suggestion) => matchSuggestion(filter, suggestion, true)
  );
  if (allowCreate) {
    const filterIsSelectedItem = (
      // If the selectedItem is an array (for example AutocompleteArrayInput)
      // we shouldn't try to match
      !!selectedItem && !Array.isArray(selectedItem) ? matchSuggestion(filter, selectedItem, true) : false
    );
    if (!hasExactMatch && !filterIsSelectedItem) {
      suggestions.push(
        getSuggestion({
          optionText,
          optionValue,
          text: createText,
          value: createValue
        })
      );
    }
  }
  const result = suggestions.filter(
    (suggestion, index) => suggestions.indexOf(suggestion) === index
  );
  return result;
};
var limitSuggestions = (suggestions, limit = 0) => Number.isInteger(limit) && limit > 0 ? suggestions.slice(0, limit) : suggestions;
var getSuggestion = ({
  optionText = "name",
  optionValue = "id",
  text = "",
  value = null
}) => {
  const suggestion = {};
  (0, import_set5.default)(suggestion, optionValue, value);
  if (typeof optionText === "string") {
    (0, import_set5.default)(suggestion, optionText, text);
  }
  return suggestion;
};

// node_modules/ra-core/src/form/FilterLiveForm.tsx
var React3 = __toESM(require_react());
var import_isEqual8 = __toESM(require_isEqual());
var import_cloneDeep2 = __toESM(require_cloneDeep());
var import_get13 = __toESM(require_get());
var import_set6 = __toESM(require_set());
var import_react110 = __toESM(require_react());
var import_jsx_runtime32 = __toESM(require_jsx_runtime());
var FilterLiveForm = (props) => {
  const { filterValues, setFilters } = useListContext();
  const resource = useResourceContext(props);
  const {
    debounce: debounce5 = 500,
    resolver,
    validate,
    children,
    formComponent: Component2 = HTMLForm,
    ...rest
  } = props;
  const finalResolver = resolver ? resolver : validate ? getSimpleValidationResolver(validate) : void 0;
  const formContext = useForm({
    mode: "onChange",
    defaultValues: filterValues,
    resolver: finalResolver,
    ...rest
  });
  const { handleSubmit, getValues: getValues2, reset, watch, formState } = formContext;
  const { isValid: isValid2 } = formState;
  const formChangesPending = React3.useRef(false);
  (0, import_react110.useEffect)(() => {
    const newValues = getFilterFormValues(getValues2(), filterValues);
    const previousValues = getValues2();
    if (formChangesPending.current) {
      formChangesPending.current = false;
      return;
    }
    if (!(0, import_isEqual8.default)(newValues, previousValues)) {
      reset(newValues);
    }
  }, [JSON.stringify(filterValues), getValues2, reset]);
  const onSubmit = (values) => {
    if (!isValid2) {
      return;
    }
    formChangesPending.current = true;
    setFilters({
      ...filterValues,
      ...values
    });
  };
  const debouncedOnSubmit = useDebouncedEvent(onSubmit, debounce5 || 0);
  (0, import_react110.useEffect)(() => {
    const { unsubscribe } = watch((values, { name }) => {
      if (name) {
        if ((0, import_get13.default)(values, name) === "") {
          const newValues = (0, import_cloneDeep2.default)(values);
          (0, import_set6.default)(newValues, name, "");
          debouncedOnSubmit(newValues);
        } else {
          debouncedOnSubmit(values);
        }
      }
    });
    return () => unsubscribe();
  }, [watch, debouncedOnSubmit]);
  const sourceContext = React3.useMemo(
    () => ({
      getSource: (source) => source,
      getLabel: (source) => `resources.${resource}.fields.${source}`
    }),
    [resource]
  );
  return (0, import_jsx_runtime32.jsx)(FormProvider, { ...formContext, children: (0, import_jsx_runtime32.jsx)(FormGroupsProvider, { children: (0, import_jsx_runtime32.jsx)(SourceContextProvider, { value: sourceContext, children: (0, import_jsx_runtime32.jsx)(Component2, { onSubmit: handleSubmit(onSubmit), children }) }) }) });
};
var HTMLForm = (props) => (0, import_jsx_runtime32.jsx)("form", { ...props });
var getFilterFormValues = (formValues, filterValues) => {
  return Object.keys(formValues).reduce(
    (acc, key) => {
      acc[key] = getInputValue(formValues, key, filterValues);
      return acc;
    },
    (0, import_cloneDeep2.default)(filterValues) ?? {}
  );
};
var getInputValue = (formValues, key, filterValues) => {
  if (formValues[key] === void 0 || formValues[key] === null) {
    return (0, import_get13.default)(filterValues, key, "");
  }
  if (Array.isArray(formValues[key])) {
    return (0, import_get13.default)(filterValues, key, "");
  }
  if (formValues[key] instanceof Date) {
    return (0, import_get13.default)(filterValues, key, "");
  }
  if (typeof formValues[key] === "object") {
    const inputValues = Object.keys(formValues[key]).reduce(
      (acc, innerKey) => {
        const nestedInputValue = getInputValue(
          formValues[key],
          innerKey,
          (filterValues || {})[key] ?? {}
        );
        acc[innerKey] = nestedInputValue;
        return acc;
      },
      {}
    );
    if (!Object.keys(inputValues).length) return "";
    return inputValues;
  }
  return (0, import_get13.default)(filterValues, key, "");
};

// node_modules/ra-core/src/controller/input/ReferenceInputBase.tsx
var import_jsx_runtime33 = __toESM(require_jsx_runtime());
var ReferenceInputBase = (props) => {
  const {
    children,
    reference,
    sort = { field: "id", order: "DESC" },
    filter = {}
  } = props;
  const controllerProps = useReferenceInputController({
    ...props,
    sort,
    filter
  });
  return (0, import_jsx_runtime33.jsx)(ResourceContextProvider, { value: reference, children: (0, import_jsx_runtime33.jsx)(ChoicesContextProvider, { value: controllerProps, children }) });
};

// node_modules/ra-core/src/controller/show/useShowController.ts
var useShowController = (props = {}) => {
  const {
    disableAuthentication = false,
    id: propsId,
    queryOptions = {}
  } = props;
  const resource = useResourceContext(props);
  if (!resource) {
    throw new Error(
      `useShowController requires a non-empty resource prop or context`
    );
  }
  const { isPending: isPendingAuthenticated } = useAuthenticated({
    enabled: !disableAuthentication
  });
  const { isPending: isPendingCanAccess } = useRequireAccess({
    action: "show",
    resource,
    // If disableAuthentication is true then isPendingAuthenticated will always be true so this hook is disabled
    enabled: !isPendingAuthenticated
  });
  const getRecordRepresentation = useGetRecordRepresentation(resource);
  const translate = useTranslate();
  const notify = useNotify();
  const redirect = useRedirect();
  const refresh = useRefresh();
  const { id: routeId } = useParams();
  if (!routeId && !propsId) {
    throw new Error(
      "useShowController requires an id prop or a route with an /:id? parameter."
    );
  }
  const id = propsId != null ? propsId : routeId;
  const { meta, ...otherQueryOptions } = queryOptions;
  const {
    data: record,
    error,
    isLoading,
    isFetching,
    isPending,
    refetch: refetch2
  } = useGetOne(
    resource,
    { id, meta },
    {
      enabled: !isPendingAuthenticated && !isPendingCanAccess || disableAuthentication,
      onError: () => {
        notify("ra.notification.item_doesnt_exist", {
          type: "error"
        });
        redirect("list", resource);
        refresh();
      },
      retry: false,
      ...otherQueryOptions
    }
  );
  if (record && record.id && record.id != id) {
    throw new Error(
      `useShowController: Fetched record's id attribute (${record.id}) must match the requested 'id' (${id})`
    );
  }
  const getResourceLabel = useGetResourceLabel();
  const recordRepresentation = getRecordRepresentation(record);
  const defaultTitle = translate("ra.page.show", {
    name: getResourceLabel(resource, 1),
    id,
    record,
    recordRepresentation: typeof recordRepresentation === "string" ? recordRepresentation : ""
  });
  return {
    defaultTitle,
    error,
    isLoading,
    isFetching,
    isPending,
    record,
    refetch: refetch2,
    resource
  };
};

// node_modules/ra-core/src/controller/show/ShowContext.tsx
var import_react111 = __toESM(require_react());
var ShowContext = (0, import_react111.createContext)(null);
ShowContext.displayName = "ShowContext";

// node_modules/ra-core/src/controller/show/ShowContextProvider.tsx
var import_jsx_runtime34 = __toESM(require_jsx_runtime());
var ShowContextProvider = ({
  children,
  value
}) => (0, import_jsx_runtime34.jsx)(ShowContext.Provider, { value, children: (0, import_jsx_runtime34.jsx)(RecordContextProvider, { value: value && value.record, children }) });

// node_modules/ra-core/src/controller/show/ShowBase.tsx
var import_jsx_runtime35 = __toESM(require_jsx_runtime());
var ShowBase = ({
  children,
  loading = null,
  ...props
}) => {
  const controllerProps = useShowController(props);
  const isAuthPending = useIsAuthPending({
    resource: controllerProps.resource,
    action: "show"
  });
  if (isAuthPending && !props.disableAuthentication) {
    return loading;
  }
  return (
    // We pass props.resource here as we don't need to create a new ResourceContext if the props is not provided
    (0, import_jsx_runtime35.jsx)(OptionalResourceContextProvider, { value: props.resource, children: (0, import_jsx_runtime35.jsx)(ShowContextProvider, { value: controllerProps, children }) })
  );
};

// node_modules/ra-core/src/controller/show/ShowController.tsx
var ShowController = ({
  children,
  ...props
}) => {
  const controllerProps = useShowController(props);
  return children(controllerProps);
};

// node_modules/ra-core/src/controller/show/useShowContext.tsx
var import_react112 = __toESM(require_react());
var useShowContext = () => {
  const context = (0, import_react112.useContext)(ShowContext);
  if (!context) {
    throw new Error(
      "useShowContext must be used inside a ShowContextProvider"
    );
  }
  return context;
};

// node_modules/ra-core/src/controller/usePrevNextController.ts
var usePrevNextController = (props) => {
  var _a, _b;
  const {
    linkType = "edit",
    storeKey,
    limit = 1e3,
    sort: initialSort = { field: "id", order: SORT_ASC },
    filter: permanentFilter = {},
    filterDefaultValues = {},
    queryOptions = {
      staleTime: 5 * 60 * 1e3
    }
  } = props;
  const record = useRecordContext(props);
  const resource = useResourceContext(props);
  const createPath = useCreatePath();
  if (!resource) {
    throw new Error(
      `useNextPrevController was called outside of a ResourceContext and without a resource prop. You must set the resource prop.`
    );
  }
  const [storedParams] = useStore(
    storeKey || `${resource}.listParams`,
    {
      filter: filterDefaultValues,
      order: initialSort.order,
      sort: initialSort.field,
      page: 1,
      perPage: 10,
      displayedFilters: {}
    }
  );
  const dataProvider = useDataProvider();
  const queryClient = useQueryClient();
  const pagination = { page: 1, perPage: limit };
  const sort = {
    field: storedParams.sort,
    order: storedParams.order
  };
  const filter = { ...storedParams.filter, ...permanentFilter };
  const { meta, ...otherQueryOptions } = queryOptions;
  const params = { pagination, sort, filter, meta };
  const queryData = queryClient.getQueryData([
    resource,
    "getList",
    {
      ...params,
      pagination: {
        page: storedParams.page,
        perPage: storedParams.perPage
      }
    }
  ]);
  const recordIndexInQueryData = (_a = queryData == null ? void 0 : queryData.data) == null ? void 0 : _a.findIndex(
    (r) => r.id === (record == null ? void 0 : record.id)
  );
  const isRecordIndexFirstInNonFirstPage = recordIndexInQueryData === 0 && storedParams.page > 1;
  const isRecordIndexLastInNonLastPage = (queryData == null ? void 0 : queryData.data) && (queryData == null ? void 0 : queryData.total) ? recordIndexInQueryData === ((_b = queryData == null ? void 0 : queryData.data) == null ? void 0 : _b.length) - 1 && storedParams.page < (queryData == null ? void 0 : queryData.total) / storedParams.perPage : void 0;
  const canUseCacheData = record && (queryData == null ? void 0 : queryData.data) && recordIndexInQueryData !== -1 && !isRecordIndexFirstInNonFirstPage && !isRecordIndexLastInNonLastPage;
  const { data, error, isFetching, isLoading, isPending } = useQuery({
    queryKey: [resource, "getList", params],
    queryFn: (queryParams) => {
      return dataProvider.getList(resource, {
        ...params,
        signal: dataProvider.supportAbortSignal === true ? queryParams.signal : void 0
      });
    },
    enabled: !canUseCacheData,
    ...otherQueryOptions
  });
  const finalData = canUseCacheData ? queryData.data : (data == null ? void 0 : data.data) || [];
  if (!record || isPending && !canUseCacheData)
    return {
      isFetching: true,
      isLoading: true,
      isPending: true,
      prevPath: void 0,
      nextPath: void 0,
      index: void 0,
      total: void 0,
      hasPrev: false,
      hasNext: false
    };
  const ids = finalData.map((record2) => record2.id);
  const index = ids.indexOf(record.id);
  const previousId = typeof ids[index - 1] !== "undefined" ? ids[index - 1] : null;
  const nextId = index !== -1 && index < ids.length - 1 ? ids[index + 1] : null;
  return {
    hasPrev: previousId !== null,
    hasNext: nextId !== null,
    prevPath: previousId !== null ? createPath({
      type: linkType,
      resource,
      id: previousId
    }) : void 0,
    nextPath: nextId !== null ? createPath({
      type: linkType,
      resource,
      id: nextId
    }) : void 0,
    index: index === -1 ? void 0 : index + (canUseCacheData ? (storedParams.perPage ?? 0) * ((storedParams.page ?? 1) - 1) : 0),
    total: canUseCacheData ? queryData == null ? void 0 : queryData.total : data == null ? void 0 : data.total,
    error,
    isFetching: canUseCacheData ? false : isFetching,
    isLoading: canUseCacheData ? false : isLoading,
    isPending: canUseCacheData ? false : isPending
  };
};

// node_modules/ra-core/src/dataProvider/useGetRecordId.ts
function useGetRecordId(recordId) {
  const contextRecord = useRecordContext();
  const { id: routeId } = useParams();
  const actualRecordId = recordId ?? (contextRecord == null ? void 0 : contextRecord.id) ?? routeId;
  if (actualRecordId == null)
    throw new Error(
      `useGetRecordId could not find the current record id. You need to use it inside a RecordContextProvider, or inside a supported route, or provide the record id to the hook yourself.`
    );
  return actualRecordId;
}

// node_modules/ra-core/src/dataProvider/useCreate.ts
var import_react113 = __toESM(require_react());
var useCreate = (resource, params = {}, options = {}) => {
  const dataProvider = useDataProvider();
  const queryClient = useQueryClient();
  const paramsRef = (0, import_react113.useRef)(params);
  const hasCallTimeOnError = (0, import_react113.useRef)(false);
  const hasCallTimeOnSuccess = (0, import_react113.useRef)(false);
  const hasCallTimeOnSettled = (0, import_react113.useRef)(false);
  const { getMutateWithMiddlewares, ...mutationOptions } = options;
  const mutation = useMutation({
    mutationFn: ({
      resource: callTimeResource = resource,
      data: callTimeData = paramsRef.current.data,
      meta: callTimeMeta = paramsRef.current.meta
    } = {}) => {
      if (!callTimeResource) {
        throw new Error(
          "useCreate mutation requires a non-empty resource"
        );
      }
      if (!callTimeData) {
        throw new Error(
          "useCreate mutation requires a non-empty data object"
        );
      }
      if (getMutateWithMiddlewares) {
        const createWithMiddlewares = getMutateWithMiddlewares(
          dataProvider.create.bind(dataProvider)
        );
        return createWithMiddlewares(callTimeResource, {
          data: callTimeData,
          meta: callTimeMeta
        }).then(({ data }) => data);
      }
      return dataProvider.create(callTimeResource, {
        data: callTimeData,
        meta: callTimeMeta
      }).then(({ data }) => data);
    },
    ...mutationOptions,
    onError: (error, variables, context) => {
      if (options.onError && !hasCallTimeOnError.current) {
        return options.onError(error, variables, context);
      }
    },
    onSuccess: (data, variables = {}, context) => {
      const { resource: callTimeResource = resource } = variables;
      queryClient.setQueryData(
        [callTimeResource, "getOne", { id: String(data.id) }],
        data
      );
      queryClient.invalidateQueries({
        queryKey: [callTimeResource, "getList"]
      });
      queryClient.invalidateQueries({
        queryKey: [callTimeResource, "getInfiniteList"]
      });
      queryClient.invalidateQueries({
        queryKey: [callTimeResource, "getMany"]
      });
      queryClient.invalidateQueries({
        queryKey: [callTimeResource, "getManyReference"]
      });
      if (options.onSuccess && !hasCallTimeOnSuccess.current) {
        options.onSuccess(data, variables, context);
      }
    },
    onSettled: (data, error, variables, context) => {
      if (options.onSettled && !hasCallTimeOnSettled.current) {
        return options.onSettled(data, error, variables, context);
      }
    }
  });
  const create = (callTimeResource = resource, callTimeParams = {}, callTimeOptions = {}) => {
    const {
      returnPromise = options.returnPromise,
      ...otherCallTimeOptions
    } = callTimeOptions;
    hasCallTimeOnError.current = !!otherCallTimeOptions.onError;
    hasCallTimeOnSuccess.current = !!otherCallTimeOptions.onSuccess;
    hasCallTimeOnSettled.current = !!otherCallTimeOptions.onSettled;
    if (returnPromise) {
      return mutation.mutateAsync(
        { resource: callTimeResource, ...callTimeParams },
        otherCallTimeOptions
      );
    }
    return mutation.mutate(
      { resource: callTimeResource, ...callTimeParams },
      otherCallTimeOptions
    );
  };
  const mutationResult = (0, import_react113.useMemo)(
    () => ({
      isLoading: mutation.isPending,
      ...mutation
    }),
    [mutation]
  );
  return [useEvent(create), mutationResult];
};

// node_modules/ra-core/src/dataProvider/useUpdate.ts
var import_react116 = __toESM(require_react());

// node_modules/ra-core/src/dataProvider/undo/useAddUndoableMutation.tsx
var import_react115 = __toESM(require_react());

// node_modules/ra-core/src/dataProvider/undo/AddUndoableMutationContext.tsx
var import_react114 = __toESM(require_react());
var AddUndoableMutationContext = (0, import_react114.createContext)(() => {
});

// node_modules/ra-core/src/dataProvider/undo/useAddUndoableMutation.tsx
var useAddUndoableMutation = () => (0, import_react115.useContext)(AddUndoableMutationContext);

// node_modules/ra-core/src/dataProvider/useUpdate.ts
var useUpdate = (resource, params = {}, options = {}) => {
  const dataProvider = useDataProvider();
  const queryClient = useQueryClient();
  const addUndoableMutation = useAddUndoableMutation();
  const { id, data, meta } = params;
  const {
    mutationMode = "pessimistic",
    getMutateWithMiddlewares,
    ...mutationOptions
  } = options;
  const mode = (0, import_react116.useRef)(mutationMode);
  const paramsRef = (0, import_react116.useRef)(params);
  const snapshot = (0, import_react116.useRef)([]);
  const mutateWithMiddlewares = (0, import_react116.useRef)(dataProvider.update);
  const callTimeOnError = (0, import_react116.useRef)();
  const callTimeOnSettled = (0, import_react116.useRef)();
  const hasCallTimeOnSuccess = (0, import_react116.useRef)(false);
  const updateCache = ({ resource: resource2, id: id2, data: data2, meta: meta2 }) => {
    const now = Date.now();
    const updatedAt = mode.current === "undoable" ? now + 5 * 1e3 : now;
    const clonedData = JSON.parse(JSON.stringify(data2));
    const updateColl = (old) => {
      if (!old) return old;
      const index = old.findIndex(
        // eslint-disable-next-line eqeqeq
        (record) => record.id == id2
      );
      if (index === -1) {
        return old;
      }
      return [
        ...old.slice(0, index),
        { ...old[index], ...clonedData },
        ...old.slice(index + 1)
      ];
    };
    queryClient.setQueryData(
      [resource2, "getOne", { id: String(id2), meta: meta2 }],
      (record) => ({ ...record, ...clonedData }),
      { updatedAt }
    );
    queryClient.setQueriesData(
      { queryKey: [resource2, "getList"] },
      (res) => res && res.data ? { ...res, data: updateColl(res.data) } : res,
      { updatedAt }
    );
    queryClient.setQueriesData(
      { queryKey: [resource2, "getInfiniteList"] },
      (res) => res && res.pages ? {
        ...res,
        pages: res.pages.map((page) => ({
          ...page,
          data: updateColl(page.data)
        }))
      } : res,
      { updatedAt }
    );
    queryClient.setQueriesData(
      { queryKey: [resource2, "getMany"] },
      (coll) => coll && coll.length > 0 ? updateColl(coll) : coll,
      { updatedAt }
    );
    queryClient.setQueriesData(
      { queryKey: [resource2, "getManyReference"] },
      (res) => res && res.data ? { data: updateColl(res.data), total: res.total } : res,
      { updatedAt }
    );
  };
  const mutation = useMutation({
    mutationFn: ({
      resource: callTimeResource = resource,
      id: callTimeId = paramsRef.current.id,
      data: callTimeData = paramsRef.current.data,
      meta: callTimeMeta = paramsRef.current.meta,
      previousData: callTimePreviousData = paramsRef.current.previousData
    } = {}) => {
      if (!callTimeResource) {
        throw new Error(
          "useUpdate mutation requires a non-empty resource"
        );
      }
      if (callTimeId == null) {
        throw new Error("useUpdate mutation requires a non-empty id");
      }
      if (!callTimeData) {
        throw new Error(
          "useUpdate mutation requires a non-empty data object"
        );
      }
      return mutateWithMiddlewares.current(callTimeResource, {
        id: callTimeId,
        data: callTimeData,
        previousData: callTimePreviousData,
        meta: callTimeMeta
      }).then(({ data: data2 }) => data2);
    },
    ...mutationOptions,
    onMutate: async (variables) => {
      if (mutationOptions.onMutate) {
        const userContext = await mutationOptions.onMutate(variables) || {};
        return {
          snapshot: snapshot.current,
          // @ts-ignore
          ...userContext
        };
      } else {
        return { snapshot: snapshot.current };
      }
    },
    onError: (error, variables = {}, context) => {
      if (mode.current === "optimistic" || mode.current === "undoable") {
        context.snapshot.forEach(([key, value]) => {
          queryClient.setQueryData(key, value);
        });
      }
      if (callTimeOnError.current) {
        return callTimeOnError.current(error, variables, context);
      }
      if (mutationOptions.onError) {
        return mutationOptions.onError(error, variables, context);
      }
    },
    onSuccess: (data2, variables = {}, context) => {
      if (mode.current === "pessimistic") {
        const {
          resource: callTimeResource = resource,
          id: callTimeId = id,
          meta: callTimeMeta = meta
        } = variables;
        updateCache({
          resource: callTimeResource,
          id: callTimeId,
          data: data2,
          meta: callTimeMeta
        });
        if (mutationOptions.onSuccess && !hasCallTimeOnSuccess.current) {
          mutationOptions.onSuccess(data2, variables, context);
        }
      }
    },
    onSettled: (data2, error, variables = {}, context) => {
      if (mode.current === "optimistic" || mode.current === "undoable") {
        context.snapshot.forEach(([queryKey]) => {
          queryClient.invalidateQueries({ queryKey });
        });
      }
      if (callTimeOnSettled.current) {
        return callTimeOnSettled.current(
          data2,
          error,
          variables,
          context
        );
      }
      if (mutationOptions.onSettled) {
        return mutationOptions.onSettled(
          data2,
          error,
          variables,
          context
        );
      }
    }
  });
  const update = async (callTimeResource = resource, callTimeParams = {}, callTimeOptions = {}) => {
    const {
      mutationMode: mutationMode2,
      returnPromise = mutationOptions.returnPromise,
      onError,
      onSettled,
      onSuccess,
      ...otherCallTimeOptions
    } = callTimeOptions;
    if (getMutateWithMiddlewares) {
      mutateWithMiddlewares.current = getMutateWithMiddlewares(
        dataProvider.update.bind(dataProvider)
      );
    } else {
      mutateWithMiddlewares.current = dataProvider.update;
    }
    hasCallTimeOnSuccess.current = !!onSuccess;
    callTimeOnError.current = onError;
    callTimeOnSettled.current = onSettled;
    paramsRef.current = params;
    if (mutationMode2) {
      mode.current = mutationMode2;
    }
    if (returnPromise && mode.current !== "pessimistic") {
      console.warn(
        "The returnPromise parameter can only be used if the mutationMode is set to pessimistic"
      );
    }
    if (mode.current === "pessimistic") {
      if (returnPromise) {
        return mutation.mutateAsync(
          { resource: callTimeResource, ...callTimeParams },
          // We don't pass onError and onSettled here as we will call them in the useMutation hook side effects
          { onSuccess, ...otherCallTimeOptions }
        );
      }
      return mutation.mutate(
        { resource: callTimeResource, ...callTimeParams },
        // We don't pass onError and onSettled here as we will call them in the useMutation hook side effects
        { onSuccess, ...otherCallTimeOptions }
      );
    }
    const {
      id: callTimeId = id,
      data: callTimeData = data,
      meta: callTimeMeta = meta
    } = callTimeParams;
    const previousRecord = queryClient.getQueryData([
      callTimeResource,
      "getOne",
      { id: String(callTimeId), meta: callTimeMeta }
    ]);
    const queryKeys = [
      [
        callTimeResource,
        "getOne",
        { id: String(callTimeId), meta: callTimeMeta }
      ],
      [callTimeResource, "getList"],
      [callTimeResource, "getInfiniteList"],
      [callTimeResource, "getMany"],
      [callTimeResource, "getManyReference"]
    ];
    snapshot.current = queryKeys.reduce(
      (prev, queryKey) => prev.concat(queryClient.getQueriesData({ queryKey })),
      []
    );
    await Promise.all(
      snapshot.current.map(
        ([queryKey]) => queryClient.cancelQueries({ queryKey })
      )
    );
    updateCache({
      resource: callTimeResource,
      id: callTimeId,
      data: callTimeData,
      meta: callTimeMeta
    });
    setTimeout(() => {
      if (onSuccess) {
        onSuccess(
          { ...previousRecord, ...callTimeData },
          { resource: callTimeResource, ...callTimeParams },
          { snapshot: snapshot.current }
        );
      } else if (mutationOptions.onSuccess && !hasCallTimeOnSuccess.current) {
        mutationOptions.onSuccess(
          { ...previousRecord, ...callTimeData },
          { resource: callTimeResource, ...callTimeParams },
          { snapshot: snapshot.current }
        );
      }
    }, 0);
    if (mode.current === "optimistic") {
      return mutation.mutate({
        resource: callTimeResource,
        // We don't pass onError and onSettled here as we will call them in the useMutation hook side effects
        ...callTimeParams
      });
    } else {
      addUndoableMutation(({ isUndo }) => {
        if (isUndo) {
          snapshot.current.forEach(([key, value]) => {
            queryClient.setQueryData(key, value);
          });
        } else {
          mutation.mutate({
            resource: callTimeResource,
            ...callTimeParams
          });
        }
      });
    }
  };
  const mutationResult = (0, import_react116.useMemo)(
    () => ({
      isLoading: mutation.isPending,
      ...mutation
    }),
    [mutation]
  );
  return [useEvent(update), mutationResult];
};

// node_modules/ra-core/src/dataProvider/useUpdateMany.ts
var import_react117 = __toESM(require_react());
var useUpdateMany = (resource, params = {}, options = {}) => {
  const dataProvider = useDataProvider();
  const queryClient = useQueryClient();
  const addUndoableMutation = useAddUndoableMutation();
  const { ids, data, meta } = params;
  const { mutationMode = "pessimistic", ...mutationOptions } = options;
  const mode = (0, import_react117.useRef)(mutationMode);
  const paramsRef = (0, import_react117.useRef)(params);
  const snapshot = (0, import_react117.useRef)([]);
  const hasCallTimeOnError = (0, import_react117.useRef)(false);
  const hasCallTimeOnSuccess = (0, import_react117.useRef)(false);
  const hasCallTimeOnSettled = (0, import_react117.useRef)(false);
  const updateCache = async ({
    resource: resource2,
    ids: ids2,
    data: data2,
    meta: meta2
  }) => {
    const updatedAt = mode.current === "undoable" ? Date.now() + 1e3 * 5 : Date.now();
    const clonedData = JSON.parse(JSON.stringify(data2));
    const updateColl = (old) => {
      if (!old) return old;
      let newCollection = [...old];
      ids2.forEach((id) => {
        const index = old.findIndex((record) => record.id == id);
        if (index === -1) {
          return;
        }
        newCollection = [
          ...newCollection.slice(0, index),
          { ...newCollection[index], ...clonedData },
          ...newCollection.slice(index + 1)
        ];
      });
      return newCollection;
    };
    ids2.forEach((id) => {
      queryClient.setQueryData(
        [resource2, "getOne", { id: String(id), meta: meta2 }],
        (record) => ({ ...record, ...clonedData }),
        { updatedAt }
      );
    });
    queryClient.setQueriesData(
      { queryKey: [resource2, "getList"] },
      (res) => res && res.data ? { ...res, data: updateColl(res.data) } : res,
      { updatedAt }
    );
    queryClient.setQueriesData(
      { queryKey: [resource2, "getInfiniteList"] },
      (res) => res && res.pages ? {
        ...res,
        pages: res.pages.map((page) => ({
          ...page,
          data: updateColl(page.data)
        }))
      } : res,
      { updatedAt }
    );
    queryClient.setQueriesData(
      { queryKey: [resource2, "getMany"] },
      (coll) => coll && coll.length > 0 ? updateColl(coll) : coll,
      { updatedAt }
    );
    queryClient.setQueriesData(
      { queryKey: [resource2, "getManyReference"] },
      (res) => res && res.data ? { data: updateColl(res.data), total: res.total } : res,
      { updatedAt }
    );
  };
  const mutation = useMutation({
    mutationFn: ({
      resource: callTimeResource = resource,
      ids: callTimeIds = paramsRef.current.ids,
      data: callTimeData = paramsRef.current.data,
      meta: callTimeMeta = paramsRef.current.meta
    } = {}) => {
      if (!callTimeResource) {
        throw new Error(
          "useUpdateMany mutation requires a non-empty resource"
        );
      }
      if (!callTimeIds) {
        throw new Error(
          "useUpdateMany mutation requires an array of ids"
        );
      }
      if (!callTimeData) {
        throw new Error(
          "useUpdateMany mutation requires a non-empty data object"
        );
      }
      return dataProvider.updateMany(callTimeResource, {
        ids: callTimeIds,
        data: callTimeData,
        meta: callTimeMeta
      }).then(({ data: data2 }) => data2 || []);
    },
    ...mutationOptions,
    onMutate: async (variables) => {
      if (mutationOptions.onMutate) {
        const userContext = await mutationOptions.onMutate(variables) || {};
        return {
          snapshot: snapshot.current,
          // @ts-ignore
          ...userContext
        };
      } else {
        return { snapshot: snapshot.current };
      }
    },
    onError: (error, variables = {}, context) => {
      if (mode.current === "optimistic" || mode.current === "undoable") {
        context.snapshot.forEach(([key, value]) => {
          queryClient.setQueryData(key, value);
        });
      }
      if (mutationOptions.onError && !hasCallTimeOnError.current) {
        return mutationOptions.onError(error, variables, context);
      }
    },
    onSuccess: (dataFromResponse, variables = {}, context) => {
      if (mode.current === "pessimistic") {
        const {
          resource: callTimeResource = resource,
          ids: callTimeIds = ids,
          data: callTimeData = data,
          meta: callTimeMeta = meta
        } = variables;
        if (!callTimeResource) {
          throw new Error(
            "useUpdateMany mutation requires a non-empty resource"
          );
        }
        if (!callTimeIds) {
          throw new Error(
            "useUpdateMany mutation requires an array of ids"
          );
        }
        updateCache({
          resource: callTimeResource,
          ids: callTimeIds,
          data: callTimeData,
          meta: callTimeMeta
        });
        if (mutationOptions.onSuccess && !hasCallTimeOnSuccess.current) {
          mutationOptions.onSuccess(
            dataFromResponse,
            variables,
            context
          );
        }
      }
    },
    onSettled: (data2, error, variables = {}, context) => {
      if (mode.current === "optimistic" || mode.current === "undoable") {
        context.snapshot.forEach(([queryKey]) => {
          queryClient.invalidateQueries({ queryKey });
        });
      }
      if (mutationOptions.onSettled && !hasCallTimeOnSettled.current) {
        return mutationOptions.onSettled(
          data2,
          error,
          variables,
          context
        );
      }
    }
  });
  const updateMany = async (callTimeResource = resource, callTimeParams = {}, callTimeOptions = {}) => {
    if (!callTimeResource) {
      throw new Error(
        "useUpdateMany mutation requires a non-empty resource"
      );
    }
    const {
      mutationMode: mutationMode2,
      returnPromise = mutationOptions.returnPromise,
      ...otherCallTimeOptions
    } = callTimeOptions;
    hasCallTimeOnError.current = !!otherCallTimeOptions.onError;
    hasCallTimeOnSuccess.current = !!otherCallTimeOptions.onSuccess;
    hasCallTimeOnSettled.current = !!otherCallTimeOptions.onSettled;
    paramsRef.current = params;
    if (mutationMode2) {
      mode.current = mutationMode2;
    }
    if (returnPromise && mode.current !== "pessimistic") {
      console.warn(
        "The returnPromise parameter can only be used if the mutationMode is set to pessimistic"
      );
    }
    if (mode.current === "pessimistic") {
      if (returnPromise) {
        return mutation.mutateAsync(
          { resource: callTimeResource, ...callTimeParams },
          otherCallTimeOptions
        );
      }
      return mutation.mutate(
        { resource: callTimeResource, ...callTimeParams },
        otherCallTimeOptions
      );
    }
    const {
      ids: callTimeIds = ids,
      data: callTimeData = data,
      meta: callTimeMeta = meta
    } = callTimeParams;
    if (!callTimeIds) {
      throw new Error("useUpdateMany mutation requires an array of ids");
    }
    const queryKeys = [
      [callTimeResource, "getOne"],
      [callTimeResource, "getList"],
      [callTimeResource, "getInfiniteList"],
      [callTimeResource, "getMany"],
      [callTimeResource, "getManyReference"]
    ];
    snapshot.current = queryKeys.reduce(
      (prev, queryKey) => prev.concat(queryClient.getQueriesData({ queryKey })),
      []
    );
    await Promise.all(
      snapshot.current.map(
        ([queryKey]) => queryClient.cancelQueries({ queryKey })
      )
    );
    await updateCache({
      resource: callTimeResource,
      ids: callTimeIds,
      data: callTimeData,
      meta: callTimeMeta
    });
    setTimeout(() => {
      if (otherCallTimeOptions.onSuccess) {
        otherCallTimeOptions.onSuccess(
          callTimeIds,
          { resource: callTimeResource, ...callTimeParams },
          { snapshot: snapshot.current }
        );
      } else if (mutationOptions.onSuccess) {
        mutationOptions.onSuccess(
          callTimeIds,
          { resource: callTimeResource, ...callTimeParams },
          { snapshot: snapshot.current }
        );
      }
    }, 0);
    if (mode.current === "optimistic") {
      return mutation.mutate(
        { resource: callTimeResource, ...callTimeParams },
        {
          onSettled: otherCallTimeOptions.onSettled,
          onError: otherCallTimeOptions.onError
        }
      );
    } else {
      addUndoableMutation(({ isUndo }) => {
        if (isUndo) {
          snapshot.current.forEach(([key, value]) => {
            queryClient.setQueryData(key, value);
          });
        } else {
          mutation.mutate(
            { resource: callTimeResource, ...callTimeParams },
            {
              onSettled: otherCallTimeOptions.onSettled,
              onError: otherCallTimeOptions.onError
            }
          );
        }
      });
    }
  };
  const mutationResult = (0, import_react117.useMemo)(
    () => ({
      isLoading: mutation.isPending,
      ...mutation
    }),
    [mutation]
  );
  return [useEvent(updateMany), mutationResult];
};

// node_modules/ra-core/src/dataProvider/useDelete.ts
var import_react118 = __toESM(require_react());
var useDelete = (resource, params = {}, options = {}) => {
  const dataProvider = useDataProvider();
  const queryClient = useQueryClient();
  const addUndoableMutation = useAddUndoableMutation();
  const { id, previousData } = params;
  const { mutationMode = "pessimistic", ...mutationOptions } = options;
  const mode = (0, import_react118.useRef)(mutationMode);
  const paramsRef = (0, import_react118.useRef)(params);
  const snapshot = (0, import_react118.useRef)([]);
  const hasCallTimeOnError = (0, import_react118.useRef)(false);
  const hasCallTimeOnSuccess = (0, import_react118.useRef)(false);
  const hasCallTimeOnSettled = (0, import_react118.useRef)(false);
  const updateCache = ({ resource: resource2, id: id2 }) => {
    const now = Date.now();
    const updatedAt = mode.current === "undoable" ? now + 5 * 1e3 : now;
    const updateColl = (old) => {
      if (!old) return old;
      const index = old.findIndex(
        // eslint-disable-next-line eqeqeq
        (record) => record.id == id2
      );
      if (index === -1) {
        return old;
      }
      return [...old.slice(0, index), ...old.slice(index + 1)];
    };
    queryClient.setQueriesData(
      { queryKey: [resource2, "getList"] },
      (res) => {
        if (!res || !res.data) return res;
        const newCollection = updateColl(res.data);
        const recordWasFound = newCollection.length < res.data.length;
        return recordWasFound ? {
          data: newCollection,
          total: res.total ? res.total - 1 : void 0,
          pageInfo: res.pageInfo
        } : res;
      },
      { updatedAt }
    );
    queryClient.setQueriesData(
      { queryKey: [resource2, "getInfiniteList"] },
      (res) => {
        if (!res || !res.pages) return res;
        return {
          ...res,
          pages: res.pages.map((page) => {
            const newCollection = updateColl(page.data);
            const recordWasFound = newCollection.length < page.data.length;
            return recordWasFound ? {
              ...page,
              data: newCollection,
              total: page.total ? page.total - 1 : void 0,
              pageInfo: page.pageInfo
            } : page;
          })
        };
      },
      { updatedAt }
    );
    queryClient.setQueriesData(
      { queryKey: [resource2, "getMany"] },
      (coll) => coll && coll.length > 0 ? updateColl(coll) : coll,
      { updatedAt }
    );
    queryClient.setQueriesData(
      { queryKey: [resource2, "getManyReference"] },
      (res) => {
        if (!res || !res.data) return res;
        const newCollection = updateColl(res.data);
        const recordWasFound = newCollection.length < res.data.length;
        return recordWasFound ? {
          data: newCollection,
          total: res.total - 1
        } : res;
      },
      { updatedAt }
    );
  };
  const mutation = useMutation({
    mutationFn: ({
      resource: callTimeResource = resource,
      id: callTimeId = paramsRef.current.id,
      previousData: callTimePreviousData = paramsRef.current.previousData,
      meta: callTimeMeta = paramsRef.current.meta
    } = {}) => {
      if (!callTimeResource) {
        throw new Error(
          "useDelete mutation requires a non-empty resource"
        );
      }
      if (callTimeId == null) {
        throw new Error("useDelete mutation requires a non-empty id");
      }
      return dataProvider.delete(callTimeResource, {
        id: callTimeId,
        previousData: callTimePreviousData,
        meta: callTimeMeta
      }).then(({ data }) => data);
    },
    ...mutationOptions,
    onMutate: async (variables) => {
      if (mutationOptions.onMutate) {
        const userContext = await mutationOptions.onMutate(variables) || {};
        return {
          snapshot: snapshot.current,
          // @ts-ignore
          ...userContext
        };
      } else {
        return { snapshot: snapshot.current };
      }
    },
    onError: (error, variables = {}, context) => {
      if (mode.current === "optimistic" || mode.current === "undoable") {
        context.snapshot.forEach(([key, value]) => {
          queryClient.setQueryData(key, value);
        });
      }
      if (mutationOptions.onError && !hasCallTimeOnError.current) {
        return mutationOptions.onError(error, variables, context);
      }
    },
    onSuccess: (data, variables = {}, context) => {
      if (mode.current === "pessimistic") {
        const {
          resource: callTimeResource = resource,
          id: callTimeId = id
        } = variables;
        updateCache({
          resource: callTimeResource,
          id: callTimeId
        });
        if (mutationOptions.onSuccess && !hasCallTimeOnSuccess.current) {
          mutationOptions.onSuccess(data, variables, context);
        }
      }
    },
    onSettled: (data, error, variables = {}, context) => {
      context.snapshot.forEach(([queryKey]) => {
        queryClient.invalidateQueries({ queryKey });
      });
      if (mutationOptions.onSettled && !hasCallTimeOnSettled.current) {
        return mutationOptions.onSettled(
          data,
          error,
          variables,
          context
        );
      }
    }
  });
  const mutate = async (callTimeResource = resource, callTimeParams = {}, callTimeOptions = {}) => {
    const { mutationMode: mutationMode2, ...otherCallTimeOptions } = callTimeOptions;
    hasCallTimeOnError.current = !!callTimeOptions.onError;
    hasCallTimeOnSuccess.current = !!callTimeOptions.onSuccess;
    hasCallTimeOnSettled.current = !!callTimeOptions.onSettled;
    paramsRef.current = params;
    if (mutationMode2) {
      mode.current = mutationMode2;
    }
    const {
      id: callTimeId = id,
      previousData: callTimePreviousData = previousData
    } = callTimeParams;
    const queryKeys = [
      [callTimeResource, "getList"],
      [callTimeResource, "getInfiniteList"],
      [callTimeResource, "getMany"],
      [callTimeResource, "getManyReference"]
    ];
    snapshot.current = queryKeys.reduce(
      (prev, queryKey) => prev.concat(queryClient.getQueriesData({ queryKey })),
      []
    );
    if (mode.current === "pessimistic") {
      return mutation.mutate(
        { resource: callTimeResource, ...callTimeParams },
        otherCallTimeOptions
      );
    }
    await Promise.all(
      snapshot.current.map(
        ([queryKey]) => queryClient.cancelQueries({ queryKey })
      )
    );
    updateCache({
      resource: callTimeResource,
      id: callTimeId
    });
    setTimeout(() => {
      if (callTimeOptions.onSuccess) {
        callTimeOptions.onSuccess(
          callTimePreviousData,
          { resource: callTimeResource, ...callTimeParams },
          { snapshot: snapshot.current }
        );
      } else if (mutationOptions.onSuccess) {
        mutationOptions.onSuccess(
          callTimePreviousData,
          { resource: callTimeResource, ...callTimeParams },
          { snapshot: snapshot.current }
        );
      }
    }, 0);
    if (mode.current === "optimistic") {
      return mutation.mutate(
        { resource: callTimeResource, ...callTimeParams },
        {
          onSettled: callTimeOptions.onSettled,
          onError: callTimeOptions.onError
        }
      );
    } else {
      addUndoableMutation(({ isUndo }) => {
        if (isUndo) {
          snapshot.current.forEach(([key, value]) => {
            queryClient.setQueryData(key, value);
          });
        } else {
          mutation.mutate(
            { resource: callTimeResource, ...callTimeParams },
            {
              onSettled: callTimeOptions.onSettled,
              onError: callTimeOptions.onError
            }
          );
        }
      });
    }
  };
  const mutationResult = (0, import_react118.useMemo)(
    () => ({
      isLoading: mutation.isPending,
      ...mutation
    }),
    [mutation]
  );
  return [useEvent(mutate), mutationResult];
};

// node_modules/ra-core/src/dataProvider/useDeleteMany.ts
var import_react119 = __toESM(require_react());
var useDeleteMany = (resource, params = {}, options = {}) => {
  const dataProvider = useDataProvider();
  const queryClient = useQueryClient();
  const addUndoableMutation = useAddUndoableMutation();
  const { ids } = params;
  const { mutationMode = "pessimistic", ...mutationOptions } = options;
  const mode = (0, import_react119.useRef)(mutationMode);
  const paramsRef = (0, import_react119.useRef)({});
  const snapshot = (0, import_react119.useRef)([]);
  const hasCallTimeOnError = (0, import_react119.useRef)(false);
  const hasCallTimeOnSuccess = (0, import_react119.useRef)(false);
  const hasCallTimeOnSettled = (0, import_react119.useRef)(false);
  const updateCache = ({ resource: resource2, ids: ids2 }) => {
    const now = Date.now();
    const updatedAt = mode.current === "undoable" ? now + 5 * 1e3 : now;
    const updateColl = (old) => {
      if (!old) return old;
      let newCollection = [...old];
      ids2.forEach((id) => {
        const index = newCollection.findIndex(
          // eslint-disable-next-line eqeqeq
          (record) => record.id == id
        );
        if (index === -1) {
          return;
        }
        newCollection = [
          ...newCollection.slice(0, index),
          ...newCollection.slice(index + 1)
        ];
      });
      return newCollection;
    };
    queryClient.setQueriesData(
      { queryKey: [resource2, "getList"] },
      (res) => {
        if (!res || !res.data) return res;
        const newCollection = updateColl(res.data);
        const recordWasFound = newCollection.length < res.data.length;
        return recordWasFound ? {
          data: newCollection,
          total: res.total ? res.total - (res.data.length - newCollection.length) : void 0,
          pageInfo: res.pageInfo
        } : res;
      },
      { updatedAt }
    );
    queryClient.setQueriesData(
      { queryKey: [resource2, "getInfiniteList"] },
      (res) => {
        if (!res || !res.pages) return res;
        return {
          ...res,
          pages: res.pages.map((page) => {
            const newCollection = updateColl(page.data);
            const recordWasFound = newCollection.length < page.data.length;
            return recordWasFound ? {
              ...page,
              data: newCollection,
              total: page.total ? page.total - (page.data.length - newCollection.length) : void 0,
              pageInfo: page.pageInfo
            } : page;
          })
        };
      },
      { updatedAt }
    );
    queryClient.setQueriesData(
      { queryKey: [resource2, "getMany"] },
      (coll) => coll && coll.length > 0 ? updateColl(coll) : coll,
      { updatedAt }
    );
    queryClient.setQueriesData(
      { queryKey: [resource2, "getManyReference"] },
      (res) => {
        if (!res || !res.data) return res;
        const newCollection = updateColl(res.data);
        const recordWasFound = newCollection.length < res.data.length;
        if (!recordWasFound) {
          return res;
        }
        if (res.total) {
          return {
            data: newCollection,
            total: res.total - (res.data.length - newCollection.length)
          };
        }
        if (res.pageInfo) {
          return {
            data: newCollection,
            pageInfo: res.pageInfo
          };
        }
        throw new Error(
          "Found getList result in cache without total or pageInfo"
        );
      },
      { updatedAt }
    );
  };
  const mutation = useMutation({
    mutationFn: ({
      resource: callTimeResource = resource,
      ids: callTimeIds = paramsRef.current.ids,
      meta: callTimeMeta = paramsRef.current.meta
    } = {}) => {
      if (!callTimeResource) {
        throw new Error(
          "useDeleteMany mutation requires a non-empty resource"
        );
      }
      if (!callTimeIds) {
        throw new Error(
          "useDeleteMany mutation requires an array of ids"
        );
      }
      if (callTimeIds.length === 0) {
        return Promise.resolve([]);
      }
      return dataProvider.deleteMany(callTimeResource, {
        ids: callTimeIds,
        meta: callTimeMeta
      }).then(({ data }) => data || []);
    },
    ...mutationOptions,
    onMutate: async (variables) => {
      if (mutationOptions.onMutate) {
        const userContext = await mutationOptions.onMutate(variables) || {};
        return {
          snapshot: snapshot.current,
          // @ts-ignore
          ...userContext
        };
      } else {
        return { snapshot: snapshot.current };
      }
    },
    onError: (error, variables = {}, context) => {
      if (mode.current === "optimistic" || mode.current === "undoable") {
        context.snapshot.forEach(([key, value]) => {
          queryClient.setQueryData(key, value);
        });
      }
      if (mutationOptions.onError && !hasCallTimeOnError.current) {
        return mutationOptions.onError(error, variables, context);
      }
    },
    onSuccess: (data, variables = {}, context) => {
      if (mode.current === "pessimistic") {
        const {
          resource: callTimeResource = resource,
          ids: callTimeIds = ids
        } = variables;
        updateCache({
          resource: callTimeResource,
          ids: callTimeIds
        });
        if (mutationOptions.onSuccess && !hasCallTimeOnSuccess.current) {
          mutationOptions.onSuccess(data, variables, context);
        }
      }
    },
    onSettled: (data, error, variables = {}, context) => {
      if (mode.current === "optimistic" || mode.current === "undoable") {
        context.snapshot.forEach(([queryKey]) => {
          queryClient.invalidateQueries({ queryKey });
        });
      }
      if (mutationOptions.onSettled && !hasCallTimeOnSettled.current) {
        return mutationOptions.onSettled(
          data,
          error,
          variables,
          context
        );
      }
    }
  });
  const mutate = async (callTimeResource = resource, callTimeParams = {}, callTimeOptions = {}) => {
    const { mutationMode: mutationMode2, ...otherCallTimeOptions } = callTimeOptions;
    hasCallTimeOnError.current = !!callTimeOptions.onError;
    hasCallTimeOnSuccess.current = !!callTimeOptions.onSuccess;
    hasCallTimeOnSettled.current = !!callTimeOptions.onSettled;
    paramsRef.current = params;
    if (mutationMode2) {
      mode.current = mutationMode2;
    }
    if (mode.current === "pessimistic") {
      return mutation.mutate(
        { resource: callTimeResource, ...callTimeParams },
        {
          onSuccess: otherCallTimeOptions.onSuccess,
          onSettled: otherCallTimeOptions.onSettled,
          onError: otherCallTimeOptions.onError
        }
      );
    }
    const { ids: callTimeIds = ids } = callTimeParams;
    if (!callTimeIds) {
      throw new Error("useDeleteMany mutation requires an array of ids");
    }
    const queryKeys = [
      [callTimeResource, "getList"],
      [callTimeResource, "getInfiniteList"],
      [callTimeResource, "getMany"],
      [callTimeResource, "getManyReference"]
    ];
    snapshot.current = queryKeys.reduce(
      (prev, queryKey) => prev.concat(queryClient.getQueriesData({ queryKey })),
      []
    );
    await Promise.all(
      snapshot.current.map(
        ([queryKey]) => queryClient.cancelQueries({ queryKey })
      )
    );
    updateCache({
      resource: callTimeResource,
      ids: callTimeIds
    });
    setTimeout(() => {
      if (otherCallTimeOptions.onSuccess) {
        otherCallTimeOptions.onSuccess(
          callTimeIds,
          { resource: callTimeResource, ...callTimeParams },
          { snapshot: snapshot.current }
        );
      } else if (mutationOptions.onSuccess) {
        mutationOptions.onSuccess(
          callTimeIds,
          { resource: callTimeResource, ...callTimeParams },
          { snapshot: snapshot.current }
        );
      }
    }, 0);
    if (mode.current === "optimistic") {
      return mutation.mutate(
        { resource: callTimeResource, ...callTimeParams },
        {
          onSettled: otherCallTimeOptions.onSettled,
          onError: otherCallTimeOptions.onError
        }
      );
    } else {
      addUndoableMutation(({ isUndo }) => {
        if (isUndo) {
          snapshot.current.forEach(([key, value]) => {
            queryClient.setQueryData(key, value);
          });
        } else {
          mutation.mutate(
            { resource: callTimeResource, ...callTimeParams },
            {
              onSettled: otherCallTimeOptions.onSettled,
              onError: otherCallTimeOptions.onError
            }
          );
        }
      });
    }
  };
  const mutationResult = (0, import_react119.useMemo)(
    () => ({
      isLoading: mutation.isPending,
      ...mutation
    }),
    [mutation]
  );
  return [useEvent(mutate), mutationResult];
};

// node_modules/ra-core/src/dataProvider/useInfiniteGetList.ts
var import_react120 = __toESM(require_react());
var MAX_DATA_LENGTH_TO_CACHE2 = 100;
var useInfiniteGetList = (resource, params = {}, options = {}) => {
  var _a, _b, _c, _d;
  const {
    pagination = { page: 1, perPage: 25 },
    sort = { field: "id", order: "DESC" },
    filter = {},
    meta
  } = params;
  const dataProvider = useDataProvider();
  const queryClient = useQueryClient();
  const {
    onSuccess = noop6,
    onError = noop6,
    onSettled = noop6,
    ...queryOptions
  } = options;
  const onSuccessEvent = useEvent(onSuccess);
  const onErrorEvent = useEvent(onError);
  const onSettledEvent = useEvent(onSettled);
  const result = useInfiniteQuery({
    queryKey: [
      resource,
      "getInfiniteList",
      { pagination, sort, filter, meta }
    ],
    queryFn: (queryParams) => {
      const { pageParam = pagination.page } = queryParams;
      return dataProvider.getList(resource, {
        pagination: {
          page: pageParam,
          perPage: pagination.perPage
        },
        sort,
        filter,
        meta,
        signal: dataProvider.supportAbortSignal === true ? queryParams.signal : void 0
      }).then(({ data, pageInfo, total, meta: meta2 }) => ({
        data,
        total,
        pageParam,
        pageInfo,
        meta: meta2
      }));
    },
    initialPageParam: pagination.page,
    ...queryOptions,
    getNextPageParam: (lastLoadedPage) => {
      if (lastLoadedPage.pageInfo) {
        return lastLoadedPage.pageInfo.hasNextPage ? lastLoadedPage.pageParam + 1 : void 0;
      }
      const totalPages = Math.ceil(
        (lastLoadedPage.total || 0) / pagination.perPage
      );
      return lastLoadedPage.pageParam < totalPages ? Number(lastLoadedPage.pageParam) + 1 : void 0;
    },
    getPreviousPageParam: (lastLoadedPage) => {
      if (lastLoadedPage.pageInfo) {
        return lastLoadedPage.pageInfo.hasPreviousPage ? lastLoadedPage.pageParam - 1 : void 0;
      }
      return lastLoadedPage.pageParam === 1 ? void 0 : lastLoadedPage.pageParam - 1;
    }
  });
  const metaValue = (0, import_react120.useRef)(meta);
  const resourceValue = (0, import_react120.useRef)(resource);
  (0, import_react120.useEffect)(() => {
    metaValue.current = meta;
  }, [meta]);
  (0, import_react120.useEffect)(() => {
    resourceValue.current = resource;
  }, [resource]);
  (0, import_react120.useEffect)(() => {
    if (result.data === void 0 || result.error != null || result.isFetching)
      return;
    const allPagesDataLength = result.data.pages.reduce(
      (acc, page) => acc + page.data.length,
      0
    );
    if (allPagesDataLength <= MAX_DATA_LENGTH_TO_CACHE2) {
      result.data.pages.forEach((page) => {
        page.data.forEach((record) => {
          queryClient.setQueryData(
            [
              resourceValue.current,
              "getOne",
              { id: String(record.id), meta: metaValue.current }
            ],
            (oldRecord) => oldRecord ?? record
          );
        });
      });
    }
    onSuccessEvent(result.data);
  }, [
    onSuccessEvent,
    queryClient,
    result.data,
    result.error,
    result.isFetching
  ]);
  (0, import_react120.useEffect)(() => {
    if (result.error == null || result.isFetching) return;
    onErrorEvent(result.error);
  }, [onErrorEvent, result.error, result.isFetching]);
  (0, import_react120.useEffect)(() => {
    if (result.status === "pending" || result.isFetching) return;
    onSettledEvent(result.data, result.error);
  }, [
    onSettledEvent,
    result.data,
    result.error,
    result.status,
    result.isFetching
  ]);
  return result.data ? {
    ...result,
    data: result.data,
    total: ((_b = (_a = result.data) == null ? void 0 : _a.pages[0]) == null ? void 0 : _b.total) ?? void 0,
    meta: (_d = (_c = result.data) == null ? void 0 : _c.pages[0]) == null ? void 0 : _d.meta
  } : result;
};
var noop6 = () => void 0;

// node_modules/ra-core/src/dataProvider/undo/TakeUndoableMutationContext.tsx
var import_react121 = __toESM(require_react());
var TakeUndoableMutationContext = (0, import_react121.createContext)(() => {
});

// node_modules/ra-core/src/dataProvider/undo/UndoableMutationsContextProvider.tsx
var import_react122 = __toESM(require_react());
var import_jsx_runtime36 = __toESM(require_jsx_runtime());
var UndoableMutationsContextProvider = ({ children }) => {
  const [mutations, setMutations] = (0, import_react122.useState)([]);
  const addMutation = (0, import_react122.useCallback)((mutation) => {
    setMutations((mutations2) => [...mutations2, mutation]);
  }, []);
  const takeMutation = (0, import_react122.useCallback)(() => {
    if (mutations.length === 0) return;
    const [mutation, ...rest] = mutations;
    setMutations(rest);
    return mutation;
  }, [mutations]);
  return (0, import_jsx_runtime36.jsx)(TakeUndoableMutationContext.Provider, { value: takeMutation, children: (0, import_jsx_runtime36.jsx)(AddUndoableMutationContext.Provider, { value: addMutation, children }) });
};

// node_modules/ra-core/src/dataProvider/undo/useTakeUndoableMutation.tsx
var import_react123 = __toESM(require_react());
var useTakeUndoableMutation = () => (0, import_react123.useContext)(TakeUndoableMutationContext);

// node_modules/ra-core/src/preferences/PreferencesEditorContextProvider.tsx
var import_react125 = __toESM(require_react());

// node_modules/ra-core/src/preferences/PreferencesEditorContext.tsx
var import_react124 = __toESM(require_react());
var PreferencesEditorContext = (0, import_react124.createContext)(void 0);

// node_modules/ra-core/src/preferences/PreferencesEditorContextProvider.tsx
var import_jsx_runtime37 = __toESM(require_jsx_runtime());
var PreferencesEditorContextProvider = ({ children }) => {
  const [isEnabled, setIsEnabled] = (0, import_react125.useState)(false);
  const [editor, setEditor] = (0, import_react125.useState)(null);
  const [preferenceKey, setPreferenceKey] = (0, import_react125.useState)(null);
  const [path, setPath] = (0, import_react125.useState)(null);
  const [title, setTitleString] = (0, import_react125.useState)(null);
  const [titleOptions, setTitleOptions] = (0, import_react125.useState)();
  const enable = (0, import_react125.useCallback)(() => setIsEnabled(true), []);
  const disable = (0, import_react125.useCallback)(() => {
    setIsEnabled(false);
    setEditor(null);
  }, []);
  const setTitle = (0, import_react125.useCallback)((title2, titleOptions2) => {
    setTitleString(title2);
    setTitleOptions(titleOptions2);
  }, []);
  const context = (0, import_react125.useMemo)(() => {
    return {
      editor,
      setEditor,
      preferenceKey,
      setPreferenceKey,
      title,
      titleOptions,
      setTitle,
      isEnabled,
      disable,
      enable,
      path,
      setPath
    };
  }, [
    disable,
    enable,
    editor,
    preferenceKey,
    isEnabled,
    path,
    setPath,
    title,
    titleOptions,
    setTitle
  ]);
  return (0, import_jsx_runtime37.jsx)(PreferencesEditorContext.Provider, { value: context, children });
};

// node_modules/ra-core/src/core/CoreAdminContext.tsx
var import_jsx_runtime38 = __toESM(require_jsx_runtime());
var defaultStore2 = memoryStore();
var CoreAdminContext = (props) => {
  const {
    authProvider,
    basename,
    dataProvider = defaultDataProvider,
    i18nProvider,
    store = defaultStore2,
    children,
    queryClient
  } = props;
  if (!dataProvider) {
    throw new Error(`Missing dataProvider prop.
React-admin requires a valid dataProvider function to work.`);
  }
  const finalQueryClient = (0, import_react126.useMemo)(
    () => queryClient || new QueryClient(),
    [queryClient]
  );
  const finalAuthProvider = (0, import_react126.useMemo)(
    () => authProvider instanceof Function ? convertLegacyAuthProvider_default(authProvider) : authProvider,
    [authProvider]
  );
  const finalDataProvider = (0, import_react126.useMemo)(
    () => dataProvider instanceof Function ? convertLegacyDataProvider_default(dataProvider) : dataProvider,
    [dataProvider]
  );
  return (0, import_jsx_runtime38.jsx)(AuthContext.Provider, { value: finalAuthProvider, children: (0, import_jsx_runtime38.jsx)(DataProviderContext_default.Provider, { value: finalDataProvider, children: (0, import_jsx_runtime38.jsx)(StoreContextProvider, { value: store, children: (0, import_jsx_runtime38.jsx)(PreferencesEditorContextProvider, { children: (0, import_jsx_runtime38.jsx)(QueryClientProvider, { client: finalQueryClient, children: (0, import_jsx_runtime38.jsx)(AdminRouter, { basename, children: (0, import_jsx_runtime38.jsx)(I18nContextProvider, { value: i18nProvider, children: (0, import_jsx_runtime38.jsx)(NotificationContextProvider, { children: (0, import_jsx_runtime38.jsx)(UndoableMutationsContextProvider, { children: (0, import_jsx_runtime38.jsx)(ResourceDefinitionContextProvider, { children }) }) }) }) }) }) }) }) }) });
};

// node_modules/ra-core/src/core/CoreAdminUI.tsx
var import_react134 = __toESM(require_react());

// node_modules/ra-core/node_modules/react-error-boundary/dist/react-error-boundary.development.esm.js
var import_react127 = __toESM(require_react());
var ErrorBoundaryContext = (0, import_react127.createContext)(null);
var initialState = {
  didCatch: false,
  error: null
};
var ErrorBoundary = class extends import_react127.Component {
  constructor(props) {
    super(props);
    this.resetErrorBoundary = this.resetErrorBoundary.bind(this);
    this.state = initialState;
  }
  static getDerivedStateFromError(error) {
    return {
      didCatch: true,
      error
    };
  }
  resetErrorBoundary() {
    const {
      error
    } = this.state;
    if (error !== null) {
      var _this$props$onReset, _this$props;
      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
        args[_key] = arguments[_key];
      }
      (_this$props$onReset = (_this$props = this.props).onReset) === null || _this$props$onReset === void 0 ? void 0 : _this$props$onReset.call(_this$props, {
        args,
        reason: "imperative-api"
      });
      this.setState(initialState);
    }
  }
  componentDidCatch(error, info) {
    var _this$props$onError, _this$props2;
    (_this$props$onError = (_this$props2 = this.props).onError) === null || _this$props$onError === void 0 ? void 0 : _this$props$onError.call(_this$props2, error, info);
  }
  componentDidUpdate(prevProps, prevState) {
    const {
      didCatch
    } = this.state;
    const {
      resetKeys
    } = this.props;
    if (didCatch && prevState.error !== null && hasArrayChanged(prevProps.resetKeys, resetKeys)) {
      var _this$props$onReset2, _this$props3;
      (_this$props$onReset2 = (_this$props3 = this.props).onReset) === null || _this$props$onReset2 === void 0 ? void 0 : _this$props$onReset2.call(_this$props3, {
        next: resetKeys,
        prev: prevProps.resetKeys,
        reason: "keys"
      });
      this.setState(initialState);
    }
  }
  render() {
    const {
      children,
      fallbackRender,
      FallbackComponent,
      fallback
    } = this.props;
    const {
      didCatch,
      error
    } = this.state;
    let childToRender = children;
    if (didCatch) {
      const props = {
        error,
        resetErrorBoundary: this.resetErrorBoundary
      };
      if (typeof fallbackRender === "function") {
        childToRender = fallbackRender(props);
      } else if (FallbackComponent) {
        childToRender = (0, import_react127.createElement)(FallbackComponent, props);
      } else if (fallback !== void 0) {
        childToRender = fallback;
      } else {
        {
          console.error("react-error-boundary requires either a fallback, fallbackRender, or FallbackComponent prop");
        }
        throw error;
      }
    }
    return (0, import_react127.createElement)(ErrorBoundaryContext.Provider, {
      value: {
        didCatch,
        error,
        resetErrorBoundary: this.resetErrorBoundary
      }
    }, childToRender);
  }
};
function hasArrayChanged() {
  let a = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : [];
  let b = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [];
  return a.length !== b.length || a.some((item, index) => !Object.is(item, b[index]));
}

// node_modules/ra-core/src/core/CoreAdminRoutes.tsx
var import_react132 = __toESM(require_react());

// node_modules/ra-core/src/core/useConfigureAdminRouterFromChildren.tsx
var React4 = __toESM(require_react());
var import_react129 = __toESM(require_react());

// node_modules/ra-core/src/auth/usePermissions.ts
var import_react128 = __toESM(require_react());
var emptyParams = {};
var usePermissions = (params = emptyParams, queryParams = {
  staleTime: 5 * 60 * 1e3
}) => {
  const authProvider = useAuthProvider_default();
  const logoutIfAccessDenied = useLogoutIfAccessDenied_default();
  const { onSuccess, onError, onSettled, ...queryOptions } = queryParams ?? {};
  const queryResult = useQuery({
    queryKey: ["auth", "getPermissions", params],
    queryFn: async ({ signal }) => {
      if (!authProvider || !authProvider.getPermissions) {
        return [];
      }
      const permissions = await authProvider.getPermissions({
        ...params,
        signal
      });
      return permissions ?? null;
    },
    ...queryOptions
  });
  const onSuccessEvent = useEvent(onSuccess ?? noop7);
  const onSettledEvent = useEvent(onSettled ?? noop7);
  const onErrorEvent = useEvent(
    onError ?? ((error) => {
      if (true) {
        console.error(error);
      }
      logoutIfAccessDenied(error);
    })
  );
  (0, import_react128.useEffect)(() => {
    if (queryResult.data === void 0 || queryResult.isFetching) return;
    onSuccessEvent(queryResult.data);
  }, [onSuccessEvent, queryResult.data, queryResult.isFetching]);
  (0, import_react128.useEffect)(() => {
    if (queryResult.error == null || queryResult.isFetching) return;
    onErrorEvent(queryResult.error);
  }, [onErrorEvent, queryResult.error, queryResult.isFetching]);
  (0, import_react128.useEffect)(() => {
    if (queryResult.status === "pending" || queryResult.isFetching) return;
    onSettledEvent(queryResult.data, queryResult.error);
  }, [
    onSettledEvent,
    queryResult.data,
    queryResult.error,
    queryResult.status,
    queryResult.isFetching
  ]);
  const result = (0, import_react128.useMemo)(
    () => ({
      ...queryResult,
      permissions: queryResult.data
    }),
    [queryResult]
  );
  return !authProvider || !authProvider.getPermissions ? fakeQueryResult : result;
};
var usePermissions_default = usePermissions;
var noop7 = () => {
};
var fakeQueryResult = {
  permissions: void 0,
  data: void 0,
  dataUpdatedAt: 0,
  error: null,
  errorUpdatedAt: 0,
  errorUpdateCount: 0,
  failureCount: 0,
  failureReason: null,
  fetchStatus: "idle",
  isError: false,
  isInitialLoading: false,
  isLoading: false,
  isLoadingError: false,
  isFetched: true,
  isFetchedAfterMount: true,
  isFetching: false,
  isPaused: false,
  isPlaceholderData: false,
  isPending: false,
  isRefetchError: false,
  isRefetching: false,
  isStale: false,
  isSuccess: true,
  status: "success",
  refetch: () => Promise.resolve(fakeQueryResult)
};

// node_modules/ra-core/src/core/useConfigureAdminRouterFromChildren.tsx
var useConfigureAdminRouterFromChildren = (children) => {
  const { permissions, isPending } = usePermissions_default();
  const [routesAndResources, status] = useRoutesAndResourcesFromChildren(
    children,
    permissions,
    isPending
  );
  useRegisterResources(routesAndResources.resources, permissions);
  return {
    customRoutesWithLayout: routesAndResources.customRoutesWithLayout,
    customRoutesWithoutLayout: routesAndResources.customRoutesWithoutLayout,
    status,
    resources: routesAndResources.resources
  };
};
var useRoutesAndResourcesFromChildren = (children, permissions, isLoading) => {
  const doLogout = useLogout_default();
  const [routesAndResources, setRoutesAndResources, mergeRoutesAndResources] = useRoutesAndResourcesState(getRoutesAndResourceFromNodes(children));
  const [status, setStatus] = (0, import_react129.useState)(
    () => getStatus({
      children,
      ...routesAndResources
    })
  );
  if (!status) {
    throw new Error("Status should be defined");
  }
  (0, import_react129.useEffect)(() => {
    const resolveChildFunction = async (childFunc) => {
      try {
        const childrenFuncResult = childFunc(permissions);
        if (childrenFuncResult == null ? void 0 : childrenFuncResult.then) {
          childrenFuncResult.then(
            (resolvedChildren) => {
              mergeRoutesAndResources(
                getRoutesAndResourceFromNodes(resolvedChildren)
              );
              setStatus("ready");
            }
          );
        } else {
          mergeRoutesAndResources(
            getRoutesAndResourceFromNodes(
              childrenFuncResult
            )
          );
          setStatus("ready");
        }
      } catch (error) {
        console.error(error);
        doLogout();
      }
    };
    const updateFromChildren = async () => {
      const functionChild = getSingleChildFunction(children);
      const newRoutesAndResources = getRoutesAndResourceFromNodes(children);
      setRoutesAndResources(newRoutesAndResources);
      setStatus(
        !!functionChild ? "loading" : newRoutesAndResources.resources.length > 0 || newRoutesAndResources.customRoutesWithLayout.length > 0 || newRoutesAndResources.customRoutesWithoutLayout.length > 0 ? "ready" : "empty"
      );
      if (functionChild) {
        resolveChildFunction(functionChild);
      }
    };
    if (!isLoading) {
      updateFromChildren();
    }
  }, [
    children,
    doLogout,
    isLoading,
    mergeRoutesAndResources,
    permissions,
    setRoutesAndResources,
    setStatus
  ]);
  return [routesAndResources, status];
};
var useRoutesAndResourcesState = (initialState2) => {
  const [routesAndResources, setRoutesAndResources] = (0, import_react129.useState)(initialState2);
  const mergeRoutesAndResources = (0, import_react129.useCallback)(
    (newRoutesAndResources) => {
      setRoutesAndResources((previous) => ({
        customRoutesWithLayout: previous.customRoutesWithLayout.concat(
          newRoutesAndResources.customRoutesWithLayout
        ),
        customRoutesWithoutLayout: previous.customRoutesWithoutLayout.concat(
          newRoutesAndResources.customRoutesWithoutLayout
        ),
        resources: previous.resources.concat(
          newRoutesAndResources.resources
        )
      }));
    },
    []
  );
  return [routesAndResources, setRoutesAndResources, mergeRoutesAndResources];
};
var useRegisterResources = (resources, permissions) => {
  const { register, unregister } = useResourceDefinitionContext();
  (0, import_react129.useEffect)(() => {
    resources.forEach((resource) => {
      if (typeof resource.type.registerResource === "function") {
        const definition = resource.type.registerResource(resource.props, permissions);
        register(definition);
      } else {
        throw new Error(
          "When using a custom Resource element, it must have a static registerResource method accepting its props and returning a ResourceDefinition"
        );
      }
    });
    return () => {
      resources.forEach((resource) => {
        if (typeof resource.type.registerResource === "function") {
          const definition = resource.type.registerResource(resource.props, permissions);
          unregister(definition);
        } else {
          throw new Error(
            "When using a custom Resource element, it must have a static registerResource method accepting its props and returning a ResourceDefinition"
          );
        }
      });
    };
  }, [permissions, register, resources, unregister]);
};
var getStatus = ({
  children,
  resources,
  customRoutesWithLayout,
  customRoutesWithoutLayout
}) => {
  return getSingleChildFunction(children) ? "loading" : resources.length > 0 || customRoutesWithLayout.length > 0 || customRoutesWithoutLayout.length > 0 ? "ready" : "empty";
};
var getSingleChildFunction = (children) => {
  const childrenArray = Array.isArray(children) ? children : [children];
  const functionChildren = childrenArray.filter(
    (child) => typeof child === "function"
  );
  if (functionChildren.length > 1) {
    throw new Error(
      "You can only provide one function child to AdminRouter"
    );
  }
  if (functionChildren.length === 0) {
    return null;
  }
  return functionChildren[0];
};
var getRoutesAndResourceFromNodes = (children) => {
  const customRoutesWithLayout = [];
  const customRoutesWithoutLayout = [];
  const resources = [];
  if (typeof children === "function") {
    return {
      customRoutesWithLayout: [],
      customRoutesWithoutLayout: [],
      resources: []
    };
  }
  import_react129.Children.forEach(children, (element) => {
    if (!React4.isValidElement(element)) {
      return;
    }
    if (element.type === import_react129.Fragment) {
      const customRoutesFromFragment = getRoutesAndResourceFromNodes(
        element.props.children
      );
      customRoutesWithLayout.push(
        ...customRoutesFromFragment.customRoutesWithLayout
      );
      customRoutesWithoutLayout.push(
        ...customRoutesFromFragment.customRoutesWithoutLayout
      );
      resources.push(...customRoutesFromFragment.resources);
    }
    if (element.type.raName === "CustomRoutes") {
      const customRoutesElement = element;
      if (customRoutesElement.props.noLayout) {
        customRoutesWithoutLayout.push(
          customRoutesElement.props.children
        );
      } else {
        customRoutesWithLayout.push(customRoutesElement.props.children);
      }
    } else if (element.type.raName === "Resource") {
      resources.push(
        element
      );
    }
  });
  return {
    customRoutesWithLayout,
    customRoutesWithoutLayout,
    resources
  };
};

// node_modules/ra-core/src/core/HasDashboardContext.ts
var import_react130 = __toESM(require_react());
var HasDashboardContext = (0, import_react130.createContext)(false);
var HasDashboardContextProvider = HasDashboardContext.Provider;
var useHasDashboard = () => (0, import_react130.useContext)(HasDashboardContext);

// node_modules/ra-core/src/auth/useCanAccessResources.ts
var import_react131 = __toESM(require_react());
var useCanAccessResources = (params) => {
  const authProvider = useAuthProvider_default();
  const record = useRecordContext(params);
  const { action, resources, ...options } = params;
  const queryResult = useQuery({
    queryKey: ["auth", "canAccess", resources, action, record],
    queryFn: async ({ signal }) => {
      const queries = await Promise.all(
        resources.map(async (resource) => {
          if (!authProvider || !authProvider.canAccess) {
            return { canAccess: true, resource };
          }
          const canAccess = await authProvider.canAccess({
            resource,
            action,
            record,
            signal: authProvider.supportAbortSignal ? signal : void 0
          });
          return { canAccess, resource };
        })
      );
      const result2 = queries.reduce(
        (acc, { resource, canAccess }) => {
          acc[resource] = canAccess;
          return acc;
        },
        {}
      );
      return result2;
    },
    ...options
  });
  const result = (0, import_react131.useMemo)(() => {
    return {
      canAccess: queryResult.data,
      ...queryResult
    };
  }, [queryResult]);
  const resultWithoutAuthProvider = (0, import_react131.useMemo)(() => {
    return {
      canAccess: resources.reduce(
        (acc, resource) => {
          acc[resource] = true;
          return acc;
        },
        {}
      ),
      isPending: false,
      isError: false,
      error: null
    };
  }, [resources]);
  return !authProvider || !authProvider.canAccess ? resultWithoutAuthProvider : result;
};

// node_modules/ra-core/src/core/useFirstResourceWithListAccess.tsx
var useFirstResourceWithListAccess = () => {
  const { isPending: isPendingAuthenticated } = useAuthenticated();
  const resources = useResourceDefinitions();
  const resourcesNames = Object.keys(resources).filter(
    (resource) => resources[resource].hasList
  );
  const { canAccess, isPending } = useCanAccessResources({
    action: "list",
    resources: resourcesNames,
    enabled: !isPendingAuthenticated
  });
  const firstResourceWithListAccess = resourcesNames.find(
    (resource) => canAccess && canAccess[resource] === true
  );
  return { resource: firstResourceWithListAccess, isPending };
};

// node_modules/ra-core/src/core/NavigateToFirstResource.tsx
var import_jsx_runtime39 = __toESM(require_jsx_runtime());
var NavigateToFirstResource = ({
  loading: LoadingPage
}) => {
  const { resource, isPending } = useFirstResourceWithListAccess();
  const createPath = useCreatePath();
  if (isPending) {
    return (0, import_jsx_runtime39.jsx)(LoadingPage, {});
  }
  if (resource) {
    return (0, import_jsx_runtime39.jsx)(
      Navigate,
      {
        to: createPath({
          resource,
          type: "list"
        }),
        replace: true
      }
    );
  }
};

// node_modules/ra-core/src/core/CoreAdminRoutes.tsx
var import_jsx_runtime40 = __toESM(require_jsx_runtime());
var CoreAdminRoutes = (props) => {
  useScrollToTop();
  const {
    customRoutesWithLayout,
    customRoutesWithoutLayout,
    status,
    resources
  } = useConfigureAdminRouterFromChildren(props.children);
  const {
    layout: Layout,
    catchAll: CatchAll,
    dashboard,
    loading: LoadingPage,
    requireAuth,
    ready: Ready,
    authenticationError: AuthenticationError = Noop,
    accessDenied: AccessDenied = Noop
  } = props;
  const { authenticated, isPending: isPendingAuthenticated } = useAuthState_default(
    void 0,
    // do not log the user out on failure to allow access to custom routes with no layout
    false,
    { enabled: requireAuth }
  );
  if (status === "empty") {
    if (!Ready) {
      throw new Error(
        "The admin is empty. Please provide an empty component, or pass Resource or CustomRoutes as children."
      );
    }
    return (0, import_jsx_runtime40.jsx)(Ready, {});
  }
  if (status === "loading" || requireAuth && isPendingAuthenticated) {
    return (0, import_jsx_runtime40.jsxs)(Routes, { children: [
      customRoutesWithoutLayout,
      (0, import_jsx_runtime40.jsx)(
        Route,
        {
          path: "*",
          element: (0, import_jsx_runtime40.jsx)("div", { style: { height: "100vh" }, children: (0, import_jsx_runtime40.jsx)(LoadingPage, {}) })
        }
      )
    ] });
  }
  if (requireAuth && (isPendingAuthenticated || !authenticated)) {
    return (0, import_jsx_runtime40.jsxs)(Routes, { children: [
      customRoutesWithoutLayout,
      (0, import_jsx_runtime40.jsx)(Route, { path: "*", element: (0, import_jsx_runtime40.jsx)(LogoutOnMount, {}) })
    ] });
  }
  return (0, import_jsx_runtime40.jsxs)(Routes, { children: [
    customRoutesWithoutLayout,
    (0, import_jsx_runtime40.jsx)(
      Route,
      {
        path: "/*",
        element: (0, import_jsx_runtime40.jsx)(HasDashboardContextProvider, { value: !!dashboard, children: (0, import_jsx_runtime40.jsx)(Layout, { children: (0, import_jsx_runtime40.jsxs)(Routes, { children: [
          customRoutesWithLayout,
          import_react132.Children.map(resources, (resource) => (0, import_jsx_runtime40.jsx)(
            Route,
            {
              path: `${resource.props.name}/*`,
              element: resource
            },
            resource.props.name
          )),
          (0, import_jsx_runtime40.jsx)(
            Route,
            {
              path: "/",
              element: dashboard ? (0, import_jsx_runtime40.jsx)(
                WithPermissions_default,
                {
                  authParams: defaultAuthParams2,
                  component: dashboard,
                  loading: LoadingPage
                }
              ) : (0, import_jsx_runtime40.jsx)(
                NavigateToFirstResource,
                {
                  loading: LoadingPage
                }
              )
            }
          ),
          (0, import_jsx_runtime40.jsx)(
            Route,
            {
              path: "/authentication-error",
              element: (0, import_jsx_runtime40.jsx)(AuthenticationError, {})
            }
          ),
          (0, import_jsx_runtime40.jsx)(
            Route,
            {
              path: "/access-denied",
              element: (0, import_jsx_runtime40.jsx)(AccessDenied, {})
            }
          ),
          (0, import_jsx_runtime40.jsx)(Route, { path: "*", element: (0, import_jsx_runtime40.jsx)(CatchAll, {}) })
        ] }) }) })
      }
    )
  ] });
};
var defaultAuthParams2 = { params: { route: "dashboard" } };
var Noop = () => null;

// node_modules/ra-core/src/core/DefaultTitleContext.ts
var import_react133 = __toESM(require_react());
var DefaultTitleContext = (0, import_react133.createContext)("React Admin");
var DefaultTitleContextProvider = DefaultTitleContext.Provider;
var useDefaultTitle = () => (0, import_react133.useContext)(DefaultTitleContext);

// node_modules/ra-core/src/core/CoreAdminUI.tsx
var import_jsx_runtime41 = __toESM(require_jsx_runtime());
var DefaultLayout = ({ children }) => (0, import_jsx_runtime41.jsx)(import_jsx_runtime41.Fragment, { children });
var DefaultError = ({ error, errorInfo, resetErrorBoundary }) => {
  useResetErrorBoundaryOnLocationChange(resetErrorBoundary);
  return (0, import_jsx_runtime41.jsxs)("div", { children: [
    (0, import_jsx_runtime41.jsx)("h1", { children: "Error" }),
    (0, import_jsx_runtime41.jsxs)("pre", { children: [
      error.message,
      errorInfo == null ? void 0 : errorInfo.componentStack
    ] })
  ] });
};
var CoreAdminUI = (props) => {
  const [errorInfo, setErrorInfo] = (0, import_react134.useState)({});
  const {
    authCallbackPage: LoginCallbackPage = false,
    catchAll = Noop2,
    children,
    dashboard,
    disableTelemetry = false,
    error: ErrorComponent = DefaultError,
    layout = DefaultLayout,
    loading = Noop2,
    loginPage: LoginPage = false,
    ready = Ready_default,
    requireAuth = false,
    title = "React Admin",
    authenticationError = Noop2,
    accessDenied = Noop2
  } = props;
  (0, import_react134.useEffect)(() => {
    if (disableTelemetry || true) {
      return;
    }
    const img = new Image();
    img.src = `https://react-admin-telemetry.marmelab.com/react-admin-telemetry?domain=${window.location.hostname}`;
  }, [disableTelemetry]);
  const handleError = (error, info) => setErrorInfo(info);
  return (0, import_jsx_runtime41.jsx)(DefaultTitleContextProvider, { value: title, children: (0, import_jsx_runtime41.jsx)(
    ErrorBoundary,
    {
      onError: handleError,
      fallbackRender: ({ error, resetErrorBoundary }) => (0, import_jsx_runtime41.jsx)("div", { style: { minHeight: "100vh" }, children: (0, import_jsx_runtime41.jsx)(
        ErrorComponent,
        {
          error,
          errorInfo,
          resetErrorBoundary
        }
      ) }),
      children: (0, import_jsx_runtime41.jsxs)(Routes, { children: [
        LoginPage !== false && LoginPage !== true ? (0, import_jsx_runtime41.jsx)(
          Route,
          {
            path: "/login",
            element: createOrGetElement(LoginPage)
          }
        ) : null,
        LoginCallbackPage !== false && LoginCallbackPage !== true ? (0, import_jsx_runtime41.jsx)(
          Route,
          {
            path: "/auth-callback",
            element: createOrGetElement(LoginCallbackPage)
          }
        ) : null,
        (0, import_jsx_runtime41.jsx)(
          Route,
          {
            path: "/*",
            element: (0, import_jsx_runtime41.jsx)(
              CoreAdminRoutes,
              {
                catchAll,
                dashboard,
                layout,
                loading,
                requireAuth,
                ready,
                authenticationError,
                accessDenied,
                children
              }
            )
          }
        )
      ] })
    }
  ) });
};
var createOrGetElement = (el) => (0, import_react134.isValidElement)(el) ? el : (0, import_react134.createElement)(el);
var Noop2 = () => null;

// node_modules/ra-core/src/core/CoreAdmin.tsx
var import_jsx_runtime42 = __toESM(require_jsx_runtime());
var CoreAdmin = (props) => {
  const {
    accessDenied,
    authenticationError,
    authProvider,
    basename,
    catchAll,
    children,
    dashboard,
    dataProvider,
    disableTelemetry,
    error,
    i18nProvider,
    layout,
    loading,
    loginPage,
    queryClient,
    ready,
    requireAuth,
    store,
    title = "React Admin"
  } = props;
  return (0, import_jsx_runtime42.jsx)(
    CoreAdminContext,
    {
      authProvider,
      basename,
      dataProvider,
      i18nProvider,
      queryClient,
      store,
      children: (0, import_jsx_runtime42.jsx)(
        CoreAdminUI,
        {
          accessDenied,
          authenticationError,
          catchAll,
          dashboard,
          disableTelemetry,
          error,
          layout,
          loading,
          loginPage,
          ready,
          requireAuth,
          title,
          children
        }
      )
    }
  );
};

// node_modules/ra-core/src/core/CustomRoutes.tsx
var CustomRoutes = (_props) => {
  return null;
};
CustomRoutes.raName = "CustomRoutes";

// node_modules/ra-core/src/core/ResourceContextProvider.tsx
var import_jsx_runtime43 = __toESM(require_jsx_runtime());
var ResourceContextProvider = ({
  children,
  value
}) => value ? (0, import_jsx_runtime43.jsx)(ResourceContext.Provider, { value, children }) : children;

// node_modules/ra-core/src/core/OptionalResourceContextProvider.tsx
var import_jsx_runtime44 = __toESM(require_jsx_runtime());
var OptionalResourceContextProvider = ({
  value,
  children
}) => value ? (0, import_jsx_runtime44.jsx)(ResourceContextProvider, { value, children }) : children;

// node_modules/ra-core/src/core/Resource.tsx
var import_react135 = __toESM(require_react());
var import_react_is = __toESM(require_react_is());
var import_jsx_runtime45 = __toESM(require_jsx_runtime());
var Resource = (props) => {
  const { create, edit, list, name, show } = props;
  return (0, import_jsx_runtime45.jsx)(ResourceContextProvider, { value: name, children: (0, import_jsx_runtime45.jsxs)(Routes, { children: [
    create && (0, import_jsx_runtime45.jsx)(Route, { path: "create/*", element: getElement(create) }),
    show && (0, import_jsx_runtime45.jsx)(Route, { path: ":id/show/*", element: getElement(show) }),
    edit && (0, import_jsx_runtime45.jsx)(Route, { path: ":id/*", element: getElement(edit) }),
    list && (0, import_jsx_runtime45.jsx)(
      Route,
      {
        path: "/*",
        element: (0, import_jsx_runtime45.jsx)(
          RestoreScrollPosition,
          {
            storeKey: `${name}.list.scrollPosition`,
            children: getElement(list)
          }
        )
      }
    ),
    props.children
  ] }) });
};
var getElement = (ElementOrComponent) => {
  if ((0, import_react135.isValidElement)(ElementOrComponent)) {
    return ElementOrComponent;
  }
  if ((0, import_react_is.isValidElementType)(ElementOrComponent)) {
    const Element = ElementOrComponent;
    return (0, import_jsx_runtime45.jsx)(Element, {});
  }
  return null;
};
Resource.raName = "Resource";
Resource.registerResource = ({
  create,
  edit,
  icon,
  list,
  name,
  options,
  show,
  recordRepresentation,
  hasCreate,
  hasEdit,
  hasShow
}) => ({
  name,
  options,
  hasList: !!list,
  hasCreate: !!create || !!hasCreate,
  hasEdit: !!edit || !!hasEdit,
  hasShow: !!show || !!hasShow,
  icon,
  recordRepresentation
});

// node_modules/ra-core/src/core/SourceContext.tsx
var import_react136 = __toESM(require_react());
var SourceContext = (0, import_react136.createContext)(
  void 0
);
var defaultContextValue = {
  getSource: (source) => source,
  getLabel: (source) => source
};
var SourceContextProvider = SourceContext.Provider;
var useSourceContext = () => {
  const context = (0, import_react136.useContext)(SourceContext);
  if (!context) {
    return defaultContextValue;
  }
  return context;
};
var useOptionalSourceContext = () => (0, import_react136.useContext)(SourceContext);

// node_modules/ra-core/src/core/useGetResourceLabel.ts
var import_inflection = __toESM(require_inflection());
var useGetResourceLabel = () => {
  const translate = useTranslate();
  const definitions = useResourceDefinitions();
  return (resource, count = 2) => {
    const resourceDefinition = definitions[resource];
    const label = translate(`resources.${resource}.name`, {
      smart_count: count,
      _: resourceDefinition && resourceDefinition.options && resourceDefinition.options.label ? translate(resourceDefinition.options.label, {
        smart_count: count,
        _: resourceDefinition.options.label
      }) : (0, import_inflection.humanize)((0, import_inflection.inflect)(resource, count))
    });
    return label;
  };
};

// node_modules/ra-core/src/core/useGetRecordRepresentation.ts
var React5 = __toESM(require_react());
var import_react138 = __toESM(require_react());
var import_get14 = __toESM(require_get());

// node_modules/ra-core/src/core/useResourceDefinition.ts
var import_react137 = __toESM(require_react());
var import_defaults2 = __toESM(require_defaults());
var useResourceDefinition = (props) => {
  const resource = useResourceContext(props);
  const resourceDefinitions = useResourceDefinitions();
  const { hasCreate, hasEdit, hasList, hasShow, recordRepresentation } = props || {};
  const definition = (0, import_react137.useMemo)(() => {
    return (0, import_defaults2.default)(
      {},
      {
        hasCreate,
        hasEdit,
        hasList,
        hasShow,
        recordRepresentation
      },
      resource ? resourceDefinitions[resource] : {}
    );
  }, [
    resource,
    resourceDefinitions,
    hasCreate,
    hasEdit,
    hasList,
    hasShow,
    recordRepresentation
  ]);
  return definition;
};

// node_modules/ra-core/src/core/useGetRecordRepresentation.ts
var useGetRecordRepresentation = (resource) => {
  const { recordRepresentation } = useResourceDefinition({ resource });
  return (0, import_react138.useCallback)(
    (record) => {
      if (!record) return "";
      if (typeof recordRepresentation === "function") {
        return recordRepresentation(record);
      }
      if (typeof recordRepresentation === "string") {
        return (0, import_get14.default)(record, recordRepresentation);
      }
      if (React5.isValidElement(recordRepresentation)) {
        return recordRepresentation;
      }
      if ((record == null ? void 0 : record.name) != null && (record == null ? void 0 : record.name) !== "") {
        return record.name;
      }
      if ((record == null ? void 0 : record.title) != null && (record == null ? void 0 : record.title) !== "") {
        return record.title;
      }
      if ((record == null ? void 0 : record.label) != null && (record == null ? void 0 : record.label) !== "") {
        return record.label;
      }
      if ((record == null ? void 0 : record.reference) != null && (record == null ? void 0 : record.reference) !== "") {
        return record.reference;
      }
      return `#${record.id}`;
    },
    [recordRepresentation]
  );
};

// node_modules/ra-core/src/core/useWrappedSource.ts
var useWrappedSource = (source) => {
  const sourceContext = useSourceContext();
  return sourceContext.getSource(source);
};

// node_modules/ra-core/src/i18n/useTranslateLabel.ts
var useTranslateLabel = () => {
  const translate = useTranslate();
  const resourceFromContext = useResourceContext();
  const sourceContext = useOptionalSourceContext();
  return (0, import_react139.useCallback)(
    ({
      source,
      label,
      resource
    }) => {
      if (label === false || label === "") {
        return null;
      }
      if (label && typeof label !== "string") {
        return label;
      }
      return translate(
        ...getFieldLabelTranslationArgs({
          label,
          defaultLabel: source ? sourceContext == null ? void 0 : sourceContext.getLabel(source) : void 0,
          resource,
          resourceFromContext,
          source
        })
      );
    },
    [resourceFromContext, translate, sourceContext]
  );
};

// node_modules/ra-core/src/i18n/index.ts
var DEFAULT_LOCALE = "en";

// node_modules/ra-core/src/util/FieldTitle.tsx
var import_jsx_runtime46 = __toESM(require_jsx_runtime());
var FieldTitle = (props) => {
  const { source, label, resource, isRequired: isRequired2 } = props;
  const translateLabel = useTranslateLabel();
  if (label === true) {
    throw new Error(
      "Label parameter must be a string, a ReactElement or false"
    );
  }
  if (label === false || label === "") {
    return null;
  }
  if (label && typeof label !== "string") {
    return label;
  }
  return (0, import_jsx_runtime46.jsxs)("span", { children: [
    translateLabel({
      label,
      resource,
      source
    }),
    isRequired2 && (0, import_jsx_runtime46.jsx)("span", { "aria-hidden": "true", children: " *" })
  ] });
};
FieldTitle.displayName = "FieldTitle";
var FieldTitle_default = (0, import_react140.memo)(FieldTitle);

// node_modules/ra-core/src/util/Ready.tsx
var import_react141 = __toESM(require_react());
var import_jsx_runtime47 = __toESM(require_jsx_runtime());
var styles = {
  root: {
    width: "100vw",
    height: "100vh",
    display: "flex",
    flexDirection: "column",
    fontFamily: '"Roboto", sans-serif'
  },
  main: {
    flex: 1,
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    textAlign: "center",
    flexDirection: "column",
    background: "linear-gradient(135deg, #00023b 0%, #00023b 50%, #313264 100%)",
    color: "white",
    fontSize: "1.5em",
    fontWeight: "bold"
  },
  secondary: {
    height: "20vh",
    background: "#e8e8e8",
    color: "black",
    display: "flex",
    alignItems: "center",
    justifyContent: "space-evenly"
  },
  link: {
    textAlign: "center",
    width: 150,
    display: "block",
    textDecoration: "none",
    color: "black",
    opacity: 0.7
  },
  linkHovered: {
    opacity: 1
  },
  image: {
    width: 50
  },
  logo: {
    height: 100
  }
};
var Button = ({ img, label, href }) => {
  const [hovered, setHovered] = (0, import_react141.useState)(false);
  return (0, import_jsx_runtime47.jsx)("div", { children: (0, import_jsx_runtime47.jsxs)(
    "a",
    {
      href,
      style: hovered ? { ...styles.link, ...styles.linkHovered } : styles.link,
      onMouseEnter: () => setHovered(true),
      onMouseLeave: () => setHovered(false),
      children: [
        (0, import_jsx_runtime47.jsx)("img", { src: img, alt: label, style: styles.image }),
        (0, import_jsx_runtime47.jsx)("br", {}),
        label
      ]
    }
  ) });
};
var Ready_default = () => false ? (0, import_jsx_runtime47.jsx)("span", {}) : (0, import_jsx_runtime47.jsxs)("div", { style: styles.root, children: [
  (0, import_jsx_runtime47.jsxs)("div", { style: styles.main, children: [
    (0, import_jsx_runtime47.jsx)(
      "img",
      {
        style: styles.logo,
        src: "data:image/svg+xml;base64,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",
        alt: "react-admin logo"
      }
    ),
    (0, import_jsx_runtime47.jsx)("h1", { children: "Welcome to React-admin" }),
    (0, import_jsx_runtime47.jsxs)("div", { children: [
      "Your application is properly configured.",
      (0, import_jsx_runtime47.jsx)("br", {}),
      "Now you can add a <Resource> as child of <Admin>."
    ] })
  ] }),
  (0, import_jsx_runtime47.jsxs)("div", { style: styles.secondary, children: [
    (0, import_jsx_runtime47.jsx)(
      Button,
      {
        href: "https://marmelab.com/react-admin/documentation.html",
        img: "data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDIzLjAuMCwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgeD0iMHB4IiB5PSIwcHgiIHdpZHRoPSIyNHB4IgoJIGhlaWdodD0iMjRweCIgdmlld0JveD0iMCAwIDI0IDI0IiBlbmFibGUtYmFja2dyb3VuZD0ibmV3IDAgMCAyNCAyNCIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+CjxnIGlkPSJCb3VuZGluZ19Cb3giPgoJPHJlY3QgZmlsbD0ibm9uZSIgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0Ii8+CjwvZz4KPGcgaWQ9IkZsYXQiPgoJPGcgaWQ9InVpX3g1Rl9zcGVjX3g1Rl9oZWFkZXJfY29weV8yIj4KCTwvZz4KCTxnPgoJCTxwYXRoIGQ9Ik0yMSw1Yy0xLjExLTAuMzUtMi4zMy0wLjUtMy41LTAuNWMtMS45NSwwLTQuMDUsMC40LTUuNSwxLjVjLTEuNDUtMS4xLTMuNTUtMS41LTUuNS0xLjVTMi40NSw0LjksMSw2djE0LjY1CgkJCWMwLDAuMjUsMC4yNSwwLjUsMC41LDAuNWMwLjEsMCwwLjE1LTAuMDUsMC4yNS0wLjA1QzMuMSwyMC40NSw1LjA1LDIwLDYuNSwyMGMxLjk1LDAsNC4wNSwwLjQsNS41LDEuNWMxLjM1LTAuODUsMy44LTEuNSw1LjUtMS41CgkJCWMxLjY1LDAsMy4zNSwwLjMsNC43NSwxLjA1YzAuMSwwLjA1LDAuMTUsMC4wNSwwLjI1LDAuMDVjMC4yNSwwLDAuNS0wLjI1LDAuNS0wLjVWNkMyMi40LDUuNTUsMjEuNzUsNS4yNSwyMSw1eiBNMywxOC41VjcKCQkJYzEuMS0wLjM1LDIuMy0wLjUsMy41LTAuNWMxLjM0LDAsMy4xMywwLjQxLDQuNSwwLjk5djExLjVDOS42MywxOC40MSw3Ljg0LDE4LDYuNSwxOEM1LjMsMTgsNC4xLDE4LjE1LDMsMTguNXogTTIxLDE4LjUKCQkJYy0xLjEtMC4zNS0yLjMtMC41LTMuNS0wLjVjLTEuMzQsMC0zLjEzLDAuNDEtNC41LDAuOTlWNy40OWMxLjM3LTAuNTksMy4xNi0wLjk5LDQuNS0wLjk5YzEuMiwwLDIuNCwwLjE1LDMuNSwwLjVWMTguNXoiLz4KCQk8cGF0aCBvcGFjaXR5PSIwLjMiIGQ9Ik0xMSw3LjQ5QzkuNjMsNi45MSw3Ljg0LDYuNSw2LjUsNi41QzUuMyw2LjUsNC4xLDYuNjUsMyw3djExLjVDNC4xLDE4LjE1LDUuMywxOCw2LjUsMTgKCQkJYzEuMzQsMCwzLjEzLDAuNDEsNC41LDAuOTlWNy40OXoiLz4KCTwvZz4KCTxnPgoJCTxwYXRoIGQ9Ik0xNy41LDEwLjVjMC44OCwwLDEuNzMsMC4wOSwyLjUsMC4yNlY5LjI0QzE5LjIxLDkuMDksMTguMzYsOSwxNy41LDljLTEuMjgsMC0yLjQ2LDAuMTYtMy41LDAuNDd2MS41NwoJCQlDMTQuOTksMTAuNjksMTYuMTgsMTAuNSwxNy41LDEwLjV6Ii8+CgkJPHBhdGggZD0iTTE3LjUsMTMuMTZjMC44OCwwLDEuNzMsMC4wOSwyLjUsMC4yNlYxMS45Yy0wLjc5LTAuMTUtMS42NC0wLjI0LTIuNS0wLjI0Yy0xLjI4LDAtMi40NiwwLjE2LTMuNSwwLjQ3djEuNTcKCQkJQzE0Ljk5LDEzLjM2LDE2LjE4LDEzLjE2LDE3LjUsMTMuMTZ6Ii8+CgkJPHBhdGggZD0iTTE3LjUsMTUuODNjMC44OCwwLDEuNzMsMC4wOSwyLjUsMC4yNnYtMS41MmMtMC43OS0wLjE1LTEuNjQtMC4yNC0yLjUtMC4yNGMtMS4yOCwwLTIuNDYsMC4xNi0zLjUsMC40N3YxLjU3CgkJCUMxNC45OSwxNi4wMiwxNi4xOCwxNS44MywxNy41LDE1LjgzeiIvPgoJPC9nPgo8L2c+Cjwvc3ZnPgo=",
        label: "Documentation"
      }
    ),
    (0, import_jsx_runtime47.jsx)(
      Button,
      {
        href: "https://github.com/marmelab/react-admin/tree/master/examples",
        img: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBmaWxsPSJub25lIiBkPSJNMCAwaDI0djI0SDBWMHoiLz48cGF0aCBkPSJNOS40IDE2LjZMNC44IDEybDQuNi00LjZMOCA2bC02IDYgNiA2IDEuNC0xLjR6bTUuMiAwbDQuNi00LjYtNC42LTQuNkwxNiA2bDYgNi02IDYtMS40LTEuNHoiLz48L3N2Zz4=",
        label: "Examples"
      }
    ),
    (0, import_jsx_runtime47.jsx)(
      Button,
      {
        href: "https://stackoverflow.com/questions/tagged/react-admin",
        img: "data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDIzLjAuMSwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgeD0iMHB4IiB5PSIwcHgiIHdpZHRoPSIyNHB4IgoJIGhlaWdodD0iMjRweCIgdmlld0JveD0iMCAwIDI0IDI0IiBlbmFibGUtYmFja2dyb3VuZD0ibmV3IDAgMCAyNCAyNCIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+CjxnIGlkPSJCb3VuZGluZ19Cb3giPgoJPHJlY3QgZmlsbD0ibm9uZSIgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0Ii8+CjwvZz4KPGcgaWQ9IkZsYXQiPgoJPGcgaWQ9InVpX3g1Rl9zcGVjX3g1Rl9oZWFkZXJfY29weV8yIj4KCTwvZz4KCTxnPgoJCTxjaXJjbGUgb3BhY2l0eT0iMC4zIiBjeD0iOSIgY3k9IjgiIHI9IjIiLz4KCQk8cGF0aCBvcGFjaXR5PSIwLjMiIGQ9Ik05LDE1Yy0yLjcsMC01LjgsMS4yOS02LDIuMDFMMywxOGgxMnYtMUMxNC44LDE2LjI5LDExLjcsMTUsOSwxNXoiLz4KCQk8cGF0aCBkPSJNMTYuNjcsMTMuMTNDMTguMDQsMTQuMDYsMTksMTUuMzIsMTksMTd2M2g0di0zQzIzLDE0LjgyLDE5LjQzLDEzLjUzLDE2LjY3LDEzLjEzeiIvPgoJCTxwYXRoIGQ9Ik0xNSwxMmMyLjIxLDAsNC0xLjc5LDQtNGMwLTIuMjEtMS43OS00LTQtNGMtMC40NywwLTAuOTEsMC4xLTEuMzMsMC4yNEMxNC41LDUuMjcsMTUsNi41OCwxNSw4cy0wLjUsMi43My0xLjMzLDMuNzYKCQkJQzE0LjA5LDExLjksMTQuNTMsMTIsMTUsMTJ6Ii8+CgkJPHBhdGggZD0iTTksMTJjMi4yMSwwLDQtMS43OSw0LTRjMC0yLjIxLTEuNzktNC00LTRTNSw1Ljc5LDUsOEM1LDEwLjIxLDYuNzksMTIsOSwxMnogTTksNmMxLjEsMCwyLDAuOSwyLDJjMCwxLjEtMC45LDItMiwyCgkJCVM3LDkuMSw3LDhDNyw2LjksNy45LDYsOSw2eiIvPgoJCTxwYXRoIGQ9Ik05LDEzYy0yLjY3LDAtOCwxLjM0LTgsNHYzaDE2di0zQzE3LDE0LjM0LDExLjY3LDEzLDksMTN6IE0xNSwxOEgzbDAtMC45OUMzLjIsMTYuMjksNi4zLDE1LDksMTVzNS44LDEuMjksNiwyVjE4eiIvPgoJPC9nPgo8L2c+Cjwvc3ZnPgo=",
        label: "Community"
      }
    )
  ] })
] });

// node_modules/ra-core/src/util/warning.ts
var warning_default = (condition, message) => {
  if (condition && true) {
    console.warn(message);
  }
};

// node_modules/ra-core/src/util/useWhyDidYouUpdate.ts
var import_react142 = __toESM(require_react());
function useWhyDidYouUpdate(name, props) {
  const previousProps = (0, import_react142.useRef)();
  (0, import_react142.useEffect)(() => {
    if (previousProps.current) {
      const allKeys = Object.keys({ ...previousProps.current, ...props });
      const changesObj = {};
      allKeys.forEach((key) => {
        if (previousProps.current[key] !== props[key]) {
          changesObj[key] = {
            from: previousProps.current[key],
            to: props[key]
          };
        }
      });
      if (Object.keys(changesObj).length) {
        console.log("[why-did-you-update]", name, changesObj);
      }
    }
    previousProps.current = props;
  });
}

// node_modules/ra-core/src/util/getMutationMode.ts
var getMutationMode = (mutationMode, undoable) => {
  if (mutationMode) {
    return mutationMode;
  }
  switch (undoable) {
    case true:
      return "undoable";
    case false:
      return "pessimistic";
    default:
      return "undoable";
  }
};

// node_modules/ra-core/src/util/getFieldLabelTranslationArgs.ts
var import_inflection2 = __toESM(require_inflection());
var getFieldLabelTranslationArgs = (options) => {
  if (!options) return [""];
  const { label, defaultLabel, resource, resourceFromContext, source } = options;
  if (typeof label !== "undefined") return [label, { _: label }];
  if (typeof source === "undefined") return [""];
  const { sourceWithoutDigits, sourceSuffix } = getSourceParts(source);
  const defaultLabelTranslation = (0, import_inflection2.transform)(
    sourceSuffix.replace(/\./g, " "),
    ["underscore", "humanize"]
  );
  if (resource) {
    return [
      getResourceFieldLabelKey(resource, sourceWithoutDigits),
      { _: defaultLabelTranslation }
    ];
  }
  if (defaultLabel) {
    return [defaultLabel, { _: defaultLabelTranslation }];
  }
  return [
    getResourceFieldLabelKey(
      resourceFromContext || "undefined",
      sourceWithoutDigits
    ),
    { _: defaultLabelTranslation }
  ];
};
var getResourceFieldLabelKey = (resource, source) => `resources.${resource}.fields.${source}`;
var getSourceParts = (source) => {
  const sourceWithoutDigits = source.replace(/\.\d+\./g, ".");
  const parts = source.split(".");
  let lastPartWithDigits;
  parts.forEach((part, index) => {
    if (onlyDigits(part)) {
      lastPartWithDigits = index;
    }
  });
  const sourceSuffix = lastPartWithDigits != null ? parts.slice(lastPartWithDigits + 1).join(".") : source;
  return { sourceWithoutDigits, sourceSuffix };
};
var onlyDigits = (s) => {
  for (let i = s.length - 1; i >= 0; i--) {
    const d = s.charCodeAt(i);
    if (d < 48 || d > 57) return false;
  }
  return true;
};

// node_modules/ra-core/src/util/mergeRefs.ts
function mergeRefs(refs) {
  return (value) => {
    refs.forEach((ref) => {
      if (typeof ref === "function") {
        ref(value);
      } else if (ref != null) {
        ref.current = value;
      }
    });
  };
}

// node_modules/ra-core/src/util/useEvent.ts
var React6 = __toESM(require_react());
var import_react143 = __toESM(require_react());
var useLayoutEffect2 = typeof window !== "undefined" ? React6.useLayoutEffect : React6.useEffect;
var useEvent = (fn) => {
  const ref = React6.useRef(() => {
    throw new Error("Cannot call an event handler while rendering.");
  });
  useLayoutEffect2(() => {
    ref.current = fn;
  });
  return (0, import_react143.useCallback)((...args) => ref.current(...args), []);
};

// node_modules/ra-core/src/util/useDebouncedEvent.ts
var React7 = __toESM(require_react());
var import_react144 = __toESM(require_react());
var import_debounce6 = __toESM(require_debounce());
var useLayoutEffect4 = typeof window !== "undefined" ? React7.useLayoutEffect : React7.useEffect;
var useDebouncedEvent = (callback, delay) => {
  const debouncedCallbackRef = (0, import_react144.useRef)(
    () => {
      throw new Error("Cannot call an event handler while rendering.");
    }
  );
  const stableCallback = useEvent(callback);
  useLayoutEffect4(() => {
    debouncedCallbackRef.current = (0, import_debounce6.default)(stableCallback, delay);
  }, [stableCallback, delay]);
  return (0, import_react144.useCallback)(
    (...args) => debouncedCallbackRef.current(...args),
    []
  );
};

// node_modules/ra-core/src/util/useFieldValue.ts
var import_get15 = __toESM(require_get());
var useFieldValue = (params) => {
  const { defaultValue, source } = params;
  const record = useRecordContext(params);
  return (0, import_get15.default)(record, source, defaultValue);
};

// node_modules/ra-core/src/util/asyncDebounce.ts
var import_debounce7 = __toESM(require_debounce());
function asyncDebounce(func, wait) {
  const resolveSet = /* @__PURE__ */ new Set();
  const rejectSet = /* @__PURE__ */ new Set();
  const debounced = (0, import_debounce7.default)((args) => {
    func(...args).then((...res) => {
      resolveSet.forEach((resolve) => resolve(...res));
    }).catch((...res) => {
      rejectSet.forEach((reject) => reject(...res));
    }).finally(() => {
      resolveSet.clear();
      rejectSet.clear();
    });
  }, wait);
  return (...args) => new Promise((resolve, reject) => {
    resolveSet.add(resolve);
    rejectSet.add(reject);
    debounced(args);
  });
}

// node_modules/ra-core/src/util/useCheckForApplicationUpdate.ts
var import_react145 = __toESM(require_react());
var useCheckForApplicationUpdate = (options) => {
  const {
    url = window.location.href,
    fetchOptions,
    interval: delay = ONE_HOUR,
    onNewVersionAvailable: onNewVersionAvailableProp,
    disabled = true
  } = options;
  const currentHash = (0, import_react145.useRef)();
  const onNewVersionAvailable = useEvent(onNewVersionAvailableProp);
  (0, import_react145.useEffect)(() => {
    if (disabled) return;
    getHashForUrl(url, fetchOptions).then((hash2) => {
      if (hash2 != null) {
        currentHash.current = hash2;
      }
    });
  }, [disabled, url, JSON.stringify(fetchOptions)]);
  (0, import_react145.useEffect)(() => {
    if (disabled) return;
    const interval = setInterval(() => {
      getHashForUrl(url, fetchOptions).then((hash2) => {
        if (hash2 != null && currentHash.current !== hash2) {
          currentHash.current = hash2;
          onNewVersionAvailable();
        }
      }).catch(() => {
      });
    }, delay);
    return () => clearInterval(interval);
  }, [
    delay,
    onNewVersionAvailable,
    disabled,
    url,
    // eslint-disable-next-line react-hooks/exhaustive-deps
    JSON.stringify(fetchOptions)
  ]);
};
var getHashForUrl = async (url, fetchOptions) => {
  try {
    const response = await fetch(url, fetchOptions);
    if (!response.ok) return null;
    const text = await response.text();
    return hash(text);
  } catch (e) {
    return null;
  }
};
var hash = (value, seed = 0) => {
  let h1 = 3735928559 ^ seed, h2 = 1103547991 ^ seed;
  for (let i = 0, ch; i < value.length; i++) {
    ch = value.charCodeAt(i);
    h1 = Math.imul(h1 ^ ch, 2654435761);
    h2 = Math.imul(h2 ^ ch, 1597334677);
  }
  h1 = Math.imul(h1 ^ h1 >>> 16, 2246822507);
  h1 ^= Math.imul(h2 ^ h2 >>> 13, 3266489909);
  h2 = Math.imul(h2 ^ h2 >>> 16, 2246822507);
  h2 ^= Math.imul(h1 ^ h1 >>> 13, 3266489909);
  return 4294967296 * (2097151 & h2) + (h1 >>> 0);
};
var ONE_HOUR = 1e3 * 60 * 60;

// node_modules/ra-core/src/store/useStore.ts
var useStore = (key, defaultValue) => {
  const { getItem, setItem, subscribe } = useStoreContext();
  const [value, setValue] = (0, import_react146.useState)(() => getItem(key, defaultValue));
  (0, import_react146.useEffect)(() => {
    const storedValue = getItem(key, defaultValue);
    if (!(0, import_isEqual9.default)(value, storedValue)) {
      setValue(storedValue);
    }
    const unsubscribe = subscribe(key, (newValue) => {
      setValue(typeof newValue === "undefined" ? defaultValue : newValue);
    });
    return () => unsubscribe();
  }, [key, subscribe, defaultValue, getItem, value]);
  const set7 = useEvent((valueParam, runtimeDefaultValue) => {
    const newValue = typeof valueParam === "function" ? valueParam(value) : valueParam;
    setItem(
      key,
      typeof newValue === "undefined" ? typeof runtimeDefaultValue === "undefined" ? defaultValue : runtimeDefaultValue : newValue
    );
  });
  return [value, set7];
};

// node_modules/ra-core/src/store/useRemoveFromStore.ts
var import_react147 = __toESM(require_react());
var useRemoveFromStore = (hookTimeKey) => {
  const { removeItem } = useStoreContext();
  return (0, import_react147.useCallback)(
    (key) => {
      if (typeof key === "undefined" && typeof hookTimeKey === "undefined") {
        throw new Error(
          "You must provide a key to remove an item from the store"
        );
      }
      return removeItem(key ?? hookTimeKey);
    },
    [removeItem, hookTimeKey]
  );
};

// node_modules/ra-core/src/store/useRemoveItemsFromStore.ts
var import_react148 = __toESM(require_react());
var useRemoveItemsFromStore = (hookTimeKeyPrefix) => {
  const { removeItems } = useStoreContext();
  return (0, import_react148.useCallback)(
    (keyPrefix) => {
      if (typeof keyPrefix === "undefined" && typeof hookTimeKeyPrefix === "undefined") {
        throw new Error(
          "You must provide a key to remove an item from the store"
        );
      }
      return removeItems(keyPrefix ?? hookTimeKeyPrefix);
    },
    [removeItems, hookTimeKeyPrefix]
  );
};

// node_modules/ra-core/src/store/useResetStore.ts
var useResetStore = () => {
  const { reset } = useStoreContext();
  return reset;
};

// node_modules/ra-core/src/auth/useLogout.ts
var useLogout = () => {
  const authProvider = useAuthProvider_default();
  const queryClient = useQueryClient();
  const resetStore = useResetStore();
  const navigate = useNavigate();
  const navigateRef = (0, import_react149.useRef)(navigate);
  const location = useLocation();
  const locationRef = (0, import_react149.useRef)(location);
  const basename = useBasename();
  const loginUrl = removeDoubleSlashes(
    `${basename}/${defaultAuthParams.loginUrl}`
  );
  (0, import_react149.useEffect)(() => {
    locationRef.current = location;
    navigateRef.current = navigate;
  }, [location, navigate]);
  const logout = (0, import_react149.useCallback)(
    (params = {}, redirectTo = loginUrl, redirectToCurrentLocationAfterLogin = true) => {
      if (authProvider) {
        return authProvider.logout(params).then((redirectToFromProvider) => {
          if (redirectToFromProvider === false || redirectTo === false) {
            resetStore();
            queryClient.clear();
            return;
          }
          const finalRedirectTo = redirectToFromProvider || redirectTo;
          if (finalRedirectTo == null ? void 0 : finalRedirectTo.startsWith("http")) {
            resetStore();
            queryClient.clear();
            window.location.href = finalRedirectTo;
            return finalRedirectTo;
          }
          const redirectToParts = finalRedirectTo.split("?");
          const newLocation = {
            pathname: redirectToParts[0]
          };
          let newLocationOptions = {};
          if (redirectToCurrentLocationAfterLogin && locationRef.current && locationRef.current.pathname) {
            newLocationOptions = {
              state: {
                nextPathname: locationRef.current.pathname,
                nextSearch: locationRef.current.search
              }
            };
          }
          if (redirectToParts[1]) {
            newLocation.search = redirectToParts[1];
          }
          navigateRef.current(newLocation, newLocationOptions);
          resetStore();
          queryClient.clear();
          return redirectToFromProvider;
        });
      } else {
        navigateRef.current(
          {
            pathname: loginUrl
          },
          {
            state: {
              nextPathname: locationRef.current && locationRef.current.pathname
            }
          }
        );
        resetStore();
        queryClient.clear();
        return Promise.resolve();
      }
    },
    [authProvider, resetStore, loginUrl, queryClient]
  );
  return logout;
};
var useLogout_default = useLogout;

// node_modules/ra-core/src/auth/useAuthState.ts
var emptyParams2 = {};
var useAuthState = (params = emptyParams2, logoutOnFailure = false, queryOptions = emptyParams2) => {
  const authProvider = useAuthProvider_default();
  const logout = useLogout_default();
  const basename = useBasename();
  const notify = useNotify();
  const { onSuccess, onError, onSettled, ...options } = queryOptions;
  const queryResult = useQuery({
    queryKey: ["auth", "checkAuth", params],
    queryFn: ({ signal }) => {
      if (!authProvider) {
        return true;
      }
      return authProvider.checkAuth({ ...params, signal }).then(() => true).catch((error) => {
        if (error != null) {
          throw error;
        }
        throw new Error();
      });
    },
    retry: false,
    ...options
  });
  const onSuccessEvent = useEvent(onSuccess ?? noop8);
  const onSettledEvent = useEvent(onSettled ?? noop8);
  const onErrorEvent = useEvent(
    onError ?? ((error) => {
      if (!logoutOnFailure) return;
      const loginUrl = removeDoubleSlashes(
        `${basename}/${defaultAuthParams.loginUrl}`
      );
      logout(
        {},
        error && error.redirectTo != null ? error.redirectTo : loginUrl
      );
      const shouldSkipNotify = error && error.message === false;
      !shouldSkipNotify && notify(getErrorMessage2(error, "ra.auth.auth_check_error"), {
        type: "error"
      });
    })
  );
  (0, import_react150.useEffect)(() => {
    if (queryResult.data === void 0 || queryResult.isFetching) return;
    if (queryOptions.enabled === false) return;
    onSuccessEvent(queryResult.data);
  }, [
    onSuccessEvent,
    queryResult.data,
    queryResult.isFetching,
    queryOptions.enabled
  ]);
  (0, import_react150.useEffect)(() => {
    if (queryResult.error == null || queryResult.isFetching) return;
    if (queryOptions.enabled === false) return;
    onErrorEvent(queryResult.error);
  }, [
    onErrorEvent,
    queryResult.error,
    queryResult.isFetching,
    queryOptions.enabled
  ]);
  (0, import_react150.useEffect)(() => {
    if (queryResult.status === "pending" || queryResult.isFetching) return;
    if (queryOptions.enabled === false) return;
    onSettledEvent(queryResult.data, queryResult.error);
  }, [
    onSettledEvent,
    queryResult.data,
    queryResult.error,
    queryResult.status,
    queryResult.isFetching,
    queryOptions.enabled
  ]);
  const result = (0, import_react150.useMemo)(() => {
    return {
      ...queryResult,
      authenticated: queryResult.error ? false : queryResult.data
    };
  }, [queryResult]);
  return authProvider != null ? result : noAuthProviderQueryResult;
};
var useAuthState_default = useAuthState;
var getErrorMessage2 = (error, defaultMessage) => typeof error === "string" ? error : typeof error === "undefined" || !error.message ? defaultMessage : error.message;
var noop8 = () => {
};
var noAuthProviderQueryResult = {
  authenticated: true,
  data: true,
  dataUpdatedAt: 0,
  error: null,
  errorUpdatedAt: 0,
  errorUpdateCount: 0,
  failureCount: 0,
  failureReason: null,
  fetchStatus: "idle",
  isError: false,
  isInitialLoading: false,
  isLoading: false,
  isLoadingError: false,
  isFetched: true,
  isFetchedAfterMount: true,
  isFetching: false,
  isPaused: false,
  isPlaceholderData: false,
  isPending: false,
  isRefetchError: false,
  isRefetching: false,
  isStale: false,
  isSuccess: true,
  status: "success",
  refetch: () => Promise.resolve(noAuthProviderQueryResult)
};

// node_modules/ra-core/src/auth/WithPermissions.tsx
var import_react151 = __toESM(require_react());

// node_modules/ra-core/src/auth/useAuthenticated.ts
var useAuthenticated = ({
  params,
  logoutOnFailure = true,
  ...options
} = {}) => {
  return useAuthState_default(params ?? emptyParams3, logoutOnFailure, options);
};
var emptyParams3 = {};

// node_modules/ra-core/src/auth/WithPermissions.tsx
var import_jsx_runtime48 = __toESM(require_jsx_runtime());
var isEmptyChildren = (children) => import_react151.Children.count(children) === 0;
var WithPermissions = (props) => {
  const {
    authParams,
    children,
    render,
    component,
    loading: Loading = null,
    staticContext,
    ...rest
  } = props;
  warning_default(
    render && children && !isEmptyChildren(children) || render && component || component && children && !isEmptyChildren(children),
    "You should only use one of the `component`, `render` and `children` props in <WithPermissions>"
  );
  const { isPending: isAuthenticationPending } = useAuthenticated(authParams);
  const { permissions, isPending: isPendingPermissions } = usePermissions_default(
    authParams,
    {
      enabled: !isAuthenticationPending
    }
  );
  if (isAuthenticationPending || isPendingPermissions) {
    return Loading ? (0, import_jsx_runtime48.jsx)(Loading, {}) : null;
  }
  if (component) {
    return (0, import_react151.createElement)(component, { permissions, ...rest });
  }
  if (render) {
    return render({ permissions, ...rest });
  }
  if (children) {
    return children({ permissions, ...rest });
  }
};
var WithPermissions_default = WithPermissions;

// node_modules/ra-core/src/auth/useLogin.ts
var import_react152 = __toESM(require_react());
var useLogin = () => {
  const authProvider = useAuthProvider_default();
  const queryClient = useQueryClient();
  const location = useLocation();
  const locationState = location.state;
  const navigate = useNavigate();
  const basename = useBasename();
  const { resetNotifications } = useNotificationContext();
  const nextPathName = locationState && locationState.nextPathname;
  const nextSearch = locationState && locationState.nextSearch;
  const afterLoginUrl = removeDoubleSlashes(
    `${basename}/${defaultAuthParams.afterLoginUrl}`
  );
  const login = (0, import_react152.useCallback)(
    (params = {}, pathName) => {
      if (authProvider) {
        return authProvider.login(params).then((ret) => {
          resetNotifications();
          queryClient.invalidateQueries({
            queryKey: ["auth", "getPermissions"]
          });
          if (ret && ret.hasOwnProperty("redirectTo")) {
            if (ret) {
              navigate(ret.redirectTo);
            }
          } else {
            const redirectUrl = pathName ? pathName : nextPathName + nextSearch || afterLoginUrl;
            navigate(redirectUrl);
          }
          return ret;
        });
      } else {
        resetNotifications();
        navigate(afterLoginUrl);
        return Promise.resolve();
      }
    },
    [
      authProvider,
      queryClient,
      navigate,
      nextPathName,
      nextSearch,
      resetNotifications,
      afterLoginUrl
    ]
  );
  return login;
};
var useLogin_default = useLogin;

// node_modules/ra-core/src/auth/useGetPermissions.ts
var import_react153 = __toESM(require_react());
var useGetPermissions = () => {
  const authProvider = useAuthProvider_default();
  const getPermissions = (0, import_react153.useCallback)(
    (params = {}) => {
      if (authProvider && authProvider.getPermissions) {
        return authProvider.getPermissions(params).then((result) => result ?? null);
      }
      return Promise.resolve([]);
    },
    [authProvider]
  );
  return getPermissions;
};
var useGetPermissions_default = useGetPermissions;

// node_modules/ra-core/src/auth/types.ts
var AUTH_LOGIN = "AUTH_LOGIN";
var AUTH_CHECK = "AUTH_CHECK";
var AUTH_ERROR = "AUTH_ERROR";
var AUTH_LOGOUT = "AUTH_LOGOUT";
var AUTH_GET_PERMISSIONS = "AUTH_GET_PERMISSIONS";

// node_modules/ra-core/src/auth/convertLegacyAuthProvider.ts
var convertLegacyAuthProvider_default = (legacyAuthProvider) => {
  const authProvider = (...args) => legacyAuthProvider.apply(null, args);
  authProvider.login = (params) => legacyAuthProvider(AUTH_LOGIN, params);
  authProvider.logout = (params) => legacyAuthProvider(AUTH_LOGOUT, params);
  authProvider.checkAuth = (params) => legacyAuthProvider(AUTH_CHECK, params);
  authProvider.checkError = (error) => legacyAuthProvider(AUTH_ERROR, error);
  authProvider.getPermissions = (params) => legacyAuthProvider(AUTH_GET_PERMISSIONS, params);
  return authProvider;
};

// node_modules/ra-core/src/auth/Authenticated.tsx
var import_jsx_runtime49 = __toESM(require_jsx_runtime());
var Authenticated = (props) => {
  const { authParams, loading = null, children } = props;
  const { isPending, isError } = useAuthenticated({ params: authParams });
  if (isPending || isError) {
    return loading;
  }
  return (0, import_jsx_runtime49.jsx)(import_jsx_runtime49.Fragment, { children });
};

// node_modules/ra-core/src/auth/useCanAccess.ts
var import_react154 = __toESM(require_react());
var useCanAccess = (params) => {
  const authProvider = useAuthProvider_default();
  const resource = useResourceContext(params);
  if (!resource) {
    throw new Error(
      "useCanAccess must be used inside a <Resource> component or provide a resource prop"
    );
  }
  const record = useRecordContext(params);
  const authProviderHasCanAccess = !!(authProvider == null ? void 0 : authProvider.canAccess);
  const queryResult = useQuery({
    queryKey: ["auth", "canAccess", { ...params, record, resource }],
    queryFn: async ({ signal }) => {
      if (!authProvider || !authProvider.canAccess) {
        return true;
      }
      return authProvider.canAccess({
        ...params,
        record,
        resource,
        signal: authProvider.supportAbortSignal ? signal : void 0
      });
    },
    enabled: authProviderHasCanAccess,
    ...params
  });
  const result = (0, import_react154.useMemo)(() => {
    return {
      ...queryResult,
      canAccess: queryResult.data
    };
  }, [queryResult]);
  return authProviderHasCanAccess ? result : emptyQueryObserverResult;
};
var emptyQueryObserverResult = {
  canAccess: true,
  data: true,
  dataUpdatedAt: 0,
  error: null,
  errorUpdatedAt: 0,
  errorUpdateCount: 0,
  failureCount: 0,
  failureReason: null,
  fetchStatus: "idle",
  isError: false,
  isInitialLoading: false,
  isLoading: false,
  isLoadingError: false,
  isFetched: true,
  isFetchedAfterMount: true,
  isFetching: false,
  isPaused: false,
  isPlaceholderData: false,
  isPending: false,
  isRefetchError: false,
  isRefetching: false,
  isStale: false,
  isSuccess: true,
  status: "success",
  refetch: () => Promise.resolve(emptyQueryObserverResult)
};

// node_modules/ra-core/src/auth/CanAccess.tsx
var import_jsx_runtime50 = __toESM(require_jsx_runtime());
var CanAccess = ({
  children,
  loading = null,
  accessDenied = null,
  error: errorElement = DEFAULT_ERROR,
  ...props
}) => {
  const { canAccess, error, isPending } = useCanAccess(props);
  if (isPending) {
    return loading;
  }
  if (error) {
    return errorElement;
  }
  if (canAccess === false) {
    return accessDenied;
  }
  return children;
};
var CanAccessDefaultError = () => {
  const basename = useBasename();
  return (0, import_jsx_runtime50.jsx)(Navigate, { to: `${basename}/authentication-error` });
};
var DEFAULT_ERROR = (0, import_jsx_runtime50.jsx)(CanAccessDefaultError, {});

// node_modules/ra-core/src/auth/LogoutOnMount.ts
var import_react155 = __toESM(require_react());
var LogoutOnMount = () => {
  const logout = useLogout_default();
  (0, import_react155.useEffect)(() => {
    logout();
  }, [logout]);
  return null;
};

// node_modules/ra-core/src/auth/useCheckAuth.ts
var import_react156 = __toESM(require_react());
var useCheckAuth = () => {
  const authProvider = useAuthProvider_default();
  const notify = useNotify();
  const logout = useLogout_default();
  const basename = useBasename();
  const loginUrl = removeDoubleSlashes(
    `${basename}/${defaultAuthParams.loginUrl}`
  );
  const checkAuth = (0, import_react156.useCallback)(
    (params = {}, logoutOnFailure = true, redirectTo = loginUrl) => authProvider ? authProvider.checkAuth(params).catch((error) => {
      if (logoutOnFailure) {
        logout(
          {},
          error && error.redirectTo != null ? error.redirectTo : redirectTo
        );
        const shouldSkipNotify = error && error.message === false;
        !shouldSkipNotify && notify(
          getErrorMessage3(
            error,
            "ra.auth.auth_check_error"
          ),
          { type: "error" }
        );
      }
      throw error;
    }) : checkAuthWithoutAuthProvider(),
    [authProvider, logout, notify, loginUrl]
  );
  return checkAuth;
};
var checkAuthWithoutAuthProvider = () => Promise.resolve();
var getErrorMessage3 = (error, defaultMessage) => typeof error === "string" ? error : typeof error === "undefined" || !error.message ? defaultMessage : error.message;

// node_modules/ra-core/src/auth/useGetIdentity.ts
var import_react157 = __toESM(require_react());
var defaultIdentity = {
  id: ""
};
var defaultQueryParams = {
  staleTime: 5 * 60 * 1e3
};
var useGetIdentity = (options = defaultQueryParams) => {
  const authProvider = useAuthProvider_default();
  const { onSuccess, onError, onSettled, ...queryOptions } = options;
  const result = useQuery({
    queryKey: ["auth", "getIdentity"],
    queryFn: async ({ signal }) => {
      if (authProvider && typeof authProvider.getIdentity === "function") {
        return authProvider.getIdentity({ signal });
      } else {
        return defaultIdentity;
      }
    },
    ...queryOptions
  });
  const onSuccessEvent = useEvent(onSuccess ?? noop9);
  const onErrorEvent = useEvent(onError ?? noop9);
  const onSettledEvent = useEvent(onSettled ?? noop9);
  (0, import_react157.useEffect)(() => {
    if (result.data === void 0 || result.isFetching) return;
    onSuccessEvent(result.data);
  }, [onSuccessEvent, result.data, result.isFetching]);
  (0, import_react157.useEffect)(() => {
    if (result.error == null || result.isFetching) return;
    onErrorEvent(result.error);
  }, [onErrorEvent, result.error, result.isFetching]);
  (0, import_react157.useEffect)(() => {
    if (result.status === "pending" || result.isFetching) return;
    onSettledEvent(result.data, result.error);
  }, [
    onSettledEvent,
    result.data,
    result.error,
    result.status,
    result.isFetching
  ]);
  return (0, import_react157.useMemo)(
    () => ({
      ...result,
      identity: result.data
    }),
    [result]
  );
};
var noop9 = () => {
};

// node_modules/ra-core/src/auth/useHandleAuthCallback.ts
var import_react158 = __toESM(require_react());
var useHandleAuthCallback = (options) => {
  const authProvider = useAuthProvider_default();
  const redirect = useRedirect();
  const location = useLocation();
  const locationState = location.state;
  const nextPathName = locationState && locationState.nextPathname;
  const nextSearch = locationState && locationState.nextSearch;
  const defaultRedirectUrl = nextPathName ? nextPathName + nextSearch : "/";
  const { onSuccess, onError, onSettled, ...queryOptions } = options ?? {};
  let handleCallbackPromise;
  const queryResult = useQuery({
    queryKey: ["auth", "handleCallback"],
    queryFn: ({ signal }) => {
      if (!handleCallbackPromise) {
        handleCallbackPromise = authProvider && typeof authProvider.handleCallback === "function" ? authProvider.handleCallback({ signal }).then((result) => result ?? null) : Promise.resolve();
      }
      return handleCallbackPromise;
    },
    retry: false,
    ...queryOptions
  });
  const onSuccessEvent = useEvent(
    onSuccess ?? ((data) => {
      const previousLocation = localStorage.getItem(
        PreviousLocationStorageKey
      );
      const redirectTo = (data == null ? void 0 : data.redirectTo) ?? previousLocation;
      if (redirectTo === false) {
        return;
      }
      redirect(redirectTo ?? defaultRedirectUrl);
    })
  );
  const onErrorEvent = useEvent(onError ?? noop10);
  const onSettledEvent = useEvent(onSettled ?? noop10);
  (0, import_react158.useEffect)(() => {
    if (queryResult.error == null || queryResult.isFetching) return;
    onErrorEvent(queryResult.error);
  }, [onErrorEvent, queryResult.error, queryResult.isFetching]);
  (0, import_react158.useEffect)(() => {
    if (queryResult.data === void 0 || queryResult.isFetching) return;
    onSuccessEvent(queryResult.data);
  }, [onSuccessEvent, queryResult.data, queryResult.isFetching]);
  (0, import_react158.useEffect)(() => {
    if (queryResult.status === "pending" || queryResult.isFetching) return;
    onSettledEvent(queryResult.data, queryResult.error);
  }, [
    onSettledEvent,
    queryResult.data,
    queryResult.error,
    queryResult.status,
    queryResult.isFetching
  ]);
  return queryResult;
};
var PreviousLocationStorageKey = "@react-admin/nextPathname";
var noop10 = () => {
};

// node_modules/ra-core/src/auth/useIsAuthPending.ts
var useIsAuthPending = (params) => {
  const { action, ...props } = params;
  const queryClient = useQueryClient();
  const authProvider = useAuthProvider_default();
  const resource = useResourceContext(props);
  if (!authProvider) {
    return false;
  }
  const authQueryState = queryClient.getQueryState(["auth", "checkAuth", {}]);
  const canAccessQueryState = queryClient.getQueryState([
    "auth",
    "canAccess",
    { action, resource }
  ]);
  if ((authQueryState == null ? void 0 : authQueryState.status) === "pending" || authProvider.canAccess && (canAccessQueryState == null ? void 0 : canAccessQueryState.status) === "pending") {
    return true;
  }
  return false;
};

// node_modules/ra-core/src/auth/useRequireAccess.tsx
var import_react159 = __toESM(require_react());
var useRequireAccess = (params) => {
  const { canAccess, data, error, ...rest } = useCanAccess(params);
  const navigate = useNavigate();
  const basename = useBasename();
  (0, import_react159.useEffect)(() => {
    if (rest.isPending) return;
    if (canAccess === false) {
      navigate(`${basename}/access-denied`);
    }
  }, [basename, canAccess, navigate, rest.isPending]);
  (0, import_react159.useEffect)(() => {
    if (error) {
      navigate(`${basename}/authentication-error`);
    }
  }, [basename, navigate, error]);
  return rest;
};

// node_modules/ra-core/src/auth/addRefreshAuthToAuthProvider.ts
var addRefreshAuthToAuthProvider = (provider, refreshAuth) => {
  const proxy = new Proxy(provider, {
    get(_, name) {
      const shouldIntercept = AuthProviderInterceptedMethods.includes(name.toString()) && provider[name.toString()] != null;
      if (shouldIntercept) {
        return async (...args) => {
          await refreshAuth();
          return provider[name.toString()](...args);
        };
      }
      return provider[name.toString()];
    }
  });
  return proxy;
};
var AuthProviderInterceptedMethods = [
  "checkAuth",
  "getIdentity",
  "getPermissions"
];

// node_modules/ra-core/src/auth/addRefreshAuthToDataProvider.ts
var addRefreshAuthToDataProvider = (provider, refreshAuth) => {
  const proxy = new Proxy(provider, {
    get(_, name) {
      return async (...args) => {
        await refreshAuth();
        return provider[name.toString()](...args);
      };
    }
  });
  return proxy;
};

// node_modules/ra-core/src/preferences/PreferenceKeyContext.tsx
var import_react160 = __toESM(require_react());
var import_jsx_runtime51 = __toESM(require_jsx_runtime());
var PreferenceKeyContext = (0, import_react160.createContext)("");
var PreferenceKeyContextProvider = ({
  value = "",
  children
}) => (0, import_jsx_runtime51.jsx)(PreferenceKeyContext.Provider, { value, children });
var usePreferenceKey = () => {
  return (0, import_react160.useContext)(PreferenceKeyContext);
};

// node_modules/ra-core/src/preferences/usePreference.ts
var usePreference = (key, defaultValue) => {
  const preferenceKey = usePreferenceKey();
  if (!preferenceKey) {
    throw new Error(
      "usePreference cannot be used outside of a Configurable component. Did you forget to wrap your component with <Configurable>? If you don't want to use Configurable, you can use the useStore hook instead."
    );
  }
  return useStore(
    preferenceKey && key ? `${preferenceKey}.${key}` : preferenceKey ?? key,
    defaultValue
  );
};

// node_modules/ra-core/src/preferences/usePreferencesEditor.ts
var import_react161 = __toESM(require_react());
var usePreferencesEditor = () => {
  const context = (0, import_react161.useContext)(PreferencesEditorContext);
  if (!context) {
    throw new Error(
      "usePreferencesEditor must be used within a PreferencesEditorContextProvider"
    );
  }
  return context;
};

// node_modules/ra-core/src/preferences/usePreferenceInput.ts
var import_react162 = __toESM(require_react());
var usePreferenceInput = (key, defaultValue) => {
  const [valueFromStore, setValueFromStore] = usePreference(
    key,
    defaultValue
  );
  const [value, setValue] = (0, import_react162.useState)(valueFromStore);
  (0, import_react162.useEffect)(() => {
    setValue(valueFromStore || defaultValue);
  }, [valueFromStore, defaultValue]);
  const onChange = (event) => {
    setValue(event.target.value === "" ? defaultValue : event.target.value);
  };
  const onBlur = () => {
    setValueFromStore(value);
  };
  const onKeyDown = (event) => {
    var _a;
    if (event.key === "Enter") {
      setValueFromStore(value);
      const form = event.target.form;
      if (form) {
        const index = [...form].indexOf(event.target);
        (_a = form.elements[index + 1]) == null ? void 0 : _a.focus();
      }
      event.preventDefault();
    }
  };
  return { value, onChange, onBlur, onKeyDown };
};

// node_modules/ra-core/src/preferences/useSetInspectorTitle.ts
var import_react163 = __toESM(require_react());
var useSetInspectorTitle = (title, options) => {
  const preferencesEditorContext = usePreferencesEditor();
  if (!preferencesEditorContext) {
    throw new Error(
      "useSetInspectorTitle cannot be called outside of a PreferencesEditorContext"
    );
  }
  const { setTitle } = preferencesEditorContext;
  (0, import_react163.useEffect)(() => {
    setTitle(title, options);
  }, [title, JSON.stringify(options), setTitle]);
};

// node_modules/ra-core/src/inference/getValuesFromRecords.ts
var getValuesFromRecords_default = (records) => records.reduce((values, record) => {
  Object.keys(record).forEach((fieldName) => {
    if (!values[fieldName]) {
      values[fieldName] = [];
    }
    if (record[fieldName] != null) {
      const value = Array.isArray(record[fieldName]) ? [record[fieldName]] : record[fieldName];
      values[fieldName] = values[fieldName].concat(value);
    }
  });
  return values;
}, {});

// node_modules/ra-core/src/inference/InferredElement.ts
var import_react164 = __toESM(require_react());
var InferredElement = class {
  constructor(type, props, children) {
    this.type = type;
    this.props = props;
    this.children = children;
    this.type = type;
    this.props = props;
    this.children = children;
  }
  getElement(props = {}) {
    if (!this.type || !this.type.component) {
      return;
    }
    return this.children ? (0, import_react164.createElement)(
      this.type.component,
      { ...this.props, ...props },
      this.children.length > 0 ? this.children.map(
        (child, index) => child.getElement({ key: index })
      ) : this.children.getElement()
    ) : (0, import_react164.createElement)(this.type.component, { ...this.props, ...props });
  }
  getProps() {
    return this.props;
  }
  isDefined() {
    return !!this.type;
  }
  getRepresentation() {
    if (!this.type) {
      return "";
    }
    if (this.type.representation) {
      return this.type.representation(this.props, this.children);
    }
    if (this.type.component) {
      return `<${this.type.component.displayName || this.type.component.name} source="${this.props.source}" />`;
    }
    return "";
  }
};
var InferredElement_default = InferredElement;

// node_modules/ra-core/src/inference/assertions.ts
var isNumeric = (value) => !isNaN(parseFloat(value)) && isFinite(value);
var valuesAreNumeric = (values) => values.every(isNumeric);
var isInteger = (value) => Number.isInteger(value) || !isNaN(parseInt(value));
var valuesAreInteger = (values) => values.every(isInteger);
var isBoolean = (value) => typeof value === "boolean";
var valuesAreBoolean = (values) => values.every(isBoolean);
var isString = (value) => typeof value === "string";
var valuesAreString = (values) => values.every(isString);
var HtmlRegexp = /<([A-Z][A-Z0-9]*)\b[^>]*>(.*?)<\/\1>/i;
var isHtml = (value) => !value || HtmlRegexp.test(value);
var valuesAreHtml = (values) => values.every(isHtml);
var UrlRegexp = /http(s*):\/\/.*/i;
var isUrl = (value) => !value || UrlRegexp.test(value);
var valuesAreUrl = (values) => values.every(isUrl);
var ImageUrlRegexp = /http(s*):\/\/.*\.(jpeg|jpg|jfif|pjpeg|pjp|png|svg|gif|webp|apng|bmp|ico|cur|tif|tiff)/i;
var isImageUrl = (value) => !value || ImageUrlRegexp.test(value);
var valuesAreImageUrl = (values) => values.every(isImageUrl);
var EmailRegexp = /@{1}/;
var isEmail = (value) => !value || EmailRegexp.test(value);
var valuesAreEmail = (values) => values.every(isEmail);
var isArray = (value) => Array.isArray(value);
var valuesAreArray = (values) => values.every(isArray);
var isDate = (value) => !value || value instanceof Date;
var valuesAreDate = (values) => values.every(isDate);
var isDateString = (value) => !value || typeof value === "string" && (isMatch(value, "MM/dd/yyyy") || isMatch(value, "MM/dd/yy") || isValid(parseISO(value)));
var valuesAreDateString = (values) => values.every(isDateString);
var isObject2 = (value) => Object.prototype.toString.call(value) === "[object Object]";
var valuesAreObject = (values) => values.every(isObject2);

// node_modules/ra-core/src/inference/inferElementFromValues.tsx
var import_inflection3 = __toESM(require_inflection());
var import_jsx_runtime52 = __toESM(require_jsx_runtime());
var DefaultComponent = () => (0, import_jsx_runtime52.jsx)("span", { children: ";" });
var defaultType = {
  type: DefaultComponent,
  representation: () => "<DefaultComponent />"
};
var defaultTypes = {
  array: defaultType,
  boolean: defaultType,
  date: defaultType,
  email: defaultType,
  id: defaultType,
  number: defaultType,
  reference: defaultType,
  referenceArray: defaultType,
  richText: defaultType,
  string: defaultType,
  url: defaultType
};
var hasType = (type, types) => typeof types[type] !== "undefined";
var inferElementFromValues = (name, values = [], types = defaultTypes) => {
  if (name === "id" && hasType("id", types)) {
    return new InferredElement_default(types.id, { source: name });
  }
  if (name.substr(name.length - 3) === "_id" && hasType("reference", types)) {
    const reference = (0, import_inflection3.pluralize)(name.substr(0, name.length - 3));
    return types.reference && new InferredElement_default(types.reference, {
      source: name,
      reference
    });
  }
  if (name.substr(name.length - 2) === "Id" && hasType("reference", types)) {
    const reference = (0, import_inflection3.pluralize)(name.substr(0, name.length - 2));
    return types.reference && new InferredElement_default(types.reference, {
      source: name,
      reference
    });
  }
  if (name.substr(name.length - 4) === "_ids" && hasType("referenceArray", types)) {
    const reference = (0, import_inflection3.pluralize)(name.substr(0, name.length - 4));
    return types.referenceArray && new InferredElement_default(types.referenceArray, {
      source: name,
      reference
    });
  }
  if (name.substr(name.length - 3) === "Ids" && hasType("referenceArray", types)) {
    const reference = (0, import_inflection3.pluralize)(name.substr(0, name.length - 3));
    return types.referenceArray && new InferredElement_default(types.referenceArray, {
      source: name,
      reference
    });
  }
  if (values.length === 0) {
    return new InferredElement_default(types.string, { source: name });
  }
  if (valuesAreArray(values)) {
    if (isObject2(values[0][0]) && hasType("array", types)) {
      const leafValues = getValuesFromRecords_default(
        values.reduce((acc, vals) => acc.concat(vals), [])
      );
      return types.array && new InferredElement_default(
        types.array,
        {
          source: name
        },
        Object.keys(leafValues).map(
          (leafName) => inferElementFromValues(
            leafName,
            leafValues[leafName],
            types
          )
        )
      );
    }
    return new InferredElement_default(types.string, { source: name });
  }
  if (valuesAreBoolean(values) && hasType("boolean", types)) {
    return new InferredElement_default(types.boolean, { source: name });
  }
  if (valuesAreDate(values) && hasType("date", types)) {
    return new InferredElement_default(types.date, { source: name });
  }
  if (valuesAreString(values)) {
    if (name === "email" && hasType("email", types)) {
      return new InferredElement_default(types.email, { source: name });
    }
    if (name === "url" && hasType("url", types)) {
      return new InferredElement_default(types.url, { source: name });
    }
    if (valuesAreDateString(values) && hasType("date", types)) {
      return new InferredElement_default(types.date, { source: name });
    }
    if (valuesAreHtml(values) && hasType("richText", types)) {
      return new InferredElement_default(types.richText, { source: name });
    }
    return new InferredElement_default(types.string, { source: name });
  }
  if ((valuesAreInteger(values) || valuesAreNumeric(values)) && hasType("number", types)) {
    return new InferredElement_default(types.number, { source: name });
  }
  if (valuesAreObject(values)) {
    const propName = Object.keys(values[0]).shift();
    if (!propName) {
      return new InferredElement_default(types.string, { source: name });
    }
    const leafValues = values.map((v) => v[propName]);
    return inferElementFromValues(`${name}.${propName}`, leafValues, types);
  }
  return new InferredElement_default(types.string, { source: name });
};
var inferElementFromValues_default = inferElementFromValues;

// node_modules/ra-core/src/inference/getElementsFromRecords.ts
var getElementsFromRecords_default = (records, types) => {
  const fieldValues = getValuesFromRecords_default(records);
  return Object.keys(fieldValues).reduce(
    (fields, fieldName) => fields.concat(
      inferElementFromValues_default(
        fieldName,
        fieldValues[fieldName],
        types
      )
    ),
    []
  ).filter((inferredElement) => inferredElement.isDefined());
};

// node_modules/ra-core/src/inference/inferTypeFromValues.ts
var import_inflection4 = __toESM(require_inflection());
var InferenceTypes = [
  "array",
  "boolean",
  "date",
  "email",
  "id",
  "image",
  "number",
  "reference",
  "referenceChild",
  "referenceArray",
  "referenceArrayChild",
  "richText",
  "string",
  "url",
  "object"
];
var inferTypeFromValues = (name, values = []) => {
  if (name === "id") {
    return { type: "id", props: { source: name } };
  }
  if (name.substr(name.length - 3) === "_id") {
    return {
      type: "reference",
      props: {
        source: name,
        reference: (0, import_inflection4.pluralize)(name.substr(0, name.length - 3))
      },
      children: { type: "referenceChild" }
    };
  }
  if (name.substr(name.length - 2) === "Id") {
    return {
      type: "reference",
      props: {
        source: name,
        reference: (0, import_inflection4.pluralize)(name.substr(0, name.length - 2))
      },
      children: { type: "referenceChild" }
    };
  }
  if (name.substr(name.length - 4) === "_ids") {
    return {
      type: "referenceArray",
      props: {
        source: name,
        reference: (0, import_inflection4.pluralize)(name.substr(0, name.length - 4))
      },
      children: { type: "referenceArrayChild" }
    };
  }
  if (name.substr(name.length - 3) === "Ids") {
    return {
      type: "referenceArray",
      props: {
        source: name,
        reference: (0, import_inflection4.pluralize)(name.substr(0, name.length - 3))
      },
      children: { type: "referenceArrayChild" }
    };
  }
  if (values.length === 0) {
    if (name === "email") {
      return { type: "email", props: { source: name } };
    }
    if (name === "url") {
      return { type: "url", props: { source: name } };
    }
    return { type: "string", props: { source: name } };
  }
  if (valuesAreArray(values)) {
    if (isObject2(values[0][0])) {
      const leafValues = getValuesFromRecords_default(
        values.reduce((acc, vals) => acc.concat(vals), [])
      );
      return {
        type: "array",
        props: { source: name },
        children: Object.keys(leafValues).map(
          (leafName) => inferTypeFromValues(leafName, leafValues[leafName])
        )
      };
    }
    return { type: "string", props: { source: name } };
  }
  if (valuesAreBoolean(values)) {
    return { type: "boolean", props: { source: name } };
  }
  if (valuesAreDate(values)) {
    return { type: "date", props: { source: name } };
  }
  if (valuesAreString(values)) {
    if (name === "email" || valuesAreEmail(values)) {
      return { type: "email", props: { source: name } };
    }
    if (name === "url" || valuesAreUrl(values)) {
      if (valuesAreImageUrl(values)) {
        return { type: "image", props: { source: name } };
      }
      return { type: "url", props: { source: name } };
    }
    if (valuesAreDateString(values)) {
      return { type: "date", props: { source: name } };
    }
    if (valuesAreHtml(values)) {
      return { type: "richText", props: { source: name } };
    }
    if (valuesAreInteger(values) || valuesAreNumeric(values)) {
      return { type: "number", props: { source: name } };
    }
    return { type: "string", props: { source: name } };
  }
  if (valuesAreInteger(values) || valuesAreNumeric(values)) {
    return { type: "number", props: { source: name } };
  }
  if (valuesAreObject(values)) {
    const propName = Object.keys(values[0]).shift();
    if (!propName) {
      return { type: "object", props: { source: name } };
    }
    const leafValues = values.map((v) => v[propName]);
    return inferTypeFromValues(`${name}.${propName}`, leafValues);
  }
  return { type: "string", props: { source: name } };
};

// node_modules/ra-core/src/types.ts
var I18N_TRANSLATE = "I18N_TRANSLATE";
var I18N_CHANGE_LOCALE = "I18N_CHANGE_LOCALE";

export {
  AuthContext,
  useAuthProvider_default,
  localStorageStore,
  getStorage,
  memoryStore,
  StoreContext,
  StoreContextProvider,
  useStoreContext,
  StoreSetter,
  escapePath_default,
  substituteTokens,
  I18nContext,
  AddNotificationContext,
  NotificationContext,
  NotificationContextProvider,
  useAddNotificationContext,
  useNotificationContext,
  useNotify,
  I18nContextProvider,
  TestTranslationProvider,
  testI18nProvider,
  TranslatableContext,
  TranslatableContextProvider,
  resolveBrowserLocale,
  mergeTranslations,
  useI18nProvider,
  useLocaleState,
  useLocale,
  useLocales,
  useSetLocale,
  useTranslatable,
  getRecordForLocale,
  useTranslatableContext,
  useTranslate,
  BasenameContextProvider,
  AdminRouter,
  require_debounce,
  useRestoreScrollPosition,
  useTrackScrollPosition,
  RestoreScrollPosition,
  useBasename,
  useCreatePath,
  removeDoubleSlashes,
  ResourceContext,
  useResourceContext,
  RecordContext,
  RecordContextProvider,
  useRecordContext,
  useGetPathForRecord,
  ResourceDefinitionContext,
  ResourceDefinitionContextProvider,
  useResourceDefinitionContext,
  useResourceDefinitions,
  useCanAccessCallback,
  useGetPathForRecordCallback,
  useRedirect,
  useResetErrorBoundaryOnLocationChange,
  useScrollToTop,
  TestMemoryRouter,
  useSplatPathBase,
  GET_LIST,
  GET_ONE,
  GET_MANY,
  GET_MANY_REFERENCE,
  CREATE,
  UPDATE,
  UPDATE_MANY,
  DELETE,
  DELETE_MANY,
  fetchActionsWithRecordResponse,
  fetchActionsWithArrayOfIdentifiedRecordsResponse,
  fetchActionsWithArrayOfRecordsResponse,
  fetchActionsWithTotalResponse,
  reactAdminFetchActions,
  sanitizeFetchType,
  defaultDataProvider,
  convertLegacyDataProvider_default,
  DataProviderContext_default,
  HttpError_default,
  require_query_string,
  fetch_exports,
  undoableEventEmitter_default,
  combineDataProviders,
  testDataProvider,
  withLifecycleCallbacks,
  applyCallbacks,
  useLogoutIfAccessDenied_default,
  useDataProvider,
  useIsDataLoaded,
  useLoading,
  useRefresh,
  useGetOne,
  useGetList,
  useGetMany,
  require_union,
  useGetManyAggregate,
  useGetManyReference,
  useFilterState_default,
  shallowEqual,
  removeEmpty_default,
  removeKey_default,
  SET_SORT,
  SORT_ASC,
  SORT_DESC,
  SET_PAGE,
  SET_PER_PAGE,
  SET_FILTER,
  SHOW_FILTER,
  HIDE_FILTER,
  queryReducer,
  useSortState_default,
  usePaginationState_default,
  useCheckMinimumRequiredProps,
  useDeleteWithUndoController_default,
  useDeleteWithConfirmController_default,
  SaveContext,
  SaveContextProvider,
  usePickSaveContext,
  useSaveContext,
  useMutationMiddlewares,
  useRegisterMutationMiddleware,
  useCreateController,
  CreateContext,
  CreateContextProvider,
  CreateBase,
  CreateController,
  useCreateContext,
  useEditController,
  WithRecord,
  OptionalRecordContextProvider,
  RecordRepresentation,
  EditContext,
  EditContextProvider,
  EditBase,
  EditController,
  useEditContext,
  ReferenceFieldContext,
  ReferenceFieldContextProvider,
  useReferenceFieldContext,
  useReference,
  useReferenceFieldController,
  ReferenceFieldBase,
  downloadCSV,
  defaultExporter,
  ExporterContext,
  fetchRelatedRecords,
  useRecordSelection,
  useSafeSetState,
  usePrevious,
  useDeepCompareEffect,
  useTimeout,
  useIsMounted,
  useListParams,
  parseQueryFromLocation,
  hasCustomParams,
  getQuery,
  getNumberOrDefault,
  useSelectAll,
  useInfiniteListController,
  ListContext,
  ListFilterContext,
  usePickFilterContext,
  ListSortContext,
  usePickSortContext,
  ListPaginationContext,
  usePickPaginationContext,
  ListContextProvider,
  InfinitePaginationContext,
  InfiniteListBase,
  useListController,
  injectedProps,
  getListControllerProps,
  sanitizeListRestProps,
  ListBase,
  ListController,
  useExpanded,
  useExpandAll,
  useInfinitePaginationContext,
  useList,
  useListContext,
  require_defaults,
  useListContextWithProps,
  useListFilterContext,
  useListPaginationContext,
  useListSortContext,
  useUnselect,
  useUnselectAll,
  WithListContext,
  useReferenceArrayFieldController,
  useReferenceManyFieldController,
  useReferenceOneFieldController,
  getStatusForInput,
  getSelectedReferencesStatus,
  getStatusForArrayInput,
  useReferenceArrayInputController,
  useReferenceInputController,
  ChoicesContext,
  ChoicesContextProvider,
  useChoicesContext,
  useChoices,
  FormGroupsProvider,
  useWarnWhenUnsavedChanges,
  WarnWhenUnsavedChanges,
  getSimpleValidationResolver,
  setSubmissionErrors,
  useNotifyIsFormInvalid,
  useRecordFromLocation,
  getRecordFromLocation,
  useAugmentedForm,
  Form,
  FormDataConsumer,
  FormDataConsumerView,
  FormGroupContext,
  useFormGroups,
  FormGroupContextProvider,
  useFormGroup,
  getFormGroupState,
  useFormGroupContext,
  useApplyInputDefaultValues,
  useGetValidationErrorMessage,
  isEmpty2 as isEmpty,
  combine2Validators,
  composeValidators,
  composeSyncValidators,
  required,
  minLength,
  maxLength,
  minValue,
  maxValue,
  number,
  regex,
  email,
  choices,
  isRequired,
  useUnique,
  ValidationError,
  useInput,
  useSuggestions,
  getSuggestionsFactory,
  FilterLiveForm,
  getFilterFormValues,
  ReferenceInputBase,
  useShowController,
  ShowContext,
  ShowContextProvider,
  ShowBase,
  ShowController,
  useShowContext,
  usePrevNextController,
  useGetRecordId,
  useCreate,
  AddUndoableMutationContext,
  useAddUndoableMutation,
  useUpdate,
  useUpdateMany,
  useDelete,
  useDeleteMany,
  useInfiniteGetList,
  TakeUndoableMutationContext,
  UndoableMutationsContextProvider,
  useTakeUndoableMutation,
  PreferencesEditorContext,
  PreferencesEditorContextProvider,
  CoreAdminContext,
  usePermissions_default,
  HasDashboardContext,
  HasDashboardContextProvider,
  useHasDashboard,
  useCanAccessResources,
  useFirstResourceWithListAccess,
  NavigateToFirstResource,
  CoreAdminRoutes,
  DefaultTitleContext,
  DefaultTitleContextProvider,
  useDefaultTitle,
  CoreAdminUI,
  CoreAdmin,
  CustomRoutes,
  ResourceContextProvider,
  OptionalResourceContextProvider,
  Resource,
  SourceContext,
  SourceContextProvider,
  useSourceContext,
  useOptionalSourceContext,
  require_inflection,
  useGetResourceLabel,
  useResourceDefinition,
  useGetRecordRepresentation,
  useWrappedSource,
  useTranslateLabel,
  DEFAULT_LOCALE,
  FieldTitle_default,
  Ready_default,
  warning_default,
  useWhyDidYouUpdate,
  getMutationMode,
  getFieldLabelTranslationArgs,
  getResourceFieldLabelKey,
  mergeRefs,
  useEvent,
  useDebouncedEvent,
  useFieldValue,
  asyncDebounce,
  useCheckForApplicationUpdate,
  useStore,
  useRemoveFromStore,
  useRemoveItemsFromStore,
  useResetStore,
  useLogout_default,
  useAuthState_default,
  useAuthenticated,
  WithPermissions_default,
  useLogin_default,
  useGetPermissions_default,
  AUTH_LOGIN,
  AUTH_CHECK,
  AUTH_ERROR,
  AUTH_LOGOUT,
  AUTH_GET_PERMISSIONS,
  convertLegacyAuthProvider_default,
  Authenticated,
  useCanAccess,
  CanAccess,
  LogoutOnMount,
  useCheckAuth,
  useGetIdentity,
  useHandleAuthCallback,
  PreviousLocationStorageKey,
  useIsAuthPending,
  useRequireAccess,
  addRefreshAuthToAuthProvider,
  addRefreshAuthToDataProvider,
  PreferenceKeyContext,
  PreferenceKeyContextProvider,
  usePreferenceKey,
  usePreference,
  usePreferencesEditor,
  usePreferenceInput,
  useSetInspectorTitle,
  getValuesFromRecords_default,
  InferredElement_default,
  getElementsFromRecords_default,
  InferenceTypes,
  inferTypeFromValues,
  I18N_TRANSLATE,
  I18N_CHANGE_LOCALE
};
/*! Bundled license information:

inflection/lib/inflection.js:
  (*!
   * inflection
   * Copyright(c) 2011 Ben Lin <<EMAIL>>
   * MIT Licensed
   *
   * @fileoverview
   * A port of inflection-js to node.js module.
   *)
*/
//# sourceMappingURL=chunk-FK6MK4AY.js.map
